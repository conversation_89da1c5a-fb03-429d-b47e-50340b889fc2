window.ClassSystem={isInitialized:!1,elements:{},selectedClass:null,classes:[{id:"warrior",name:"<PERSON>"},{id:"wizard",name:"<PERSON>"},{id:"blader",name:"<PERSON><PERSON>"},{id:"dark-mage",name:"<PERSON> Mage"},{id:"gladiator",name:"Gladiator"},{id:"force-gunner",name:"<PERSON> Gunner"},{id:"force-blader",name:"Force Blader"},{id:"force-archer",name:"<PERSON> Archer"},{id:"force-shielder",name:"Force Shielder"}],baseStats:{level:200,baseStr:10,baseInt:10,baseDex:10,totalStatPoints:1379,currentStr:10,currentInt:10,currentDex:10,remainingPoints:1379},statRanges:[{min:0,max:100},{min:101,max:1e3},{min:1001,max:2e3},{min:2001,max:999999}],classScaling:{warrior:{str:{hp:[1.2,1,.8,.5],attack:[.8,.6,.4,.2],damageReduce:[.25,.2,.15,.1],ignorePenetration:[.3,.25,.2,.15]},int:{magicAttack:[.3,.25,.2,.15],resistCritRate:[.06,.05,.04,.03],resistCritDmg:[.12,.1,.08,.06],resistSkillAmp:[.06,.05,.04,.03],resistKnockback:[.06,.05,.04,.03],resistStun:[.06,.05,.04,.03]},dex:{attackRate:[.5,.4,.3,.2],defenseRate:[.3,.25,.2,.15],evasion:[.5,.4,.3,.2],resistDown:[.06,.05,.04,.03],resistUnableToMove:[.06,.05,.04,.03]}},wizard:{str:{hp:[.5,.4,.3,.2],attack:[.3,.25,.2,.15],damageReduce:[.15,.12,.1,.08],ignorePenetration:[.2,.15,.12,.1]},int:{magicAttack:[1.2,1,.8,.6],resistCritRate:[.12,.1,.08,.06],resistCritDmg:[.2,.15,.12,.1],resistSkillAmp:[.12,.1,.08,.06],resistKnockback:[.08,.06,.05,.04],resistStun:[.08,.06,.05,.04]},dex:{attackRate:[.4,.3,.25,.2],defenseRate:[.3,.25,.2,.15],evasion:[.4,.3,.25,.2],resistDown:[.08,.06,.05,.04],resistUnableToMove:[.08,.06,.05,.04]}},default:{str:{hp:[1,.8,.6,.4],attack:[.6,.5,.4,.3],damageReduce:[.2,.15,.12,.1],ignorePenetration:[.25,.2,.15,.1]},int:{magicAttack:[.6,.5,.4,.3],resistCritRate:[.1,.08,.06,.04],resistCritDmg:[.15,.12,.1,.08],resistSkillAmp:[.1,.08,.06,.04],resistKnockback:[.07,.06,.05,.04],resistStun:[.07,.06,.05,.04]},dex:{attackRate:[.45,.35,.25,.15],defenseRate:[.3,.25,.2,.15],evasion:[.45,.35,.25,.15],resistDown:[.07,.06,.05,.04],resistUnableToMove:[.07,.06,.05,.04]}}},init:function(){if(this.isInitialized)return;this.populateDefaultClassScaling();const e=document.getElementById("fg-class-system");e&&(this.elements.panel=e,this.initUI(),this.loadFromStore(),!this.selectedClass&&this.classes.length>0&&(this.selectedClass=this.classes[0].id,this.updateClassSelection()),this.updateDerivedStats(),this.setupEventListeners(),this.isInitialized=!0)},populateDefaultClassScaling:function(){this.classes.forEach((e=>{this.classScaling[e.id]||(this.classScaling[e.id]=JSON.parse(JSON.stringify(this.classScaling.default)))}))},initUI:function(){this.createClassSystemUI()},createClassSystemUI:function(){if(!this.elements.panel)return;const e=document.createElement("div");e.className="fg-class-system-container";const t=document.createElement("div");t.className="fg-class-selection-section";const n=document.createElement("h3");n.className="fg-section-header",n.textContent="Select Your Class",t.appendChild(n);const s=document.createElement("div");s.className="fg-class-grid",this.classes.forEach((e=>{const t=document.createElement("div");t.className="fg-class-option",t.dataset.classId=e.id;const n=document.createElement("div");let a;switch(n.className="fg-class-icon",e.id){case"warrior":a="warrior_icon.png";break;case"wizard":a="wizard_icon.png";break;case"blader":a="bkader_icon.png";break;case"dark-mage":a="dark_mage_icon.png";break;case"gladiator":a="gladiator_icon.png";break;case"force-gunner":a="force_gunner_icon.png";break;case"force-blader":a="force_blader_icon.png";break;case"force-archer":a="force_archer_icon.png";break;case"force-shielder":a="force_shielder_icon.png";break;default:a=""}n.innerHTML=a?`<img src="${StatsConfig.getPluginUrl()}assets/images/class-icons/${a}" alt="${e.name}" class="fg-class-icon-img">`:'<i class="fg-icon-placeholder"></i>';const i=document.createElement("div");i.className="fg-class-name",i.textContent=e.name,t.appendChild(n),t.appendChild(i),s.appendChild(t)})),t.appendChild(s),e.appendChild(t);const a=document.createElement("div");a.className="fg-attribute-section";const i=document.createElement("h3");i.className="fg-section-header",i.textContent="Distribute Attribute Points",a.appendChild(i);const l=document.createElement("div");l.className="fg-points-info",l.innerHTML='\n            <p>Character Level: <span class="fg-level">200</span></p>\n            <p>Total Points: <span class="fg-total-points">1379</span></p>\n            <p>Remaining Points: <span class="fg-remaining-points">1379</span></p>\n        ',a.appendChild(l);const o=document.createElement("div");o.className="fg-attributes-container",["str","int","dex"].forEach((e=>{const t=document.createElement("div");t.className="fg-attribute-control";const n=document.createElement("div");n.className="fg-attribute-label";const s=document.createElement("div");s.className="fg-attribute-icon",StatsConfig&&StatsConfig.stats[e]&&StatsConfig.stats[e].icon?s.innerHTML=`<img src="${StatsConfig.getStatIconUrl(e)}" alt="${e.toUpperCase()}">`:s.innerHTML=`<span>${e.toUpperCase()}</span>`;const a=document.createElement("span");a.textContent=e.toUpperCase(),n.appendChild(s),n.appendChild(a);const i=document.createElement("div");i.className="fg-attribute-value-control";const l=document.createElement("button");l.className="fg-attribute-btn fg-decrement",l.textContent="-",l.dataset.attr=e;const r=document.createElement("span");r.className=`fg-attribute-value fg-${e}-value`,r.textContent="10";const c=document.createElement("button");c.className="fg-attribute-btn fg-increment",c.textContent="+",c.dataset.attr=e;const d=document.createElement("button");d.className="fg-attribute-btn fg-increment-10",d.textContent="+10",d.dataset.attr=e;const u=document.createElement("button");u.className="fg-attribute-btn fg-increment-100",u.textContent="+100",u.dataset.attr=e,i.appendChild(l),i.appendChild(r),i.appendChild(c),i.appendChild(d),i.appendChild(u);const h=document.createElement("div");h.className="fg-attribute-tooltip-trigger",h.dataset.attr=e,h.innerHTML='<i class="fg-icon-info">?</i>',t.appendChild(n),t.appendChild(i),t.appendChild(h),o.appendChild(t)})),a.appendChild(o),e.appendChild(a);const r=document.createElement("div");r.className="fg-derived-stats-section";const c=document.createElement("h3");c.className="fg-section-header",c.textContent="Derived Stats",r.appendChild(c);const d=document.createElement("div");d.className="fg-derived-stats-container";const u=document.createElement("div");u.className="fg-derived-stats-group",u.innerHTML='\n            <h4 class="fg-stats-group-header">STR-based Stats</h4>\n            <div class="fg-derived-stats fg-str-derived-stats"></div>\n        ';const h=document.createElement("div");h.className="fg-derived-stats-group",h.innerHTML='\n            <h4 class="fg-stats-group-header">INT-based Stats</h4>\n            <div class="fg-derived-stats fg-int-derived-stats"></div>\n        ';const f=document.createElement("div");f.className="fg-derived-stats-group",f.innerHTML='\n            <h4 class="fg-stats-group-header">DEX-based Stats</h4>\n            <div class="fg-derived-stats fg-dex-derived-stats"></div>\n        ',d.appendChild(u),d.appendChild(h),d.appendChild(f),r.appendChild(d),e.appendChild(r);const m=document.createElement("div");m.className="fg-attribute-tooltip",m.style.display="none",e.appendChild(m),this.elements.panel.innerHTML="",this.elements.panel.appendChild(e),this.elements.classOptions=this.elements.panel.querySelectorAll(".fg-class-option"),this.elements.remainingPoints=this.elements.panel.querySelector(".fg-remaining-points"),this.elements.strValue=this.elements.panel.querySelector(".fg-str-value"),this.elements.intValue=this.elements.panel.querySelector(".fg-int-value"),this.elements.dexValue=this.elements.panel.querySelector(".fg-dex-value"),this.elements.strDerivedStats=this.elements.panel.querySelector(".fg-str-derived-stats"),this.elements.intDerivedStats=this.elements.panel.querySelector(".fg-int-derived-stats"),this.elements.dexDerivedStats=this.elements.panel.querySelector(".fg-dex-derived-stats"),this.elements.tooltipContainer=this.elements.panel.querySelector(".fg-attribute-tooltip"),"undefined"!=typeof QuickFillButtonConfigs&&(this.quickFillManager=QuickFillButtonConfigs.initializeSystem("class",this.elements.panel.querySelector(".fg-class-system-container"))),!this.elements.strDerivedStats||!this.elements.intDerivedStats||this.elements.dexDerivedStats},setupEventListeners:function(){this.elements.panel&&(this.elements.classOptions.forEach((e=>{e.addEventListener("click",(e=>{const t=e.currentTarget.dataset.classId;this.selectClass(t)}))})),this.elements.panel.querySelectorAll(".fg-attribute-btn").forEach((e=>{e.addEventListener("click",(e=>{const t=e.currentTarget.dataset.attr;let n=1;e.currentTarget.classList.contains("fg-decrement")?n=-1:e.currentTarget.classList.contains("fg-increment-10")?n=10:e.currentTarget.classList.contains("fg-increment-100")&&(n=100),this.adjustAttribute(t,n)}))})),this.elements.panel.querySelectorAll(".fg-attribute-tooltip-trigger").forEach((e=>{e.addEventListener("mouseenter",(e=>{const t=e.currentTarget.dataset.attr;this.showTooltip(t,e)})),e.addEventListener("mouseleave",(()=>{this.hideTooltip()}))})))},getEssentialData:function(){return{selectedClass:this.selectedClass,attributes:{str:this.baseStats.currentStr,int:this.baseStats.currentInt,dex:this.baseStats.currentDex}}},loadFromStore:function(){if("undefined"==typeof BuildSaverStore)return;const e=BuildSaverStore.getSystemData("class");e&&(e.selectedClass&&(this.selectedClass=e.selectedClass,this.updateClassSelection()),e.attributes&&(this.baseStats.currentStr=e.attributes.str||this.baseStats.baseStr,this.baseStats.currentInt=e.attributes.int||this.baseStats.baseInt,this.baseStats.currentDex=e.attributes.dex||this.baseStats.baseDex,this.baseStats.remainingPoints=this.baseStats.totalStatPoints-(this.baseStats.currentStr-this.baseStats.baseStr)-(this.baseStats.currentInt-this.baseStats.baseInt)-(this.baseStats.currentDex-this.baseStats.baseDex),this.updateAttributeValues()),this.updateDerivedStats())},saveToStore:function(){return"undefined"!=typeof BuildSaverStore},selectClass:function(e){this.classes.find((t=>t.id===e))&&(this.selectedClass=e,this.updateClassSelection(),this.updateDerivedStats(),this.saveToStore())},updateClassSelection:function(){if(this.elements.classOptions&&(this.elements.classOptions.forEach((e=>{e.classList.remove("selected")})),this.selectedClass)){const e=Array.from(this.elements.classOptions).find((e=>e.dataset.classId===this.selectedClass));e&&e.classList.add("selected")}},adjustAttribute:function(e,t){if(!["str","int","dex"].includes(e))return;if(t>0&&this.baseStats.remainingPoints<t)return;const n=this.baseStats[`current${e.charAt(0).toUpperCase()+e.slice(1)}`]+t;t<0&&n<this.baseStats[`base${e.charAt(0).toUpperCase()+e.slice(1)}`]||(this.baseStats[`current${e.charAt(0).toUpperCase()+e.slice(1)}`]=n,this.baseStats.remainingPoints-=t,this.updateAttributeValues(),this.updateDerivedStats(),this.saveToStore())},resetAttributes:function(){this.baseStats.currentStr=this.baseStats.baseStr,this.baseStats.currentInt=this.baseStats.baseInt,this.baseStats.currentDex=this.baseStats.baseDex,this.baseStats.remainingPoints=this.baseStats.totalStatPoints,this.updateAttributeValues(),this.updateDerivedStats(),this.saveToStore()},updateAttributeValues:function(){this.elements.strValue&&(this.elements.strValue.textContent=this.baseStats.currentStr,this.elements.intValue.textContent=this.baseStats.currentInt,this.elements.dexValue.textContent=this.baseStats.currentDex,this.elements.remainingPoints.textContent=this.baseStats.remainingPoints)},updateDerivedStats:function(){if(!this.selectedClass)return;const e=this.classScaling[this.selectedClass],t={};Object.keys(e.str).forEach((e=>{t[e]=this.calculateStatValue("str",e)})),Object.keys(e.int).forEach((e=>{t[e]=this.calculateStatValue("int",e)})),Object.keys(e.dex).forEach((e=>{t[e]=this.calculateStatValue("dex",e)})),this.updateDerivedStatsUI(t),this.updateGlobalStats(t)},calculateStatValue:function(e,t){const n=this.baseStats[`current${e.charAt(0).toUpperCase()+e.slice(1)}`],s=this.classScaling[this.selectedClass][e][t];let a=0;return this.statRanges.forEach(((e,t)=>{if(n<=e.max){const i=Math.min(n,e.max)-Math.max(e.min,0);i>0&&(a+=i*s[t])}else{const n=e.max-Math.max(e.min,0);n>0&&(a+=n*s[t])}})),StatsConfig.stats[t]&&StatsConfig.stats[t].isPercentage?Math.round(100*a)/100:Math.floor(a)},updateDerivedStatsUI:function(e){this.elements.strDerivedStats&&(this.elements.strDerivedStats.innerHTML="",this.elements.intDerivedStats.innerHTML="",this.elements.dexDerivedStats.innerHTML="",Object.keys(this.classScaling[this.selectedClass].str).forEach((t=>{const n=e[t],s=this.createStatElement(t,n);this.elements.strDerivedStats.appendChild(s)})),Object.keys(this.classScaling[this.selectedClass].int).forEach((t=>{const n=e[t],s=this.createStatElement(t,n);this.elements.intDerivedStats.appendChild(s)})),Object.keys(this.classScaling[this.selectedClass].dex).forEach((t=>{const n=e[t],s=this.createStatElement(t,n);this.elements.dexDerivedStats.appendChild(s)})))},updateGlobalStats:function(e){"undefined"!=typeof BuildPlanner&&(e.str=this.baseStats.currentStr,e.int=this.baseStats.currentInt,e.dex=this.baseStats.currentDex,BuildPlanner.updateStats("class",e))},showTooltip:function(e,t){if(!this.selectedClass)return;const n=this.elements.tooltipContainer,s=this.baseStats[`current${e.charAt(0).toUpperCase()+e.slice(1)}`];let a=`<h4>${e.toUpperCase()} Stats Breakdown</h4>`;a+=`<p>Your ${e.toUpperCase()} value: ${s}</p>`,a+='<div class="fg-tooltip-stats">',Object.keys(this.classScaling[this.selectedClass][e]).forEach((t=>{let n=t;StatsConfig.stats[t]&&(n=StatsConfig.stats[t].name);const s=this.calculateStatValue(e,t),i=StatsConfig.stats[t]&&StatsConfig.stats[t].isPercentage?`${s}%`:s.toLocaleString();a+=`\n                <div class="fg-tooltip-stat">\n                    <span class="fg-tooltip-stat-name">${n}</span>\n                    <span class="fg-tooltip-stat-value">${i}</span>\n                </div>\n            `})),a+="</div>",n.innerHTML=a,n.style.display="block";const i=t.target.getBoundingClientRect(),l=this.elements.panel.getBoundingClientRect();n.style.left=i.right-l.left+10+"px",n.style.top=i.top-l.top-10+"px"},hideTooltip:function(){this.elements.tooltipContainer&&(this.elements.tooltipContainer.style.display="none")},createStatElement:function(e,t){const n=document.createElement("div");n.className="fg-derived-stat";let s="";StatsConfig.stats[e]&&StatsConfig.stats[e].icon&&(s=`<img src="${StatsConfig.getStatIconUrl(e)}" alt="${e}">`);let a=e;StatsConfig.stats[e]&&(a=StatsConfig.stats[e].name);const i=StatsConfig.stats[e]&&StatsConfig.stats[e].isPercentage?`${t}%`:t.toLocaleString();return n.innerHTML=`\n            <div class="fg-derived-stat-icon">${s}</div>\n            <div class="fg-derived-stat-info">\n                <span class="fg-derived-stat-name">${a}</span>\n                <span class="fg-derived-stat-value">${i}</span>\n            </div>\n        `,n}},window.PetSystem={isInitialized:!1,elements:{},activeTier:"normal",selectedStats:{normal:Array(10).fill(null),covenant:Array(10).fill(null),trust:Array(10).fill(null)},currentSlot:null,selectionWindow:null,init:function(){if(this.isInitialized)return;const e=document.getElementById("fg-pet-system");e&&(this.elements.panel=e,this.initUI(),this.selectionWindow=new SelectionWindowManager({id:"fg-pet-stat-selector",title:"Select Pet Attribute",className:"fg-pet-stat-selector",fixedPosition:!0,onSelect:e=>{const t=e.statId||e.statid;t&&this.selectStat(t)},onClose:()=>{this.currentSlot=null}}),this.loadFromStore(),this.setupEventListeners(),this.activeTier="normal",this.isInitialized=!0)},initUI:function(){this.createPetSystemUI()},createPetSystemUI:function(){if(!this.elements.panel)return;const e=`\n            <div class="fg-pet-system-container">\n                <h2>Pet System</h2>\n\n                \x3c!-- All tier content stacked vertically --\x3e\n                <div class="fg-pet-tier-content">\n                    \x3c!-- Normal tier (Lv. 1-10) --\x3e\n                    <div id="normal-tier" class="fg-pet-tier-panel">\n                        <span class="fg-pet-tier-header">Normal (Level 1-10)</span>\n                        <div class="fg-pet-slots-container" id="normal-slots">\n                            ${this.createSlotHTML("normal",1,10)}\n                        </div>\n                    </div>\n\n                    \x3c!-- Covenant tier (Lv. 11-20) --\x3e\n                    <div id="covenant-tier" class="fg-pet-tier-panel">\n                        <span class="fg-pet-tier-header">Covenant (Level 11-20)</span>\n                        <div class="fg-pet-slots-container" id="covenant-slots">\n                            ${this.createSlotHTML("covenant",11,20)}\n                        </div>\n                    </div>\n\n                    \x3c!-- Trust tier (Lv. 21-30) --\x3e\n                    <div id="trust-tier" class="fg-pet-tier-panel">\n                        <span class="fg-pet-tier-header">Trust (Level 21-30)</span>\n                        <div class="fg-pet-slots-container" id="trust-slots">\n                            ${this.createSlotHTML("trust",21,30)}\n                        </div>\n                    </div>\n                </div>\n\n                \x3c!-- Selected stats summary --\x3e\n                <div class="fg-pet-selected-stats">\n                    <h3>Selected Pet Stats</h3>\n                    <div class="fg-pet-selected-list">\n                        \x3c!-- Selected stats will be shown here --\x3e\n                    </div>\n                </div>\n            </div>\n        `;this.elements.panel.innerHTML=e,this.elements.selectedList=this.elements.panel.querySelector(".fg-pet-selected-list"),this.elements.normalSlots=this.elements.panel.querySelector("#normal-slots"),this.elements.covenantSlots=this.elements.panel.querySelector("#covenant-slots"),this.elements.trustSlots=this.elements.panel.querySelector("#trust-slots"),"undefined"!=typeof QuickFillButtonConfigs&&(this.quickFillManager=QuickFillButtonConfigs.initializeSystem("pet",this.elements.panel.querySelector(".fg-pet-system-container"))),setTimeout((()=>{this.setupSlotClickHandlers()}),0)},fillTierWithStat:function(e,t){const n=PetSystemData.tierStats[e].find((e=>e.id===t));if(n)for(let s=0;s<10;s++)this.selectedStats[e][s]={id:t,value:n.value},this.updateSlotUI(e,s)},quickFillEndgameSetup:function(){this.fillTierWithStat("normal","maxCritRate"),this.fillTierWithStat("covenant","penetration"),this.fillTierWithStat("trust","penetration"),this.updateSelectedStatsDisplay(),this.updatePetStats(),this.saveToStore()},createSlotHTML:function(e,t,n){let s="";for(let n=0;n<10;n++){const a=t+n,i=n,l=null!==this.selectedStats[e][i];if(s+='<div class="fg-pet-slot-wrapper">',s+=`\n                <div class="fg-pet-slot ${l?"selected":"empty"}"\n                     data-tier="${e}"\n                     data-slot="${i}"\n                     data-level="${a}">\n                    <div class="fg-pet-slot-level">${a}</div>\n                    <div class="fg-pet-slot-content">\n                        ${this.getSlotContentHTML(e,i)}\n                    </div>\n                </div>\n            `,l){const t=this.selectedStats[e][i],n=PetSystemData.tierStats[e].find((e=>e.id===t.id));if(n){const e=this.getStatSuffix(t.id);s+=`\n                        <div class="fg-pet-slot-stat-value-below">\n                            +${n.value}${e}\n                        </div>\n                    `}else s+='<div class="fg-pet-slot-stat-value-below"></div>'}else s+='<div class="fg-pet-slot-stat-value-below"></div>';s+="</div>"}return s},getSlotContentHTML:function(e,t){const n=this.selectedStats[e][t],s='<div class="fg-pet-slot-empty"></div>';if(!n)return s;const a=PetSystemData.tierStats[e].find((e=>e.id===n.id));if(!a)return s;let i=n.id,l="";if("undefined"!=typeof StatsConfig){const e=StatsConfig.getStatInfo(n.id);e&&(i=e.name);try{l=`<div class="fg-pet-slot-stat-icon"><img src="${StatsConfig.getStatIconUrl(n.id)}" alt="${i}" onerror="this.onerror=null; this.style.display='none';"></div>`}catch(e){}}return`\n            <div class="fg-pet-slot-stat">\n                <button class="fg-pet-slot-remove" data-tier="${e}" data-slot="${t}" title="Remove attribute">&times;</button>\n                ${l}\n                <div class="fg-pet-slot-stat-name" style="display:none;">${i}</div>\n                <div class="fg-pet-slot-stat-value" style="display:none;">+${a.value}${this.getStatSuffix(n.id)}</div>\n            </div>\n        `},showStatSelectionPopup:function(e,t,n){this.currentSlot={tier:e,slotIndex:t,level:n};const s=[...PetSystemData.tierStats[e]].sort(((e,t)=>{let n=e.id,s=t.id;if("undefined"!=typeof StatsConfig){const a=StatsConfig.getStatInfo(e.id),i=StatsConfig.getStatInfo(t.id);a&&(n=a.name),i&&(s=i.name)}return n.localeCompare(s)})).map((e=>{let t=e.id;if("undefined"!=typeof StatsConfig){const n=StatsConfig.getStatInfo(e.id);n&&(t=n.name)}let n="";if("undefined"!=typeof StatsConfig)try{n=`<img src="${StatsConfig.getStatIconUrl(e.id)}" alt="${t}" class="fg-selection-option-icon" onerror="this.onerror=null; this.style.display='none';">`}catch(e){}return{html:`\n                    <div class="fg-selection-option-with-icon">\n                        ${n}\n                        <span class="fg-selection-option-name">${t}</span>\n                    </div>\n                    <span class="fg-selection-option-value">+${e.value}${this.getStatSuffix(e.id)}</span>\n                `,data:{statId:e.id}}}));this.selectionWindow.show({title:`Select Attribute for Slot Lv.${n}`,options:s})},hideStatSelectionPopup:function(){this.selectionWindow.hide(),this.currentSlot=null},setupEventListeners:function(){this.setupSlotClickHandlers(),this.elements.panel&&this.elements.panel.addEventListener("click",(e=>{if(e.target.classList.contains("fg-pet-slot-remove")){e.stopPropagation();const t=e.target.getAttribute("data-tier"),n=parseInt(e.target.getAttribute("data-slot"));this.removeStat(t,n)}}))},setupSlotClickHandlers:function(){[this.elements.normalSlots,this.elements.covenantSlots,this.elements.trustSlots].forEach((e=>{e&&e.addEventListener("click",(e=>{const t=e.target.closest(".fg-pet-slot");if(!t)return;if(e.target.classList.contains("fg-pet-slot-remove")||e.target.closest(".fg-pet-slot-remove"))return;const n=t.getAttribute("data-tier"),s=parseInt(t.getAttribute("data-slot")),a=parseInt(t.getAttribute("data-level"));this.showStatSelectionPopup(n,s,a)}))}))},setActiveTier:function(e){this.activeTier=e},formatNumber:function(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},saveToStore:function(){return!(!window.BuildSaver||"function"!=typeof BuildSaver.saveBuild)},selectStat:function(e){if(!this.currentSlot)return;const{tier:t,slotIndex:n}=this.currentSlot,s=PetSystemData.tierStats[t].find((t=>t.id===e));s&&(this.selectedStats[t][n]={id:e,value:s.value},this.updateSlotUI(t,n),this.updateSelectedStatsDisplay(),this.updatePetStats(),this.saveToStore())},updateSlotUI:function(e,t){const n=document.querySelector(`.fg-pet-slot[data-tier="${e}"][data-slot="${t}"]`);if(!n)return;const s=null!==this.selectedStats[e][t];s?(n.classList.add("selected"),n.classList.remove("empty")):(n.classList.remove("selected"),n.classList.add("empty"));const a=n.querySelector(".fg-pet-slot-content");a&&(a.innerHTML=this.getSlotContentHTML(e,t));const i=n.closest(".fg-pet-slot-wrapper");if(i){const n=i.querySelector(".fg-pet-slot-stat-value-below");if(n)if(s){const s=this.selectedStats[e][t],a=PetSystemData.tierStats[e].find((e=>e.id===s.id));if(a){const e=this.getStatSuffix(s.id);n.innerHTML=`+${a.value}${e}`}else n.innerHTML=""}else n.innerHTML=""}},getStatSuffix:function(e){if("undefined"==typeof StatsConfig)return"";const t=StatsConfig.getStatInfo(e);if(t&&void 0!==t.isPercentage)return t.isPercentage?"%":"";for(const t in StatsConfig.stats){const n=StatsConfig.getStatInfo(t);if(n&&n.name===e)return n.isPercentage?"%":""}return""},getCombinedStats:function(){const e={};for(const t in this.selectedStats)this.selectedStats[t].forEach((t=>{if(!t)return;const{id:n,value:s}=t;"allAttackUp"===n?(e.attack=(e.attack||0)+s,e.magicAttack=(e.magicAttack||0)+s,e[n]=(e[n]||0)+s):e[n]=(e[n]||0)+s}));return e},updateSelectedStatsDisplay:function(){if(!this.elements.selectedList&&(this.elements.selectedList=document.querySelector(".fg-pet-selected-list"),!this.elements.selectedList))return;const e=this.getCombinedStats();"undefined"!=typeof StatIntegrationService?this.elements.selectedList.innerHTML=StatIntegrationService.createStatSummaryHTML(e):this.elements.selectedList.innerHTML='<p class="no-stats">No pet stats selected yet.</p>'},updatePetStats:function(){const e=this.getCombinedStats();"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats("pet",e)},removeStat:function(e,t){this.selectedStats[e][t]=null,this.updateSlotUI(e,t),this.updateSelectedStatsDisplay(),this.updatePetStats(),this.saveToStore()},resetAllStats:function(){this.selectedStats={normal:Array(10).fill(null),covenant:Array(10).fill(null),trust:Array(10).fill(null)},["normal","covenant","trust"].forEach((e=>{for(let t=0;t<10;t++)this.updateSlotUI(e,t)})),this.updateSelectedStatsDisplay(),this.updatePetStats(),this.saveToStore()},getEssentialData:function(){return{selectedStats:JSON.parse(JSON.stringify(this.selectedStats))}},loadFromStore:function(){if(window.BuildSaverStore&&BuildSaverStore.dataLoaded){const e=BuildSaverStore.getSystemData("pet");if(e&&e.selectedStats)return this.loadFromData(e)}return!1},loadFromData:function(e){if(e&&e.selectedStats){this.selectedStats=JSON.parse(JSON.stringify(e.selectedStats));for(const e in this.selectedStats)this.selectedStats[e].forEach(((t,n)=>{this.updateSlotUI(e,n)}));return this.updateSelectedStatsDisplay(),this.updatePetStats(),!0}return!1}},document.addEventListener("DOMContentLoaded",(function(){})),function(){if(void 0!==window.enhancedStatData&&void 0!==window.nodeColors)return;const e=document.getElementsByTagName("script"),t=(e[e.length-1].src,window.forceguidesPlannerData?forceguidesPlannerData.pluginUrl:"");var n;(n=t+"js/stellar/stellar-system-data.js",new Promise(((e,t)=>{const s=document.createElement("script");s.src=n,s.onload=e,s.onerror=t,document.head.appendChild(s)}))).then((()=>{})).catch((e=>{}))}(),window.StellarSystem={isInitialized:!1,elements:{},constellationLines:["daedalus","icarus","vulcanos","minerva","pluto"],selectedNode:null,activeNodes:[],statSelectionWindow:null,colorSelectionWindow:null,currentStatSelection:null,nodes:[{id:1,line:"daedalus",raw:{x:428,y:99},normalized:{x:.5336658354114713,y:.12295433291770573},currentStat:null,color:null},{id:2,line:"daedalus",raw:{x:479,y:190},normalized:{x:.5972568578553616,y:.23642066708229426},currentStat:null,color:null},{id:3,line:"daedalus",raw:{x:403,y:253},normalized:{x:.5024937655860349,y:.314974283042394},currentStat:null,color:null},{id:4,line:"daedalus",raw:{x:404,y:328},normalized:{x:.5037406483790524,y:.40849049251870323},currentStat:null,color:null},{id:5,line:"icarus",raw:{x:670,y:224},normalized:{x:.8354114713216958,y:.2788146820448878},currentStat:null,color:null},{id:6,line:"icarus",raw:{x:637,y:318},normalized:{x:.7942643391521197,y:.39602166458852867},currentStat:null,color:null},{id:7,line:"icarus",raw:{x:573,y:238},normalized:{x:.7144638403990025,y:.29627104114713215},currentStat:null,color:null},{id:8,line:"icarus",raw:{x:501,y:281},normalized:{x:.6246882793017456,y:.34988700124688277},currentStat:null,color:null},{id:9,line:"icarus",raw:{x:561,y:353},normalized:{x:.699501246882793,y:.43966256234413964},currentStat:null,color:null},{id:10,line:"icarus",raw:{x:483,y:377},normalized:{x:.6022443890274314,y:.4695877493765586},currentStat:null,color:null},{id:11,line:"vulcanos",raw:{x:623,y:622},normalized:{x:.7768079800498753,y:.7750740336658354},currentStat:null,color:null},{id:12,line:"vulcanos",raw:{x:603,y:538},normalized:{x:.7518703241895262,y:.6703358790523691},currentStat:null,color:null},{id:13,line:"vulcanos",raw:{x:716,y:503},normalized:{x:.8927680798004988,y:.6266949812967582},currentStat:null,color:null},{id:14,line:"vulcanos",raw:{x:645,y:453},normalized:{x:.8042394014962594,y:.5643508416458853},currentStat:null,color:null},{id:15,line:"vulcanos",raw:{x:735,y:355},normalized:{x:.9164588528678305,y:.44215632793017456},currentStat:null,color:null},{id:16,line:"vulcanos",raw:{x:569,y:429},normalized:{x:.7094763092269327,y:.5344256546134664},currentStat:null,color:null},{id:17,line:"vulcanos",raw:{x:505,y:518},normalized:{x:.6296758104738155,y:.64539822319202},currentStat:null,color:null},{id:18,line:"vulcanos",raw:{x:453,y:458},normalized:{x:.5648379052369077,y:.5705852556109726},currentStat:null,color:null},{id:19,line:"minerva",raw:{x:500,y:681},normalized:{x:.6234413965087282,y:.8486401184538653},currentStat:null,color:null},{id:20,line:"minerva",raw:{x:500,y:602},normalized:{x:.6234413965087282,y:.7501363778054863},currentStat:null,color:null},{id:21,line:"minerva",raw:{x:411,y:548},normalized:{x:.5124688279301746,y:.6828047069825436},currentStat:null,color:null},{id:22,line:"minerva",raw:{x:386,y:693},normalized:{x:.48129675810473815,y:.8636027119700748},currentStat:null,color:null},{id:23,line:"minerva",raw:{x:325,y:611},normalized:{x:.40523690773067333,y:.7613583229426434},currentStat:null,color:null},{id:24,line:"minerva",raw:{x:238,y:655},normalized:{x:.2967581047381546,y:.8162211658354115},currentStat:null,color:null},{id:25,line:"minerva",raw:{x:253,y:579},normalized:{x:.31546134663341646,y:.7214580735660848},currentStat:null,color:null},{id:26,line:"minerva",raw:{x:135,y:574},normalized:{x:.16832917705735662,y:.7152236596009975},currentStat:null,color:null},{id:27,line:"minerva",raw:{x:305,y:517},normalized:{x:.3802992518703242,y:.6441513403990025},currentStat:null,color:null},{id:28,line:"minerva",raw:{x:355,y:460},normalized:{x:.442643391521197,y:.5730790211970075},currentStat:null,color:null},{id:29,line:"pluto",raw:{x:260,y:135},normalized:{x:.32418952618453867,y:.16784211346633415},currentStat:null,color:null},{id:30,line:"pluto",raw:{x:353,y:183},normalized:{x:.4401496259351621,y:.22769248753117208},currentStat:null,color:null},{id:31,line:"pluto",raw:{x:308,y:284},normalized:{x:.38403990024937656,y:.3536276496259352},currentStat:null,color:null},{id:32,line:"pluto",raw:{x:256,y:225},normalized:{x:.3192019950124688,y:.28006156483790523},currentStat:null,color:null},{id:33,line:"pluto",raw:{x:152,y:210},normalized:{x:.18952618453865336,y:.2613583229426434},currentStat:null,color:null},{id:34,line:"pluto",raw:{x:176,y:314},normalized:{x:.2194513715710723,y:.3910341334164589},currentStat:null,color:null},{id:35,line:"pluto",raw:{x:79,y:360},normalized:{x:.09850374064837905,y:.44839074189526185},currentStat:null,color:null},{id:36,line:"pluto",raw:{x:158,y:415},normalized:{x:.1970074812967581,y:.516969295511222},currentStat:null,color:null},{id:37,line:"pluto",raw:{x:90,y:496},normalized:{x:.11221945137157108,y:.6179668017456359},currentStat:null,color:null},{id:38,line:"pluto",raw:{x:247,y:450},normalized:{x:.30798004987531175,y:.5606101932668329},currentStat:null,color:null},{id:39,line:"pluto",raw:{x:246,y:353},normalized:{x:.30673316708229426,y:.43966256234413964},currentStat:null,color:null},{id:40,line:"pluto",raw:{x:326,y:379},normalized:{x:.40648379052369077,y:.47208151496259354},currentStat:null,color:null}],baseStats:{},quickFillStellarSystem:function(){const e={daedalus:{stat:"pvePenetration",color:"emptiness"},icarus:{stat:"pveCritDamage",color:"emptiness"},vulcanos:{stat:"allAttackUp",color:"emptiness"},minerva:{stat:"penetration",color:"emptiness"},pluto:{stat:"critDamage",color:"emptiness"}};this.nodes.forEach((t=>{const n=e[t.line];n&&(t.currentStat=n.stat,t.statLevel=5,t.color=n.color)})),this.updateStats(),this.updateNodeSelection(),this.saveToStore()},init:function(){if(this.isInitialized)return;const e=document.getElementById("fg-stellar-system");e&&(this.elements.panel=e,this.initData()&&(this.initUI(),this.initSelectionWindows(),this.loadFromStore(),this.setupEventListeners(),this.updateStats(),this.isInitialized=!0))},initData:function(){return!(!window.enhancedStatData||!window.nodeColors)||(setTimeout((()=>{this.isInitialized||this.init()}),500),!1)},initUI:function(){this.createStellarSystemUI()},initSelectionWindows:function(){this.statSelectionWindow=new SelectionWindowManager({id:"fg-stellar-stat-selector",title:"Select Node Stat",className:"fg-stellar-stat-selector",fixedPosition:!0,onSelect:e=>{e.statId?(this.currentStatSelection={nodeId:this.selectedNode,statId:e.statId,level:e.level},this.showColorSelection(this.selectedNode)):"clear"===e.action&&this.setNodeStat(this.selectedNode,null)},onClose:()=>{this.currentStatSelection=null}}),this.colorSelectionWindow=new SelectionWindowManager({id:"fg-stellar-color-selector",title:"Select Node Color",className:"fg-stellar-color-selector",fixedPosition:!0,onSelect:e=>{if(e.colorId&&this.currentStatSelection){const{nodeId:t,statId:n,level:s}=this.currentStatSelection;this.setNodeStat(t,n,s),this.setNodeColor(t,e.colorId),this.currentStatSelection=null}},onClose:()=>{if(this.currentStatSelection){const{nodeId:e,statId:t,level:n}=this.currentStatSelection;this.setNodeStat(e,t,n),this.currentStatSelection=null}}})},createStellarSystemUI:function(){if(!this.elements.panel)return;const e=document.createElement("div");e.className="fg-stellar-system-container";const t=document.createElement("div");t.className="fg-stellar-skill-tree";const n=document.createElement("div");n.className="fg-stellar-background",t.appendChild(n);const s=document.createElement("div");s.className="fg-stellar-nodes-container",[...this.nodes].sort(((e,t)=>e.line!==t.line?this.constellationLines.indexOf(e.line)-this.constellationLines.indexOf(t.line):e.id-t.id)).forEach((e=>{const t=document.createElement("div");t.className="fg-stellar-node",t.setAttribute("data-node-id",e.id),t.setAttribute("data-line",e.line),t.setAttribute("title",`Node #${e.id} (${this.capitalizeFirstLetter(e.line)})`),t.style.left=100*e.normalized.x+"%",t.style.top=100*e.normalized.y+"%",s.appendChild(t)})),t.appendChild(s);const a=document.createElement("div");a.className="fg-stellar-stat-selection",a.style.display="none",t.appendChild(a);const i=document.createElement("div");i.className="fg-stellar-color-selection",i.style.display="none",t.appendChild(i),e.appendChild(t);const l=document.createElement("div");l.className="fg-stellar-color-bonus-section",l.innerHTML='<h3>Stellar System Summary</h3><div class="fg-stellar-color-bonus-content"></div>',e.appendChild(l),this.elements.panel.innerHTML="",this.elements.panel.appendChild(e),this.elements.skillTree=this.elements.panel.querySelector(".fg-stellar-skill-tree"),this.elements.nodes=this.elements.panel.querySelectorAll(".fg-stellar-node"),this.elements.statSelection=this.elements.panel.querySelector(".fg-stellar-stat-selection"),this.elements.colorSelection=this.elements.panel.querySelector(".fg-stellar-color-selection"),this.elements.colorBonusContent=this.elements.panel.querySelector(".fg-stellar-color-bonus-content"),"undefined"!=typeof QuickFillButtonConfigs&&(this.quickFillManager=QuickFillButtonConfigs.initializeSystem("stellar",e))},setupEventListeners:function(){this.elements.panel&&(this.elements.nodes.forEach((e=>{e.addEventListener("click",(e=>{const t=parseInt(e.currentTarget.getAttribute("data-node-id"));this.selectNode(t)}))})),this.elements.skillTree.addEventListener("click",(e=>{e.target.closest(".fg-stellar-node")||e.target.closest(".fg-stellar-stat-selection")||e.target.closest(".fg-stellar-color-selection")||this.hideSelectionPanels()})),window.addEventListener("resize",(()=>{this.adjustNodePositions()})))},adjustNodePositions:function(){this.elements.skillTree&&this.elements.nodes},selectNode:function(e){this.nodes.find((t=>t.id===e))&&(this.selectedNode=e,this.updateNodeSelection(),this.showStatSelection(e),this.saveToStore())},updateNodeSelection:function(){if(this.elements.nodes){if(this.elements.nodes.forEach((e=>{e.classList.remove("selected")})),this.selectedNode){const e=Array.from(this.elements.nodes).find((e=>parseInt(e.getAttribute("data-node-id"))===this.selectedNode));e&&e.classList.add("selected")}this.updateActiveNodes()}},updateActiveNodes:function(){this.elements.nodes&&(this.elements.skillTree.querySelectorAll(".fg-stellar-node-icon").forEach((e=>e.remove())),this.elements.nodes.forEach((e=>{const t=parseInt(e.getAttribute("data-node-id")),n=this.nodes.find((e=>e.id===t));n&&n.currentStat?(e.classList.add("active"),e.setAttribute("data-stat",n.currentStat),this.addStatIconToNode(n),n.color?e.setAttribute("data-color",n.color):e.removeAttribute("data-color"),e.style.backgroundColor="",e.style.boxShadow="",e.style.border=""):(e.classList.remove("active"),e.removeAttribute("data-stat"),e.removeAttribute("data-color"),e.style.backgroundColor="",e.style.boxShadow="",e.style.border="")})),this.calculateLineColorBonuses())},addStatIconToNode:function(e){if(!e||!e.currentStat)return;const t=e.currentStat,n=e.line;let s=null;if("undefined"!=typeof StatsConfig&&(s=StatsConfig.getStatIconUrl(t)),s){const a=document.createElement("div");a.className="fg-stellar-node-icon",a.setAttribute("data-node-id",e.id),e.color&&a.setAttribute("data-color",e.color),a.style.left=100*e.normalized.x+"%",a.style.top=100*e.normalized.y+"%";const i=document.createElement("img");i.src=s;let l=t;if("undefined"!=typeof StatsConfig)l=StatsConfig.getStatInfo(t).name;else if(n&&window.enhancedStatData&&window.enhancedStatData[n]){const e=window.enhancedStatData[n].find((e=>e.id===t));e&&(l=e.name)}i.alt=l,i.title=l;const o=e.statLevel||1,r=document.createElement("div");if(r.className="fg-stellar-level-indicator",window.enhancedStatData&&window.enhancedStatData[n]){const e=window.enhancedStatData[n].find((e=>e.id===t));if(e&&e.values[o-1]){let t=e.values[o-1];r.textContent=t+(e.isPercentage?"%":"")}else r.textContent=o}else r.textContent=o;a.appendChild(r),a.appendChild(i),this.elements.skillTree.querySelector(".fg-stellar-nodes-container").appendChild(a)}},showStatSelection:function(e){const t=this.nodes.find((t=>t.id===e));if(!t)return;const n=t.line,s=t.currentStat,a=t.statLevel||1,i=[];window.enhancedStatData&&window.enhancedStatData[n]?window.enhancedStatData[n].forEach((e=>{let t="";"undefined"!=typeof StatsConfig?t=`<img src="${StatsConfig.getStatIconUrl(e.id)}" alt="${e.name}" class="fg-selection-option-icon" />`:e.icon&&(t=`<img src="${e.icon}" alt="${e.name}" class="fg-selection-option-icon" />`);for(let n=0;n<e.values.length;n++){const l=n+1,o=e.values[n],r=e.isPercentage?`${o}%`:o,c=s===e.id&&a===l;i.push({html:`\n                            <div class="fg-selection-option-with-icon">\n                                ${t}\n                                <span class="fg-selection-option-level">+${l}</span>\n                                <span class="fg-selection-option-name">${e.name}</span>\n                            </div>\n                            <span class="fg-selection-option-value">+${r}</span>\n                        `,className:c?"selected":"",data:{statId:e.id,level:l}})}})):i.push({html:'<div class="fg-selection-empty-state">No stats available - Data file not loaded!</div>',data:{}}),t.currentStat&&i.push({html:'\n                    <div class="fg-selection-option-with-icon">\n                        <span class="fg-selection-option-name">Clear Selection</span>\n                    </div>\n                ',className:"fg-selection-clear-option",data:{action:"clear"}}),this.statSelectionWindow.show({title:`Select Stat for Node ${e} (${this.capitalizeFirstLetter(t.line)})`,options:i})},setNodeStat:function(e,t,n=1){const s=this.nodes.findIndex((t=>t.id===e));if(-1!==s){if(this.nodes[s].currentStat=t,this.nodes[s].statLevel=t?n:null,null===t&&(this.nodes[s].color=null,this.nodes[s].statLevel=null,this.statSelectionWindow&&this.statSelectionWindow.hide(),this.colorSelectionWindow&&this.colorSelectionWindow.hide()),this.elements.nodes){const s=Array.from(this.elements.nodes).find((t=>parseInt(t.getAttribute("data-node-id"))===e));s&&(t?(s.classList.add("active"),s.setAttribute("data-stat",t),s.setAttribute("data-stat-level",n)):(s.classList.remove("active"),s.removeAttribute("data-stat"),s.removeAttribute("data-stat-level")))}this.updateNodeSelection(),null===t||this.currentStatSelection?(this.updateStats(),this.calculateLineColorBonuses(),this.saveToStore()):(this.statSelectionWindow&&this.statSelectionWindow.hide(),this.showColorSelection(e))}},showColorSelection:function(e){const t=this.nodes.find((t=>t.id===e));if(!t)return;const n=[];window.nodeColors?Object.entries(window.nodeColors).forEach((([e,s])=>{const a=t.color===e,i=s.name,l=s.cssColor,o=s.borderColor;n.push({html:`\n                        <div class="fg-selection-option-with-icon" style="border-left: 3px solid ${l}">\n                            <span class="fg-selection-option-name">${i}</span>\n                            <span class="fg-selection-option-color-preview" style="background-color: ${l}; border: 1px solid ${o};"></span>\n                        </div>\n                    `,className:a?"selected":"",data:{colorId:e}})})):n.push({html:'<div class="fg-selection-empty-state">No colors available - Data file not loaded!</div>',data:{}}),this.colorSelectionWindow.show({title:`Select Color for Node ${e}`,options:n})},setNodeColor:function(e,t){const n=this.nodes.findIndex((t=>t.id===e));if(-1!==n){if(this.nodes[n].color=t,this.elements.nodes){const n=Array.from(this.elements.nodes).find((t=>parseInt(t.getAttribute("data-node-id"))===e));n&&n.setAttribute("data-color",t)}this.colorSelectionWindow&&this.colorSelectionWindow.hide(),this.updateNodeSelection(),this.updateStats(),this.calculateLineColorBonuses(),this.saveToStore()}},hideSelectionPanels:function(){this.statSelectionWindow&&this.statSelectionWindow.hide(),this.colorSelectionWindow&&this.colorSelectionWindow.hide()},getEssentialData:function(){return{nodes:this.nodes.map((e=>e.currentStat?{id:e.id,currentStat:e.currentStat,statLevel:e.statLevel||1,color:e.color}:null)).filter((e=>null!==e)),selectedNode:this.selectedNode}},loadFromStore:function(){if("undefined"==typeof BuildSaverStore)return;const e=BuildSaverStore.getSystemData("stellar");return e?this.loadFromData(e):void 0},loadFromData:function(e){return!!e&&(this.nodes.forEach((e=>{e.currentStat=null,e.statLevel=null,e.color=null})),e.nodes&&Array.isArray(e.nodes)&&e.nodes.forEach((e=>{if(e.id&&e.currentStat){const t=this.nodes.findIndex((t=>t.id===e.id));-1!==t&&(this.nodes[t].currentStat=e.currentStat,this.nodes[t].statLevel=e.statLevel||1,this.nodes[t].color=e.color||null)}})),e.selectedNode&&(this.selectedNode=e.selectedNode),this.updateNodeSelection(),this.updateStats(),this.calculateLineColorBonuses(),!0)},saveToStore:function(){return"undefined"!=typeof BuildSaverStore},resetAllNodes:function(){this.nodes.forEach((e=>{e.currentStat=null,e.statLevel=null,e.color=null})),this.selectedNode=null,this.updateNodeSelection(),this.updateStats(),this.calculateLineColorBonuses(),this.saveToStore()},calculateLineColorBonuses:function(){if(this.lineBonusStats={},!window.lineEffects||!window.nodeColors)return;this.elements.colorBonusContent&&(this.elements.colorBonusContent.innerHTML="");let e=!1,t="";if(this.constellationLines.forEach((n=>{const s=this.nodes.filter((e=>e.line===n)).filter((e=>e.currentStat&&e.color));if(0===s.length)return;const a=s[0].color;if(s.every((e=>e.color===a))&&s.length>=3){e=!0;const s=window.lineEffects[n][a];if(!s)return;const i=window.nodeColors[a]?.name||a;t+=`<div class="fg-stellar-line-bonus">\n                    <span class="fg-stellar-line-name">${this.capitalizeFirstLetter(n)}</span>\n                    <span class="fg-stellar-line-color" style="color:${window.nodeColors[a]?.cssColor||"#fff"}">\n                        ${i}:\n                    </span>`;let l=!1;if(s.effect1&&s.effect1.statId){const e=s.effect1.statId,n=s.effect1.value;let a=e,i=!1;if("undefined"!=typeof StatsConfig){const t=StatsConfig.getStatInfo(e);t&&(a=t.name,i=t.isPercentage)}t+=`<span class="fg-stellar-line-effect">\n                        ${a} +${n}${i?"%":""}\n                    </span>`,l=!0,this.lineBonusStats[e]?this.lineBonusStats[e]+=n:this.lineBonusStats[e]=n}if(s.effect2&&s.effect2.statId){const e=s.effect2.statId,n=s.effect2.value;let a=e,i=!1;if("undefined"!=typeof StatsConfig){const t=StatsConfig.getStatInfo(e);t&&(a=t.name,i=t.isPercentage)}l&&(t+=", "),t+=`<span class="fg-stellar-line-effect">\n                        ${a} +${n}${i?"%":""}\n                    </span>`,this.lineBonusStats[e]?this.lineBonusStats[e]+=n:this.lineBonusStats[e]=n}t+="</div>"}})),this.elements.colorBonusContent){let n="";if(n+='<div class="fg-stellar-constellation-bonuses">',n+="<h4>Constellation Color Bonuses</h4>",n+=e?t:"<p>No color bonuses active yet. Complete constellations with the same color to activate bonuses.</p>",n+="</div>",n+='<div class="fg-stellar-stats-summary">',n+="<h4>Node Stats Summary</h4>","undefined"!=typeof StatIntegrationService&&Object.keys(this.baseStats).length>0)n+=StatIntegrationService.createStatSummaryHTML(this.baseStats);else if(Object.keys(this.baseStats).length>0)for(const e in this.baseStats){const t=this.baseStats[e];n+=`<div class="fg-stellar-stat-item">\n                        <span class="fg-stellar-stat-name">${this.getStatName(e)}</span>\n                        <span class="fg-stellar-stat-value">+${t}${"undefined"!=typeof StatsConfig&&StatsConfig.getStatInfo(e).isPercentage?"%":""}</span>\n                    </div>`}else n+="<p>No stats from nodes yet. Select nodes and assign stats to see summary.</p>";n+="</div>",this.elements.colorBonusContent.innerHTML=n}},calculateStats:function(){const e={};if(this.nodes.forEach((t=>{if(t.currentStat){const n=t.currentStat;let s=0;const a=(t.statLevel||1)-1;if(window.enhancedStatData&&window.enhancedStatData[t.line]){const i=window.enhancedStatData[t.line].find((e=>e.id===n));i&&(s=i.values[a],"undefined"!=typeof StatsConfig&&StatsConfig.getStatInfo(n).isPercentage,s=Math.round(100*s)/100,e[n]?e[n]+=s:e[n]=s)}}})),this.lineBonusStats)for(const t in this.lineBonusStats){const n=this.lineBonusStats[t];e[t]?e[t]+=n:e[t]=n}return e},updateStats:function(){const e=this.calculateStats();this.baseStats=e,"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats("stellar",e)},getStatName:function(e){if("undefined"!=typeof StatsConfig)return StatsConfig.getStatInfo(e).name;if(window.enhancedStatData)for(const t in window.enhancedStatData){const n=window.enhancedStatData[t].find((t=>t.id===e));if(n)return n.name}return e||"Unknown"},getColorName:function(e){return window.nodeColors&&window.nodeColors[e]?window.nodeColors[e].name:e||"Unknown"},capitalizeFirstLetter:function(e){return e.charAt(0).toUpperCase()+e.slice(1)}},function(){if(void 0!==window.HonorMedalData)return;const e=document.getElementsByTagName("script"),t=(e[e.length-1].src,window.forceguidesPlannerData?forceguidesPlannerData.pluginUrl:"");var n;n=t+"js/honor-medal-system/honor-medal-data.js",new Promise(((e,t)=>{const s=document.createElement("script");s.src=n,s.onload=e,s.onerror=t,document.head.appendChild(s)}))}(),window.HonorMedalSystem={isInitialized:!1,elements:{},activeRank:"captain",selectedStats:{captain:Array(4).fill(null),general:Array(6).fill(null),commander:Array(8).fill(null),hero:Array(10).fill(null),legend:Array(12).fill(null)},currentSlot:null,selectionWindow:null,init:function(){if(this.isInitialized)return;const e=document.getElementById("fg-honor-system");e&&(this.elements.panel=e,"undefined"!=typeof HonorMedalData?(this.initUI(),this.selectionWindow=new SelectionWindowManager({id:"fg-honor-medal-selector",title:"Select Honor Medal",className:"fg-honor-medal-selector",fixedPosition:!0,onSelect:e=>{const t=e.statId||e.statid;t&&this.selectStat(t)},onClose:()=>{this.currentSlot=null}}),this.loadFromStore(),this.setupEventListeners(),this.activeRank="captain",this.isInitialized=!0,this.updateHonorMedalStats()):setTimeout((()=>this.init()),100))},initUI:function(){this.createHonorMedalUI()},createHonorMedalUI:function(){if(!this.elements.panel)return;let e="";for(const t in HonorMedalData.ranks){const n=HonorMedalData.ranks[t],s=this.canRankLevelUp(t),a=this.canRankLevelDown(t),i=this.calculateRankLevel(t),l=i>0?` <span class="level">Lv.${i}</span>`:"";e+=`\n                <div id="${t}-rank" class="fg-honor-rank-panel">\n                    <div class="fg-honor-rank-header-container">\n                        <span class="fg-honor-rank-header">• ${n.name}${l}</span>\n                        <button class="fg-honor-rank-level-up" data-rank="${t}"\n                                title="Level up all medals in this rank" ${s?"":"disabled"}>↑</button>\n                        <button class="fg-honor-rank-level-down" data-rank="${t}"\n                                title="Level down all medals in this rank" ${a?"":"disabled"}>↓</button>\n                    </div>\n                    <div class="fg-honor-slots-container" id="${t}-slots">\n                        ${this.createSlotHTML(t,n.slots)}\n                    </div>\n                </div>\n            `}const t=`\n            <div class="fg-honor-system-container">\n                <h2>Honor Medal System</h2>\n\n                \x3c!-- All rank content stacked vertically --\x3e\n                <div class="fg-honor-rank-content">\n                    ${e}\n                </div>\n\n                \x3c!-- Selected stats summary --\x3e\n                <div class="fg-honor-selected-stats">\n                    <h3>Selected Honor Medal Stats</h3>\n                    <div class="fg-honor-selected-list">\n                        \x3c!-- Selected stats will be shown here --\x3e\n                    </div>\n                </div>\n            </div>\n        `;this.elements.panel.innerHTML=t,this.elements.selectedList=this.elements.panel.querySelector(".fg-honor-selected-list"),this.elements.slotContainers={};for(const e in HonorMedalData.ranks)this.elements.slotContainers[e]=this.elements.panel.querySelector(`#${e}-slots`);this.selectionWindow=new SelectionWindowManager({id:"fg-honor-medal-selector",title:"Select Honor Medal",className:"fg-honor-medal-selector",fixedPosition:!0,onSelect:e=>{e.statId&&this.selectStat(e.statId)},onClose:()=>{this.currentSlot=null}}),"undefined"!=typeof QuickFillButtonConfigs&&(this.quickFillManager=QuickFillButtonConfigs.initializeSystem("honor",this.elements.panel.querySelector(".fg-honor-system-container"))),this.setupSlotClickHandlers()},createSlotHTML:function(e,t){let n="";for(let s=0;s<t;s++){const t=s;n+=`\n                <div class="fg-honor-slot ${null!==this.selectedStats[e][t]?"selected":"empty"}"\n                     data-rank="${e}"\n                     data-slot="${t}">\n                    ${this.getSlotContentHTML(e,t)}\n                </div>\n            `}return n},getSlotContentHTML:function(e,t){const n=this.selectedStats[e][t];if(!n)return'<div class="fg-honor-slot-empty"></div>';const{statId:s,level:a}=n,i=this.findStatDefinition(e,s);if(!i)return'<div class="fg-honor-slot-empty"></div>';let l=s;if("undefined"!=typeof StatsConfig){const e=StatsConfig.getStatInfo(s);e&&(l=e.name)}const o=i.values[a-1];let r="";if("undefined"!=typeof StatsConfig)try{r=`<div class="fg-honor-slot-stat-icon"><img src="${StatsConfig.getStatIconUrl(s)}" alt="${l}" onerror="this.onerror=null; this.style.display='none';"></div>`}catch(e){}return`\n            <div class="fg-honor-slot-stat">\n                <button class="fg-honor-slot-remove" data-rank="${e}" data-slot="${t}" title="Remove medal">&times;</button>\n                ${r}\n                <div class="fg-honor-slot-details">\n                    <span class="fg-honor-slot-stat-value">+${o}${this.getStatSuffix(s)}</span>\n                </div>\n            </div>\n        `},showStatSelectionPopup:function(e,t){if(!HonorMedalData.rankStats[e]||0===HonorMedalData.rankStats[e].length)return;this.currentSlot={rankId:e,slotIndex:t};const n=[...HonorMedalData.rankStats[e]].sort(((e,t)=>e.chance-t.chance)).map((e=>{let t=e.id;if("undefined"!=typeof StatsConfig){const n=StatsConfig.getStatInfo(e.id);n&&(t=n.name)}let n="";if("undefined"!=typeof StatsConfig)try{n=`<img src="${StatsConfig.getStatIconUrl(e.id)}" alt="${t}" class="fg-selection-option-icon" onerror="this.onerror=null; this.style.display='none';">`}catch(e){}const s=`<span class="chance">(${e.chance}%)</span>`,a=e.values[0],i=e.values[e.values.length-1],l=this.getStatSuffix(e.id);return{html:`\n                    <div class="fg-selection-option-with-icon">\n                        ${n}\n                        <span class="fg-selection-option-name">${t} ${s}</span>\n                    </div>\n                    <div class="fg-selection-option-value">\n                        +${a}${l} ~ +${i}${l}\n                    </div>\n                `,data:{statId:e.id}}}));this.selectionWindow.show({title:`Select Medal for ${HonorMedalData.ranks[e].name} Slot ${t+1}`,options:n})},hideStatSelectionPopup:function(){this.selectionWindow.hide(),this.currentSlot=null},setupEventListeners:function(){this.setupSlotClickHandlers(),this.elements.panel&&this.elements.panel.addEventListener("click",(e=>{if(e.target.classList.contains("fg-honor-slot-remove")){e.stopPropagation();const t=e.target.getAttribute("data-rank"),n=parseInt(e.target.getAttribute("data-slot"));this.removeStat(t,n)}if(e.target.classList.contains("fg-honor-rank-level-up")){e.stopPropagation();const t=e.target.getAttribute("data-rank");this.levelUpRank(t)}if(e.target.classList.contains("fg-honor-rank-level-down")){e.stopPropagation();const t=e.target.getAttribute("data-rank");this.levelDownRank(t)}}))},setupSlotClickHandlers:function(){for(const e in this.elements.slotContainers){const t=this.elements.slotContainers[e];t&&t.addEventListener("click",(e=>{const t=e.target.closest(".fg-honor-slot");if(!t)return;if(e.target.classList.contains("fg-honor-slot-remove")||e.target.closest(".fg-honor-slot-remove"))return;const n=t.getAttribute("data-rank"),s=parseInt(t.getAttribute("data-slot"));this.selectedStats[n][s]||this.showStatSelectionPopup(n,s)}))}},findStatDefinition:function(e,t){const n=HonorMedalData.rankStats[e];return n?n.find((e=>e.id===t)):null},quickFill:function(){const e={captain:"dex",general:"critDamage",commander:"allAttackUp",hero:"penetration",legend:"allAttackUp"};for(const t in e){const n=e[t],s=HonorMedalData.ranks[t].slots,a=HonorMedalData.rankStats[t];if(a&&a.find((e=>e.id===n))){for(let e=0;e<s;e++)this.selectedStats[t][e]={statId:n,level:HonorMedalData.maxLevel},this.updateSlotUI(t,e);this.updateRankLevelUpButton(t),this.updateRankLevelDisplay(t)}}this.updateSelectedStatsDisplay(),this.updateHonorMedalStats(),this.saveToStore()},selectStat:function(e){if(!this.currentSlot)return;const{rankId:t,slotIndex:n}=this.currentSlot;if(!this.findStatDefinition(t,e))return;const s=this.calculateRankLevel(t),a=s>0?s:1;this.selectedStats[t][n]={statId:e,level:a},this.updateSlotUI(t,n),this.updateSelectedStatsDisplay(),this.updateHonorMedalStats(),this.updateRankLevelUpButton(t),this.updateRankLevelDisplay(t),this.saveToStore()},updateSlotUI:function(e,t){const n=document.querySelector(`.fg-honor-slot[data-rank="${e}"][data-slot="${t}"]`);n&&(this.selectedStats[e][t]?(n.classList.add("selected"),n.classList.remove("empty")):(n.classList.remove("selected"),n.classList.add("empty")),n.innerHTML=this.getSlotContentHTML(e,t))},getStatSuffix:function(e){if("undefined"!=typeof StatsConfig){const t=StatsConfig.getStatInfo(e);if(t&&void 0!==t.isPercentage)return t.isPercentage?"%":""}return""},calculateHonorStats:function(){const e={};for(const t in this.selectedStats)this.selectedStats[t].forEach((n=>{if(!n)return;const{statId:s,level:a}=n,i=this.findStatDefinition(t,s);if(!i)return;const l=i.values[a-1];e[s]=(e[s]||0)+l}));return e},updateSelectedStatsDisplay:function(){if(!this.elements.selectedList&&(this.elements.selectedList=document.querySelector(".fg-honor-selected-list"),!this.elements.selectedList))return;const e=this.calculateHonorStats();"undefined"!=typeof StatIntegrationService?this.elements.selectedList.innerHTML=StatIntegrationService.createStatSummaryHTML(e):this.elements.selectedList.innerHTML='<p class="no-stats">No honor medals selected yet.</p>'},updateHonorMedalStats:function(){const e=this.calculateHonorStats();"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&(BuildPlanner.updateStats("honor",e),"undefined"!=typeof DPSCalculator&&DPSCalculator.calculateAndUpdateDPS&&BuildPlanner.totalStats&&DPSCalculator.calculateAndUpdateDPS(BuildPlanner.totalStats))},removeStat:function(e,t){this.selectedStats[e][t]=null,this.updateSlotUI(e,t),this.updateSelectedStatsDisplay(),this.updateHonorMedalStats(),this.updateRankLevelUpButton(e),this.updateRankLevelDisplay(e),this.saveToStore()},canRankLevelUp:function(e){return this.selectedStats[e].some((e=>null!==e&&e.level<HonorMedalData.maxLevel))},canRankLevelDown:function(e){return this.selectedStats[e].some((e=>null!==e&&e.level>1))},updateRankLevelUpButton:function(e){const t=document.querySelector(`.fg-honor-rank-level-up[data-rank="${e}"]`);if(t){const n=this.canRankLevelUp(e);t.disabled=!n}const n=document.querySelector(`.fg-honor-rank-level-down[data-rank="${e}"]`);if(n){const t=this.canRankLevelDown(e);n.disabled=!t}},adjustRankLevel:function(e,t){let n=!1;this.selectedStats[e].forEach(((s,a)=>{if(!s)return;const i=s.level+t;i>=1&&i<=HonorMedalData.maxLevel&&(s.level=i,this.updateSlotUI(e,a),this.showLevelUpFeedback(e,a),n=!0)})),n&&(this.updateSelectedStatsDisplay(),this.updateHonorMedalStats(),this.saveToStore(),this.updateRankLevelUpButton(e),this.updateRankLevelDisplay(e))},levelUpRank:function(e){this.adjustRankLevel(e,1)},levelDownRank:function(e){this.adjustRankLevel(e,-1)},levelUpStat:function(e,t){const n=this.selectedStats[e][t];n&&(n.level>=HonorMedalData.maxLevel||(n.level+=1,this.updateSlotUI(e,t),this.updateSelectedStatsDisplay(),this.updateHonorMedalStats(),this.saveToStore(),this.updateRankLevelUpButton(e),this.showLevelUpFeedback(e,t)))},showLevelUpFeedback:function(e,t){const n=document.querySelector(`.fg-honor-slot[data-rank="${e}"][data-slot="${t}"]`);n&&(n.classList.add("level-up-animation"),setTimeout((()=>n.classList.remove("level-up-animation")),500))},getEssentialData:function(){return{selectedStats:JSON.parse(JSON.stringify(this.selectedStats))}},saveToStore:function(){if(this.ensureDataStructure(),"undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("honor-medal",e),!0}return!1},loadFromStore:function(){if(window.BuildSaverStore&&BuildSaverStore.dataLoaded){const e=BuildSaverStore.getSystemData("honor-medal");if(e&&e.selectedStats)return this.loadFromData(e)}return!1},loadFromData:function(e){if(e&&e.selectedStats){this.selectedStats=JSON.parse(JSON.stringify(e.selectedStats)),this.ensureDataStructure();for(const e in this.selectedStats)this.selectedStats[e].forEach(((t,n)=>{n<HonorMedalData.ranks[e].slots&&this.updateSlotUI(e,n)})),this.updateRankLevelUpButton(e),this.updateRankLevelDisplay(e);return this.updateSelectedStatsDisplay(),this.updateHonorMedalStats(),!0}return!1},resetAllStats:function(){const e={selectedStats:{captain:Array(4).fill(null),general:Array(6).fill(null),commander:Array(8).fill(null),hero:Array(10).fill(null),legend:Array(12).fill(null)}};this.loadFromData(e),this.saveToStore()},calculateRankLevel:function(e){const t=this.selectedStats[e];if(!t||!t.length)return 0;let n=0,s=0;return t.forEach((e=>{e&&(n++,s+=e.level)})),n>0?Math.floor(s/n):0},updateRankLevelDisplay:function(e){const t=document.querySelector(`#${e}-rank .fg-honor-rank-header`);if(t){const n=HonorMedalData.ranks[e],s=this.calculateRankLevel(e),a=s>0?` <span class="level">Lv.${s}</span>`:"";t.innerHTML=`• ${n.name}${a}`}},ensureAndSave:function(){if(this.ensureDataStructure(),"undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("honor-medal",e),!0}return!1},ensureDataStructure:function(){this.selectedStats||(this.selectedStats={captain:Array(4).fill(null),general:Array(6).fill(null),commander:Array(8).fill(null),hero:Array(10).fill(null),legend:Array(12).fill(null)}),["captain","general","commander","hero","legend"].forEach((e=>{if(!this.selectedStats[e]){const t=HonorMedalData.ranks[e].slots;this.selectedStats[e]=Array(t).fill(null)}}));for(const e in this.selectedStats)if(HonorMedalData.ranks[e]){const t=HonorMedalData.ranks[e].slots,n=this.selectedStats[e].length;if(n<t){const s=Array(t-n).fill(null);this.selectedStats[e]=this.selectedStats[e].concat(s)}else n>t&&(this.selectedStats[e]=this.selectedStats[e].slice(0,t))}for(const e in this.selectedStats)for(let t=0;t<this.selectedStats[e].length;t++){const n=this.selectedStats[e][t];n&&(n.statId||(n.statId="unknown"),n.level&&"number"==typeof n.level||(n.level=1),n.level>HonorMedalData.maxLevel&&(n.level=HonorMedalData.maxLevel))}}},document.addEventListener("DOMContentLoaded",(function(){})),function(){if(void 0!==window.EssenceRuneData)return;const e=document.getElementsByTagName("script"),t=(e[e.length-1].src,window.forceguidesPlannerData?forceguidesPlannerData.pluginUrl:"");var n;n=t+"js/essence-rune-system/essence-rune-data.js",new Promise(((e,t)=>{const s=document.createElement("script");s.src=n,s.onload=e,s.onerror=t,document.head.appendChild(s)}))}(),window.EssenceRunesSystem={isInitialized:!1,dataLoaded:!1,elements:{},selectionWindow:null,data:{totalSlots:16,maxSlots:60,selectedRunes:Array(60).fill(null),currentSlot:null,get runeDefinitions(){return window.EssenceRuneData&&window.EssenceRuneData.runeDefinitions?window.EssenceRuneData.runeDefinitions:{hp:{baseStatType:"hp",iconId:"hp",variants:[{id:"hp",name:"HP I (Default)",tier:1,maxLevel:20,valuePerLevel:[50,100,150,200,250,300,350,400,450,500],apCost:[3,4,5,7,9,12,15,19,23,28],location:"Default location",materials:[]}]}}},exampleRunes:[{slot:0,type:"maxCritRate",value:3},{slot:1,type:"critDmg",value:5},{slot:2,type:"ignoreResistCritDmg",value:5},{slot:3,type:"cancelIgnorePenetration",value:9},{slot:4,type:"ignoreResistSkillAmp",value:5},{slot:5,type:"ignoreEvasion",value:10},{slot:6,type:"normalDmgUp",value:10},{slot:7,type:"penetration",value:10},{slot:8,type:"penetration2",value:25},{slot:9,type:"ignorePenetration2",value:45},{slot:10,type:"dmgReduction2",value:36},{slot:11,type:"int2",value:12},{slot:12,type:"str2",value:12},{slot:13,type:"defenseRate2",value:150}]},init:function(){if(this.isInitialized)return;if(!window.EssenceRuneData||!window.EssenceRuneData.runeDefinitions)return void setTimeout((()=>this.init()),500);const e=document.getElementById("fg-essence-runes-system");e&&(this.elements.panel=e,this.initUI(),this.setupEventListeners(),this.waitForBuildPlanner(10),this.isInitialized=!0)},waitForBuildPlanner:function(e){const t=this;let n=0;!function s(){return n++,"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&void 0!==window.EssenceRuneData?(t.updateStats(),!0):n<e&&(setTimeout(s,200*n),!1)}()},refreshSystem:function(){this.ensureDataStructure(),this.refreshUI(),this.elements.runeSelector&&(this.elements.runeSelector.style.display="none"),this.updateStats()},ensureDataStructure:function(){if(this.data||(this.data={}),this.data.selectedRunes&&Array.isArray(this.data.selectedRunes)||(this.data.selectedRunes=[]),this.data.selectedRunes.length<this.data.totalSlots){const e=this.data.totalSlots-this.data.selectedRunes.length,t=Array(e).fill(null);this.data.selectedRunes=this.data.selectedRunes.concat(t)}for(let e=0;e<this.data.selectedRunes.length;e++){const t=this.data.selectedRunes[e];if(t&&(t.type||(t.type="unknown"),t.id||(t.id="unknown"),t.level||(t.level=1),!t.maxLevel)){let e=!1;for(const[n,s]of Object.entries(this.data.runeDefinitions)){const n=s.variants.find((e=>e.id===t.type));if(n){t.maxLevel=n.maxLevel||10,e=!0;break}}e||(t.maxLevel=10)}}},ensureAndSave:function(){if(this.ensureDataStructure(),"undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("essence-runes",e),!0}return!1},getEssentialData:function(){return this.ensureDataStructure(),{selectedRunes:this.data.selectedRunes.map((e=>e?{id:e.id||null,type:e.type||null,level:e.level||1,maxLevel:e.maxLevel||10}:null)),totalSlots:this.data.totalSlots||16}},saveToStore:function(){if("undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("essence-runes",e),!0}return!1},initUI:function(){const e=`\n            <div class="essence-runes-main-layout">\n                <div class="essence-runes-left-panel">\n                    <div class="essence-runes-container">\n                        <div class="essence-runes-header">\n                            <h2>Essence Runes System</h2>\n                        </div>\n\n                        <div class="essence-runes-slots-scroll">\n                            <div class="essence-runes-slots-container">\n                                \x3c!-- Slots will be generated here --\x3e\n                            </div>\n                        </div>\n\n                        <div class="essence-runes-slots-counter">\n                            Essence Slot (In Use/Total): <span class="essence-runes-slots-count">0/${this.data.totalSlots}</span>\n                        </div>\n\n                        <div class="essence-runes-add-row">\n                            <div class="essence-runes-add-row-icon">+</div>\n                            <div>Add Slot</div>\n                        </div>\n                    </div>\n                </div>\n\n                <div class="essence-runes-right-panel">\n                    <div class="essence-runes-container">\n                        <div class="essence-runes-details">\n                            <h3>Rune Details</h3>\n                            <div class="essence-runes-details-content">\n                                <div class="essence-rune-details-empty">Select a rune to view details</div>\n                            </div>\n                        </div>\n\n                        <div class="essence-runes-stats">\n                            <h3>Stats Summary</h3>\n                            <div class="essence-runes-stats-content">\n                                <div class="essence-rune-details-empty">No runes equipped yet</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        `;this.elements.panel.innerHTML=e,this.elements.slotsContainer=this.elements.panel.querySelector(".essence-runes-slots-container"),this.elements.slotsCount=this.elements.panel.querySelector(".essence-runes-slots-count"),this.elements.addRowButton=this.elements.panel.querySelector(".essence-runes-add-row"),this.elements.detailsContent=this.elements.panel.querySelector(".essence-runes-details-content"),this.elements.statsContent=this.elements.panel.querySelector(".essence-runes-stats-content"),this.selectionWindow=new SelectionWindowManager({id:"fg-essence-rune-selector",title:"Select Essence Rune",className:"fg-essence-rune-selector",fixedPosition:!0,onSelect:e=>{e.type&&this.selectRune(e.type)},onClose:()=>{this.data.currentSlot=null}}),"undefined"!=typeof QuickFillButtonConfigs&&(this.quickFillManager=QuickFillButtonConfigs.initializeSystem("essence-runes",this.elements.panel.querySelector(".essence-runes-container"))),this.data.selectedRunes&&Array.isArray(this.data.selectedRunes)||(this.data.selectedRunes=Array(this.data.maxSlots).fill(null)),this.loadFromStore(),this.generateSlots(),this.updateStats(),this.updateSlotsCounter(),this.updateStatsDisplay()},loadFromStore:function(){if(window.BuildSaverStore&&BuildSaverStore.dataLoaded){const e=BuildSaverStore.getSystemData("essence-runes");if(e&&e.selectedRunes)return this.loadFromData(e)}return!1},loadFromData:function(e){return!(!e||!e.selectedRunes||(this.data.selectedRunes=JSON.parse(JSON.stringify(e.selectedRunes)),e.totalSlots&&"number"==typeof e.totalSlots&&(this.data.totalSlots=e.totalSlots),setTimeout((()=>{this.updateStats(),"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStatsSummaryUI&&BuildPlanner.updateStatsSummaryUI(BuildPlanner.totalStats)}),300),0))},findRuneDefinition:function(e){if(!e)return null;for(const[t,n]of Object.entries(this.data.runeDefinitions)){const s=n.variants.find((t=>t.id===e));if(s)return{variant:s,definition:n,baseStatType:t,iconId:n.iconId||t}}return null},refreshUI:function(){this.refreshRuneSlots(),this.updateSlotsCounter(),this.updateStatsDisplay()},getRuneContentHTML:function(e,t){if(!e)return'<div class="essence-rune-content">Click to add rune</div>';const n=this.findRuneDefinition(e.type);if(!n)return'<div class="essence-rune-content">Unknown rune</div>';const{variant:s,definition:a,iconId:i,baseStatType:l}=n,o=(s.id,s.name),r=s.color||"#4caf50";StatsConfig.getStatInfo(a.baseStatType||i).isPercentage;let c="";if(s&&s.valuePerLevel){const t=Math.min(e.level-1,s.valuePerLevel.length-1),n=s.valuePerLevel[t];c=StatsConfig.formatStatValue(l,n)}else c=`Lv. ${e.level}`;const d=e.level>=s.maxLevel?"essence-rune-level-up disabled":"essence-rune-level-up";let u="";if("undefined"!=typeof StatsConfig)try{u=`\n                    <div class="essence-rune-icon">\n                        <img src="${StatsConfig.getStatIconUrl(i||e.type)}" alt="${o}" onerror="handleRuneIconError(this, '${r}')">\n                    </div>\n                `}catch(e){u=`\n                    <div class="essence-rune-icon">\n                        <div class="essence-rune-icon-inner" style="background-color: ${r}"></div>\n                    </div>\n                `}else u=`\n                <div class="essence-rune-icon">\n                    <div class="essence-rune-icon-inner" style="background-color: ${r}"></div>\n                </div>\n            `;return`\n            <div class="essence-rune-content">\n                ${u}\n                <div class="essence-rune-name" style="color: ${r}">\n                    ${o}\n                </div>\n                <div class="essence-rune-level">Lv. ${e.level}</div>\n                <div class="${d}" data-slot="${t}" title="Level up rune">+</div>\n                <div class="essence-rune-remove" data-slot="${t}" title="Remove rune">×</div>\n            </div>\n        `},updateStatsDisplay:function(){const e=this.generateStatsHTML();this.elements.statsContent&&(this.elements.statsContent.innerHTML=e)},removeRune:function(e){e<0||e>=this.data.maxSlots||(this.data.selectedRunes[e]=null,this.data.currentSlot===e&&(this.data.currentSlot=null,this.showEmptyDetails()),this.refreshUI(),this.updateStats(),this.saveToStore())},setupEventListeners:function(){const e=this;"function"!=typeof window.handleRuneIconError&&(window.handleRuneIconError=function(e,t){e.onerror=null,e.style.display="none",e.parentNode.innerHTML='<div class="essence-rune-icon-inner" style="background-color: '+t+'"></div>'}),this.elements.slotsContainer.addEventListener("click",(function(t){const n=t.target.closest(".essence-rune-slot");if(n){const s=parseInt(n.dataset.slot,10);if(t.target.classList.contains("essence-rune-remove"))return void e.removeRune(s);if(t.target.classList.contains("essence-rune-level-up")&&!t.target.classList.contains("disabled"))return void e.levelUpRune(s);e.data.selectedRunes[s]?e.showRuneDetails(s):e.showRuneSelector(s)}})),this.elements.addRowButton.addEventListener("click",(function(){e.addSlot()})),this.elements.detailsContent.addEventListener("click",(function(t){if(t.target.classList.contains("level-down-button")){const n=parseInt(t.target.dataset.slot,10);e.levelDownRune(n)}else if(t.target.classList.contains("level-up-button")){const n=parseInt(t.target.dataset.slot,10);e.levelUpRune(n)}}))},getUsedSlots:function(){return this.data.selectedRunes.filter((e=>null!==e)).length},generateSlotsHTML:function(){let e="";for(let t=0;t<this.data.totalSlots;t++){const n=this.data.selectedRunes[t];let s="essence-rune-slot";s+=n?" filled":" empty",e+=`\n                <div class="${s}" data-slot="${t}">\n                    <div class="essence-rune-slot-level">${t+1}</div>\n                    ${this.getRuneContentHTML(n,t)}\n                </div>\n            `}return e},generateStatsHTML:function(){const e={},t={};if(0===this.data.selectedRunes.filter((e=>null!==e)).length)return'<div class="essence-rune-stats-empty">No Essence Runes equipped yet</div>';this.data.selectedRunes.forEach((n=>{if(!n)return;const s=this.findRuneDefinition(n.type);if(!s)return;const{variant:a,definition:i,baseStatType:l}=s,o=Math.min(n.level-1,a.valuePerLevel.length-1),r=a.valuePerLevel[o];t[l]||(t[l]={baseStatType:l,iconId:i.iconId,isPercent:StatsConfig.getStatInfo(l).isPercentage}),e[l]||(e[l]=0),e[l]+=r}));let n="";return Object.keys(e).sort(((e,t)=>{const n=StatsConfig.getStatInfo(e).name||e,s=StatsConfig.getStatInfo(t).name||t;return n.localeCompare(s)})).forEach((s=>{const a=t[s],i=e[s];let l="";if("undefined"!=typeof StatsConfig)try{l=`<img src="${StatsConfig.getStatIconUrl(a.iconId||s)}" alt="${s}" class="essence-rune-stat-icon" onerror="this.style.display='none';">`}catch(e){}const o=StatsConfig.formatStatValue(s,i),r=StatsConfig.getStatInfo(s).name||this.formatStatName(s);n+=`\n                <div class="essence-rune-stat-item">\n                    <div class="essence-rune-stat-name">${l}${r}</div>\n                    <div class="essence-rune-stat-value">${o}</div>\n                </div>\n            `})),n},formatStatName:function(e){return e.replace(/([A-Z])/g," $1").replace(/^[a-z]/,(e=>e.toUpperCase())).replace(/ [a-z]/g,(e=>e.toUpperCase()))},showRuneSelector:function(e){if(this.data.currentSlot=e,!window.EssenceRuneData||!window.EssenceRuneData.runeDefinitions)return void this.selectionWindow.show({title:"Error Loading Runes",options:[{html:'<div class="fg-selection-empty-state">Error loading rune data. Please refresh the page.</div>',data:{}}]});const t=this.data.selectedRunes.filter((e=>null!==e)).map((e=>e.type)),n=[];Object.entries(window.EssenceRuneData.runeDefinitions).forEach((([e,t])=>{t.variants&&Array.isArray(t.variants)&&t.variants.forEach((s=>{n.push({id:s.id,name:s.name||e,color:s.color||"#4caf50",tier:s.tier||1,isPercent:StatsConfig.getStatInfo(e).isPercentage,baseStatType:t.baseStatType||e,iconId:t.iconId||e})}))})),n.sort(((e,t)=>e.tier!==t.tier?e.tier-t.tier:e.name.localeCompare(t.name)));const s=n.map((e=>{const n=t.includes(e.id);let s="";if("undefined"!=typeof StatsConfig)try{s=`<img src="${StatsConfig.getStatIconUrl(e.iconId)}" alt="${e.name}" class="fg-selection-option-icon" onerror="handleRuneIconError(this, '${e.color}')">`}catch(t){s=`<div class="fg-selection-option-icon-placeholder" style="background-color: ${e.color}; width: 20px; height: 20px; display: inline-block; margin-right: 8px;"></div>`}else s=`<div class="fg-selection-option-icon-placeholder" style="background-color: ${e.color}; width: 20px; height: 20px; display: inline-block; margin-right: 8px;"></div>`;return{html:`\n                    <div class="fg-selection-option-with-icon">\n                        ${s}\n                        <span class="fg-selection-option-name" style="color: ${e.color}">${e.name}</span>\n                    </div>\n                    <span class="fg-selection-option-action">${n?"Selected":"Select"}</span>\n                `,className:n?"disabled":"",data:{type:e.id,disabled:n}}}));this.selectionWindow.show({title:"Select Essence Rune",options:s})},hideRuneSelector:function(){this.selectionWindow.hide(),this.data.currentSlot=null},selectRune:function(e){if(null===this.data.currentSlot)return;const t=this.findRuneDefinition(e);t&&(this.data.selectedRunes[this.data.currentSlot]={type:e,id:e,level:1,maxLevel:t.variant.maxLevel},this.refreshRuneSlots(),this.hideRuneSelector(),this.updateStatsDisplay(),this.updateStats(),this.saveToStore())},updateUIAfterChange:function(e){this.refreshUI(),this.showRuneDetails(e),this.updateStats()},calculateStats:function(){const e={};return this.data.selectedRunes.forEach((t=>{if(!t)return;const n=this.findRuneDefinition(t.type);if(!n)return;const{variant:s,definition:a,baseStatType:i}=n,l=Math.min(t.level-1,s.valuePerLevel.length-1),o=s.valuePerLevel[l],r=a.baseStatType||i;e[r]?e[r]+=o:e[r]=o})),e},levelUpRune:function(e){const t=this.data.selectedRunes[e];if(!t)return;const n=this.findRuneDefinition(t.type);let s=10;n?s=n.variant.maxLevel:t.maxLevel&&(s=t.maxLevel),t.level<s&&(t.level++,this.updateUIAfterChange(e),this.saveToStore())},levelDownRune:function(e){const t=this.data.selectedRunes[e];t&&t.level>1&&(t.level--,this.updateUIAfterChange(e),this.showRuneDetails(e),this.saveToStore())},showRuneDetails:function(e){const t=this.data.selectedRunes[e];if(!this.elements.detailsContent)return;if(!t)return void(this.elements.detailsContent.innerHTML=`\n                <h3>Slot ${e+1} Details</h3>\n                <div class="essence-rune-details-empty">\n                    No rune equipped in this slot\n                </div>\n            `);const n=this.findRuneDefinition(t.type);if(!n)return void(this.elements.detailsContent.innerHTML=`\n                <div class="essence-rune-details-header">\n                    <div class="essence-rune-details-name" style="color: #ff0000">\n                        ${t.type} (Missing Definition)\n                    </div>\n                </div>\n                <div class="essence-rune-location">\n                    <strong>Location:</strong> Missing definition - update essence-rune-data.js\n                </div>\n            `);const{variant:s,definition:a,baseStatType:i,iconId:l}=n,o=StatsConfig.getStatInfo(i).name||s.name.replace(/ I+$/,""),r=Math.min(t.level-1,s.valuePerLevel.length-1),c=s.valuePerLevel[r],d=StatsConfig.formatStatValue(i,c),u=s.location||"Unknown";let h="";if("undefined"!=typeof StatsConfig)try{h=`<img src="${StatsConfig.getStatIconUrl(l)}" alt="${s.name}" class="essence-rune-stat-icon">`}catch(e){h=`<div class="essence-rune-icon-inner" style="background-color: ${s.color||"#4caf50"}"></div>`}const f=`\n            <div class="essence-rune-details-header">\n                <div class="essence-rune-details-name" style="color: ${s.color||"#4caf50"}">\n                    ${h} ${s.name}\n                </div>\n                <div class="essence-rune-details-value">\n                    ${o} +${d}\n                </div>\n            </div>\n            <div class="essence-rune-location">\n                <strong>Location:</strong> ${u}\n            </div>\n            <div class="essence-rune-level-controls">\n                <button class="${t.level<=1?"level-down-button disabled":"level-down-button"}" data-slot="${e}">-</button>\n                <span>Level: ${t.level}</span>\n                <button class="${t.level>=s.maxLevel?"level-up-button disabled":"level-up-button"}" data-slot="${e}">+</button>\n            </div>\n        `;this.elements.detailsContent.innerHTML=f},getRuneDescription:function(e,t){let n=!1,s=null;for(const[a,i]of Object.entries(this.data.runeDefinitions)){const a=i.variants.find((t=>t.id===e));if(a&&a.valuePerLevel){n=!0;const e=Math.min(t-1,a.valuePerLevel.length-1);s=a.valuePerLevel[e];break}}if(n&&null!==s)switch(e){case"exp":return`Increases experience gain by ${s}%. This affects all experience earned in-game.`;case"skillExp":return`Increases skill experience gain by ${s}%. This affects all skill experience earned in-game.`;case"partyExp":return`Increases party experience gain by ${s}%. This affects experience earned when in a party.`;case"petExp":return`Increases pet experience gain by ${s}%. This affects all pet experience earned in-game.`;case"alzDropAmount":return`Increases Alz drop amount by ${s}%. This affects the amount of Alz dropped from monsters.`;case"alzDropRate":return`Increases Alz drop rate by ${s}%. This affects the chance of Alz dropping from monsters.`;case"alzBombChance":return`Increases Alz Bomb chance by ${s}%. This affects the chance of getting a large Alz drop.`}return`${this.getRuneType(e).name} at level ${t}`},updateStats:function(){const e=this.calculateStats();"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats("essence-runes",e),this.updateStatsDisplay()},addSlot:function(){this.data.totalSlots>=this.data.maxSlots||(this.data.totalSlots++,this.elements.slotsContainer.innerHTML=this.generateSlotsHTML(),this.elements.slotsCount.textContent=`${this.getUsedSlots()}/${this.data.totalSlots}`,this.data.totalSlots>=this.data.maxSlots&&(this.elements.addRowButton.style.display="none"))},updateSlotsCounter:function(){this.elements.slotsCount.textContent=`${this.getUsedSlots()}/${this.data.totalSlots}`},generateSlots:function(){if(!window.EssenceRuneData||!window.EssenceRuneData.runeDefinitions)return void setTimeout((()=>this.generateSlots()),500);const e=this.generateSlotsHTML();this.elements.slotsContainer&&(this.elements.slotsContainer.innerHTML=e)},refreshRuneSlots:function(){const e=this.generateSlotsHTML();this.elements.slotsContainer&&(this.elements.slotsContainer.innerHTML=e)},showEmptyDetails:function(){this.elements.detailsContent.innerHTML='\n            <h3>Slot Details</h3>\n            <div class="essence-rune-details-empty">\n                No rune equipped in this slot\n            </div>\n        '},getRuneType:function(e){const t=this.findRuneDefinition(e);if(t){const{variant:e,baseStatType:n}=t;return{id:e.id,name:e.name,isPercent:StatsConfig.getStatInfo(n).isPercentage,color:e.color||"#4caf50"}}return{id:e,name:`Unknown Type: ${e}`,isPercent:!1,color:"#ff0000"}},maxAllRunes:function(){const e=[];for(const[t,n]of Object.entries(this.data.runeDefinitions))n.variants.forEach((t=>{e.push(t.id)}));const t=this.data.selectedRunes.filter((e=>null!==e)).map((e=>e.type)),n=e.filter((e=>!t.includes(e))),s=t.length+n.length,a=Math.min(s,this.data.maxSlots);for(;this.data.totalSlots<a;)this.addSlot();let i=0;for(const e of n){for(;i<this.data.totalSlots&&null!==this.data.selectedRunes[i];)i++;if(i>=this.data.totalSlots)break;let t=null,n=10;for(const[s,a]of Object.entries(this.data.runeDefinitions)){const s=a.variants.find((t=>t.id===e));if(s){t=s,n=s.maxLevel;break}}this.data.selectedRunes[i]={type:e,id:e,level:n,maxLevel:n},i++}for(let e=0;e<this.data.totalSlots;e++){const t=this.data.selectedRunes[e];if(t){let e=10;for(const[n,s]of Object.entries(this.data.runeDefinitions)){const n=s.variants.find((e=>e.id===t.type));if(n){e=n.maxLevel;break}}t.level=e}}this.refreshRuneSlots(),this.updateSlotsCounter(),this.updateStatsDisplay(),this.updateStats(),this.saveToStore()},resetAllRunes:function(){confirm("Are you sure you want to remove all runes and reset to default slots? This cannot be undone.")&&(this.data.selectedRunes=Array(this.data.maxSlots).fill(null),this.data.totalSlots=16,this.refreshRuneSlots(),this.updateSlotsCounter(),this.updateStatsDisplay(),this.updateStats(),this.saveToStore())},getRuneDetails:function(e){if(!e)return null;let t=null,n=null,s=null;for(const[a,i]of Object.entries(this.data.runeDefinitions)){const l=i.variants.find((t=>t.id===e.type));if(l){t=l,n=i.iconId,s=a;break}}if(!t)return{name:"Unknown Rune",currentLevel:e.level,maxLevel:e.maxLevel||10,currentValue:0,nextValue:0,apCost:0,materials:[],statType:"unknown",iconUrl:null};const a=Math.min(e.level-1,t.valuePerLevel.length-1),i=t.valuePerLevel[a],l=Math.min(e.level,t.valuePerLevel.length-1),o=t.valuePerLevel[l],r=Math.min(e.level,t.apCost.length-1),c=t.apCost[r];let d=[];if(e.level<t.maxLevel&&t.materials&&t.materials.length>0){const n=Math.min(e.level+1,t.materials.length-1),s=t.materials[n];s&&s.name&&s.quantity>0&&d.push({name:s.name,quantity:s.quantity})}let u=null;"undefined"!=typeof StatsConfig&&StatsConfig.getStatIconUrl&&n&&(u=StatsConfig.getStatIconUrl(n));let h=!1;return"undefined"!=typeof StatsConfig&&StatsConfig.isStatPercentage&&s&&(h=StatsConfig.isStatPercentage(s)),{name:t.name,currentLevel:e.level,maxLevel:t.maxLevel,currentValue:i,nextValue:o,apCost:c,materials:d,statType:s,iconUrl:u,isPercentage:h,location:t.location||"Unknown"}},getAllRuneTypes:function(){const e=[];if(this.data.runeDefinitions)for(const[t,n]of Object.entries(this.data.runeDefinitions))n.variants.forEach((n=>{e.push({id:n.id,name:n.name,baseStatType:t})}));return e.sort(((e,t)=>e.name.localeCompare(t.name))),e}},document.addEventListener("DOMContentLoaded",(function(){})),function(){if(void 0!==window.KarmaRuneData)return;const e=document.getElementsByTagName("script"),t=(e[e.length-1].src,window.forceguidesPlannerData?forceguidesPlannerData.pluginUrl:"");var n;n=t+"js/karma-rune-system/karma-rune-data.js",new Promise(((e,t)=>{const s=document.createElement("script");s.src=n,s.onload=e,s.onerror=t,document.head.appendChild(s)}))}(),window.KarmaRunesSystem={isInitialized:!1,dataLoaded:!1,elements:{},selectionWindow:null,data:{totalSlots:8,selectedRunes:Array(8).fill(null),currentSlot:null,get runeDefinitions(){return window.KarmaRuneData&&window.KarmaRuneData.runeDefinitions?window.KarmaRuneData.runeDefinitions:{hp:{baseStatType:"hp",iconId:"hp",variants:[{id:"hp",name:"HP I (Default)",tier:1,maxLevel:20,valuePerLevel:[50,100,150,200,250,300,350,400,450,500],apCost:[3,4,5,7,9,12,15,19,23,28],location:"Default location",materials:[]}]}}},exampleRunes:[{slot:0,type:"maxCritRate",value:3},{slot:1,type:"critDmg",value:5},{slot:2,type:"ignoreResistCritDmg",value:5},{slot:3,type:"cancelIgnorePenetration",value:9},{slot:4,type:"ignoreResistSkillAmp",value:5},{slot:5,type:"ignoreEvasion",value:10},{slot:6,type:"normalDmgUp",value:10},{slot:7,type:"penetration",value:10},{slot:8,type:"penetration2",value:25},{slot:9,type:"ignorePenetration2",value:45},{slot:10,type:"dmgReduction2",value:36},{slot:11,type:"int2",value:12},{slot:12,type:"str2",value:12},{slot:13,type:"defenseRate2",value:150}]},init:function(){if(this.isInitialized)return;if(!window.KarmaRuneData||!window.KarmaRuneData.runeDefinitions)return void setTimeout((()=>this.init()),500);const e=document.getElementById("fg-karma-runes-system");e&&(this.elements.panel=e,this.initUI(),this.setupEventListeners(),this.waitForBuildPlanner(10),this.isInitialized=!0)},waitForBuildPlanner:function(e){const t=this;let n=0;!function s(){return n++,"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&void 0!==window.KarmaRuneData?(t.updateStats(),!0):n<e&&(setTimeout(s,200*n),!1)}()},refreshSystem:function(){this.ensureDataStructure(),this.refreshUI(),this.elements.runeSelector&&(this.elements.runeSelector.style.display="none"),this.updateStats()},ensureDataStructure:function(){if(this.data||(this.data={}),this.data.selectedRunes&&Array.isArray(this.data.selectedRunes)||(this.data.selectedRunes=[]),this.data.selectedRunes.length<this.data.totalSlots){const e=this.data.totalSlots-this.data.selectedRunes.length,t=Array(e).fill(null);this.data.selectedRunes=this.data.selectedRunes.concat(t)}for(let e=0;e<this.data.selectedRunes.length;e++){const t=this.data.selectedRunes[e];if(t&&(t.type||(t.type="unknown"),t.id||(t.id="unknown"),t.level||(t.level=1),!t.maxLevel)){let e=!1;for(const[n,s]of Object.entries(this.data.runeDefinitions)){const n=s.variants.find((e=>e.id===t.type));if(n){t.maxLevel=n.maxLevel||10,e=!0;break}}e||(t.maxLevel=10)}}},ensureAndSave:function(){if(this.ensureDataStructure(),"undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("karma-runes",e),!0}return!1},getEssentialData:function(){return this.ensureDataStructure(),{selectedRunes:this.data.selectedRunes.map((e=>e?{id:e.id||null,type:e.type||null,level:e.level||1,maxLevel:e.maxLevel||10}:null)),totalSlots:this.data.totalSlots||16}},saveToStore:function(){if("undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("karma-runes",e),!0}return!1},initUI:function(){const e=`\n            <div class="essence-runes-main-layout">\n                <div class="essence-runes-left-panel">\n                    <div class="essence-runes-container">\n                        <div class="essence-runes-header">\n                            <h2>Karma Runes System</h2>\n                        </div>\n\n                        <div class="essence-runes-slots-scroll">\n                            <div class="essence-runes-slots-container">\n                                \x3c!-- Slots will be generated here --\x3e\n                            </div>\n                        </div>\n\n                        <div class="essence-runes-slots-counter">\n                            Karma Runes Equipped: <span class="essence-runes-slots-count">0/${this.data.totalSlots}</span>\n                        </div>\n                    </div>\n                </div>\n\n                <div class="essence-runes-right-panel">\n                    <div class="essence-runes-container">\n                        <div class="essence-runes-details">\n                            <h3>Rune Details</h3>\n                            <div class="essence-runes-details-content">\n                                <div class="essence-rune-details-empty">Select a rune to view details</div>\n                            </div>\n                        </div>\n\n                        <div class="essence-runes-stats">\n                            <h3>Stats Summary</h3>\n                            <div class="essence-runes-stats-content">\n                                <div class="essence-rune-details-empty">No runes equipped yet</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        `;this.elements.panel.innerHTML=e,this.elements.slotsContainer=this.elements.panel.querySelector(".essence-runes-slots-container"),this.elements.slotsCount=this.elements.panel.querySelector(".essence-runes-slots-count"),this.elements.detailsContent=this.elements.panel.querySelector(".essence-runes-details-content"),this.elements.statsContent=this.elements.panel.querySelector(".essence-runes-stats-content"),this.selectionWindow=new SelectionWindowManager({id:"fg-karma-rune-selector",title:"Select Karma Rune",className:"fg-karma-rune-selector",fixedPosition:!0,onSelect:e=>{e.type&&this.selectRune(e.type)},onClose:()=>{this.data.currentSlot=null}}),"undefined"!=typeof QuickFillButtonConfigs&&(this.quickFillManager=QuickFillButtonConfigs.initializeSystem("karma-runes",this.elements.panel.querySelector(".essence-runes-container"))),this.data.selectedRunes&&Array.isArray(this.data.selectedRunes)||(this.data.selectedRunes=Array(this.data.maxSlots).fill(null)),this.loadFromStore(),this.generateSlots(),this.updateStats(),this.updateSlotsCounter(),this.updateStatsDisplay()},loadFromStore:function(){if(window.BuildSaverStore&&BuildSaverStore.dataLoaded){const e=BuildSaverStore.getSystemData("karma-runes");if(e&&e.selectedRunes)return this.loadFromData(e)}return!1},loadFromData:function(e){return!(!e||!e.selectedRunes||(this.data.selectedRunes=JSON.parse(JSON.stringify(e.selectedRunes)),e.totalSlots&&"number"==typeof e.totalSlots&&(this.data.totalSlots=e.totalSlots),setTimeout((()=>{this.updateStats(),"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStatsSummaryUI&&BuildPlanner.updateStatsSummaryUI(BuildPlanner.totalStats)}),300),0))},findRuneDefinition:function(e){if(!e)return null;for(const[t,n]of Object.entries(this.data.runeDefinitions)){const s=n.variants.find((t=>t.id===e));if(s)return{variant:s,definition:n,baseStatType:t,iconId:n.iconId||t}}return null},refreshUI:function(){this.refreshRuneSlots(),this.updateSlotsCounter(),this.updateStatsDisplay()},getRuneContentHTML:function(e,t){if(!e)return'<div class="essence-rune-content">Click to add rune</div>';const n=this.findRuneDefinition(e.type);if(!n)return'<div class="essence-rune-content">Unknown rune</div>';const{variant:s,definition:a,iconId:i,baseStatType:l}=n,o=(s.id,s.name),r=s.color||"#4caf50";StatsConfig.getStatInfo(a.baseStatType||i).isPercentage;let c="";if(s&&s.valuePerLevel){const t=Math.min(e.level-1,s.valuePerLevel.length-1),n=s.valuePerLevel[t];c=StatsConfig.formatStatValue(l,n)}else c=`Lv. ${e.level}`;const d=e.level>=s.maxLevel?"essence-rune-level-up disabled":"essence-rune-level-up";let u="";if("undefined"!=typeof StatsConfig)try{u=`\n                    <div class="essence-rune-icon">\n                        <img src="${StatsConfig.getStatIconUrl(i||e.type)}" alt="${o}" onerror="handleRuneIconError(this, '${r}')">\n                    </div>\n                `}catch(e){u=`\n                    <div class="essence-rune-icon">\n                        <div class="essence-rune-icon-inner" style="background-color: ${r}"></div>\n                    </div>\n                `}else u=`\n                <div class="essence-rune-icon">\n                    <div class="essence-rune-icon-inner" style="background-color: ${r}"></div>\n                </div>\n            `;return`\n            <div class="essence-rune-content">\n                ${u}\n                <div class="essence-rune-name" style="color: ${r}">\n                    ${o}\n                </div>\n                <div class="essence-rune-level">Lv. ${e.level}</div>\n                <div class="${d}" data-slot="${t}" title="Level up rune">+</div>\n                <div class="essence-rune-remove" data-slot="${t}" title="Remove rune">×</div>\n            </div>\n        `},updateStatsDisplay:function(){const e=this.generateStatsHTML();this.elements.statsContent&&(this.elements.statsContent.innerHTML=e)},removeRune:function(e){e<0||e>=this.data.maxSlots||(this.data.selectedRunes[e]=null,this.data.currentSlot===e&&(this.data.currentSlot=null,this.showEmptyDetails()),this.refreshUI(),this.updateStats(),this.saveToStore())},setupEventListeners:function(){const e=this;"function"!=typeof window.handleRuneIconError&&(window.handleRuneIconError=function(e,t){e.onerror=null,e.style.display="none",e.parentNode.innerHTML='<div class="essence-rune-icon-inner" style="background-color: '+t+'"></div>'}),this.elements.slotsContainer.addEventListener("click",(function(t){const n=t.target.closest(".essence-rune-slot");if(n){const s=parseInt(n.dataset.slot,10);if(t.target.classList.contains("essence-rune-remove"))return void e.removeRune(s);if(t.target.classList.contains("essence-rune-level-up")&&!t.target.classList.contains("disabled"))return void e.levelUpRune(s);e.data.selectedRunes[s]?e.showRuneDetails(s):e.showRuneSelector(s)}})),this.elements.detailsContent.addEventListener("click",(function(t){if(t.target.classList.contains("level-down-button")){const n=parseInt(t.target.dataset.slot,10);e.levelDownRune(n)}else if(t.target.classList.contains("level-up-button")){const n=parseInt(t.target.dataset.slot,10);e.levelUpRune(n)}}))},getUsedSlots:function(){return this.data.selectedRunes.filter((e=>null!==e)).length},generateSlotsHTML:function(){let e="";for(let t=0;t<this.data.totalSlots;t++){const n=this.data.selectedRunes[t];let s="essence-rune-slot";s+=n?" filled":" empty",e+=`\n                <div class="${s}" data-slot="${t}">\n                    <div class="essence-rune-slot-level">${t+1}</div>\n                    ${this.getRuneContentHTML(n,t)}\n                </div>\n            `}return e},generateStatsHTML:function(){const e={},t={};if(0===this.data.selectedRunes.filter((e=>null!==e)).length)return'<div class="essence-rune-stats-empty">No Essence Runes equipped yet</div>';this.data.selectedRunes.forEach((n=>{if(!n)return;const s=this.findRuneDefinition(n.type);if(!s)return;const{variant:a,definition:i,baseStatType:l}=s,o=Math.min(n.level-1,a.valuePerLevel.length-1),r=a.valuePerLevel[o];t[l]||(t[l]={baseStatType:l,iconId:i.iconId,isPercent:StatsConfig.getStatInfo(l).isPercentage}),e[l]||(e[l]=0),e[l]+=r}));let n="";return Object.keys(e).sort(((e,t)=>{const n=StatsConfig.getStatInfo(e).name||e,s=StatsConfig.getStatInfo(t).name||t;return n.localeCompare(s)})).forEach((s=>{const a=t[s],i=e[s];let l="";if("undefined"!=typeof StatsConfig)try{l=`<img src="${StatsConfig.getStatIconUrl(a.iconId||s)}" alt="${s}" class="essence-rune-stat-icon" onerror="this.style.display='none';">`}catch(e){}const o=StatsConfig.formatStatValue(s,i),r=StatsConfig.getStatInfo(s).name||this.formatStatName(s);n+=`\n                <div class="essence-rune-stat-item">\n                    <div class="essence-rune-stat-name">${l}${r}</div>\n                    <div class="essence-rune-stat-value">${o}</div>\n                </div>\n            `})),n},formatStatName:function(e){return e.replace(/([A-Z])/g," $1").replace(/^[a-z]/,(e=>e.toUpperCase())).replace(/ [a-z]/g,(e=>e.toUpperCase()))},showRuneSelector:function(e){if(this.data.currentSlot=e,!window.KarmaRuneData||!window.KarmaRuneData.runeDefinitions)return void this.selectionWindow.show({title:"Error Loading Runes",options:[{html:'<div class="fg-selection-empty-state">Error loading rune data. Please refresh the page.</div>',data:{}}]});const t=this.data.selectedRunes.filter((e=>null!==e)).map((e=>e.type)),n=[];Object.entries(window.KarmaRuneData.runeDefinitions).forEach((([e,t])=>{t.variants&&Array.isArray(t.variants)&&t.variants.forEach((s=>{n.push({id:s.id,name:s.name||e,color:s.color||"#4caf50",tier:s.tier||1,isPercent:StatsConfig.getStatInfo(e).isPercentage,baseStatType:t.baseStatType||e,iconId:t.iconId||e})}))})),n.sort(((e,t)=>e.tier!==t.tier?e.tier-t.tier:e.name.localeCompare(t.name)));const s=n.map((e=>{const n=t.includes(e.id);let s="";if("undefined"!=typeof StatsConfig)try{s=`<img src="${StatsConfig.getStatIconUrl(e.iconId)}" alt="${e.name}" class="fg-selection-option-icon" onerror="handleRuneIconError(this, '${e.color}')">`}catch(t){s=`<div class="fg-selection-option-icon-placeholder" style="background-color: ${e.color}; width: 20px; height: 20px; display: inline-block; margin-right: 8px;"></div>`}else s=`<div class="fg-selection-option-icon-placeholder" style="background-color: ${e.color}; width: 20px; height: 20px; display: inline-block; margin-right: 8px;"></div>`;return{html:`\n                    <div class="fg-selection-option-with-icon">\n                        ${s}\n                        <span class="fg-selection-option-name" style="color: ${e.color}">${e.name}</span>\n                    </div>\n                    <span class="fg-selection-option-action">${n?"Selected":"Select"}</span>\n                `,className:n?"disabled":"",data:{type:e.id,disabled:n}}}));this.selectionWindow.show({title:"Select Karma Rune",options:s})},hideRuneSelector:function(){this.selectionWindow.hide(),this.data.currentSlot=null},selectRune:function(e){if(null===this.data.currentSlot)return;const t=this.findRuneDefinition(e);t&&(this.data.selectedRunes[this.data.currentSlot]={type:e,id:e,level:1,maxLevel:t.variant.maxLevel},this.refreshRuneSlots(),this.hideRuneSelector(),this.updateStatsDisplay(),this.updateStats(),this.saveToStore())},updateUIAfterChange:function(e){this.refreshUI(),this.showRuneDetails(e),this.updateStats()},calculateStats:function(){const e={};return this.data.selectedRunes.forEach((t=>{if(!t)return;const n=this.findRuneDefinition(t.type);if(!n)return;const{variant:s,definition:a,baseStatType:i}=n,l=Math.min(t.level-1,s.valuePerLevel.length-1),o=s.valuePerLevel[l],r=a.baseStatType||i;e[r]?e[r]+=o:e[r]=o})),e},levelUpRune:function(e){const t=this.data.selectedRunes[e];if(!t)return;const n=this.findRuneDefinition(t.type);let s=10;n?s=n.variant.maxLevel:t.maxLevel&&(s=t.maxLevel),t.level<s&&(t.level++,this.updateUIAfterChange(e),this.saveToStore())},levelDownRune:function(e){const t=this.data.selectedRunes[e];t&&t.level>1&&(t.level--,this.updateUIAfterChange(e),this.showRuneDetails(e),this.saveToStore())},showRuneDetails:function(e){const t=this.data.selectedRunes[e];if(!this.elements.detailsContent)return;if(!t)return void(this.elements.detailsContent.innerHTML=`\n                <h3>Slot ${e+1} Details</h3>\n                <div class="essence-rune-details-empty">\n                    No rune equipped in this slot\n                </div>\n            `);const n=this.findRuneDefinition(t.type);if(!n)return void(this.elements.detailsContent.innerHTML=`\n                <div class="essence-rune-details-header">\n                    <div class="essence-rune-details-name" style="color: #ff0000">\n                        ${t.type} (Missing Definition)\n                    </div>\n                </div>\n                <div class="essence-rune-location">\n                    <strong>Location:</strong> Missing definition - update karma-rune-data.js\n                </div>\n            `);const{variant:s,definition:a,baseStatType:i,iconId:l}=n,o=StatsConfig.getStatInfo(i).name||s.name.replace(/ I+$/,""),r=Math.min(t.level-1,s.valuePerLevel.length-1),c=s.valuePerLevel[r],d=StatsConfig.formatStatValue(i,c),u=s.location||"Unknown";let h="";if("undefined"!=typeof StatsConfig)try{h=`<img src="${StatsConfig.getStatIconUrl(l)}" alt="${s.name}" class="essence-rune-stat-icon">`}catch(e){h=`<div class="essence-rune-icon-inner" style="background-color: ${s.color||"#4caf50"}"></div>`}const f=`\n            <div class="essence-rune-details-header">\n                <div class="essence-rune-details-name" style="color: ${s.color||"#4caf50"}">\n                    ${h} ${s.name}\n                </div>\n                <div class="essence-rune-details-value">\n                    ${o} +${d}\n                </div>\n            </div>\n            <div class="essence-rune-location">\n                <strong>Location:</strong> ${u}\n            </div>\n            <div class="essence-rune-level-controls">\n                <button class="${t.level<=1?"level-down-button disabled":"level-down-button"}" data-slot="${e}">-</button>\n                <span>Level: ${t.level}</span>\n                <button class="${t.level>=s.maxLevel?"level-up-button disabled":"level-up-button"}" data-slot="${e}">+</button>\n            </div>\n        `;this.elements.detailsContent.innerHTML=f},getRuneDescription:function(e,t){let n=!1,s=null;for(const[a,i]of Object.entries(this.data.runeDefinitions)){const a=i.variants.find((t=>t.id===e));if(a&&a.valuePerLevel){n=!0;const e=Math.min(t-1,a.valuePerLevel.length-1);s=a.valuePerLevel[e];break}}if(n&&null!==s)switch(e){case"exp":return`Increases experience gain by ${s}%. This affects all experience earned in-game.`;case"skillExp":return`Increases skill experience gain by ${s}%. This affects all skill experience earned in-game.`;case"partyExp":return`Increases party experience gain by ${s}%. This affects experience earned when in a party.`;case"petExp":return`Increases pet experience gain by ${s}%. This affects all pet experience earned in-game.`;case"alzDropAmount":return`Increases Alz drop amount by ${s}%. This affects the amount of Alz dropped from monsters.`;case"alzDropRate":return`Increases Alz drop rate by ${s}%. This affects the chance of Alz dropping from monsters.`;case"alzBombChance":return`Increases Alz Bomb chance by ${s}%. This affects the chance of getting a large Alz drop.`}return`${this.getRuneType(e).name} at level ${t}`},updateStats:function(){const e=this.calculateStats();"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats("karma-runes",e),this.updateStatsDisplay()},updateSlotsCounter:function(){this.elements.slotsCount.textContent=`${this.getUsedSlots()}/${this.data.totalSlots}`},generateSlots:function(){if(!window.KarmaRuneData||!window.KarmaRuneData.runeDefinitions)return void setTimeout((()=>this.generateSlots()),500);const e=this.generateSlotsHTML();this.elements.slotsContainer&&(this.elements.slotsContainer.innerHTML=e)},refreshRuneSlots:function(){const e=this.generateSlotsHTML();this.elements.slotsContainer&&(this.elements.slotsContainer.innerHTML=e)},showEmptyDetails:function(){this.elements.detailsContent.innerHTML='\n            <h3>Slot Details</h3>\n            <div class="essence-rune-details-empty">\n                No rune equipped in this slot\n            </div>\n        '},getRuneType:function(e){const t=this.findRuneDefinition(e);if(t){const{variant:e,baseStatType:n}=t;return{id:e.id,name:e.name,isPercent:StatsConfig.getStatInfo(n).isPercentage,color:e.color||"#4caf50"}}return{id:e,name:`Unknown Type: ${e}`,isPercent:!1,color:"#ff0000"}},maxAllRunes:function(){const e=[];for(const[t,n]of Object.entries(this.data.runeDefinitions))n.variants.forEach((t=>{e.push(t.id)}));const t=this.data.selectedRunes.filter((e=>null!==e)).map((e=>e.type)),n=e.filter((e=>!t.includes(e)));let s=0;for(const e of n){for(;s<this.data.totalSlots&&null!==this.data.selectedRunes[s];)s++;if(s>=this.data.totalSlots)break;let t=null,n=10;for(const[s,a]of Object.entries(this.data.runeDefinitions)){const s=a.variants.find((t=>t.id===e));if(s){t=s,n=s.maxLevel;break}}this.data.selectedRunes[s]={type:e,id:e,level:n,maxLevel:n},s++}for(let e=0;e<this.data.totalSlots;e++){const t=this.data.selectedRunes[e];if(t){let e=10;for(const[n,s]of Object.entries(this.data.runeDefinitions)){const n=s.variants.find((e=>e.id===t.type));if(n){e=n.maxLevel;break}}t.level=e}}this.refreshRuneSlots(),this.updateSlotsCounter(),this.updateStatsDisplay(),this.updateStats(),this.saveToStore()},resetAllRunes:function(){confirm("Are you sure you want to remove all karma runes? This cannot be undone.")&&(this.data.selectedRunes=Array(this.data.totalSlots).fill(null),this.refreshRuneSlots(),this.updateSlotsCounter(),this.updateStatsDisplay(),this.updateStats(),this.saveToStore())},getRuneDetails:function(e){if(!e)return null;let t=null,n=null,s=null;for(const[a,i]of Object.entries(this.data.runeDefinitions)){const l=i.variants.find((t=>t.id===e.type));if(l){t=l,n=i.iconId,s=a;break}}if(!t)return{name:"Unknown Rune",currentLevel:e.level,maxLevel:e.maxLevel||10,currentValue:0,nextValue:0,apCost:0,materials:[],statType:"unknown",iconUrl:null};const a=Math.min(e.level-1,t.valuePerLevel.length-1),i=t.valuePerLevel[a],l=Math.min(e.level,t.valuePerLevel.length-1),o=t.valuePerLevel[l],r=Math.min(e.level,t.apCost.length-1),c=t.apCost[r];let d=[];if(e.level<t.maxLevel&&t.materials&&t.materials.length>0){const n=Math.min(e.level+1,t.materials.length-1),s=t.materials[n];s&&s.name&&s.quantity>0&&d.push({name:s.name,quantity:s.quantity})}let u=null;"undefined"!=typeof StatsConfig&&StatsConfig.getStatIconUrl&&n&&(u=StatsConfig.getStatIconUrl(n));let h=!1;return"undefined"!=typeof StatsConfig&&StatsConfig.isStatPercentage&&s&&(h=StatsConfig.isStatPercentage(s)),{name:t.name,currentLevel:e.level,maxLevel:t.maxLevel,currentValue:i,nextValue:o,apCost:c,materials:d,statType:s,iconUrl:u,isPercentage:h,location:t.location||"Unknown"}},getAllRuneTypes:function(){const e=[];if(this.data.runeDefinitions)for(const[t,n]of Object.entries(this.data.runeDefinitions))n.variants.forEach((n=>{e.push({id:n.id,name:n.name,baseStatType:t})}));return e.sort(((e,t)=>e.name.localeCompare(t.name))),e}},document.addEventListener("DOMContentLoaded",(function(){})),window.EquipmentSystem={isInitialized:!1,elements:{},detailView:null,data:{slots:[{id:"Helmet",name:"Helmet",type:"armor"},{id:"Armor",name:"Armor",type:"armor"},{id:"Gloves",name:"Gloves",type:"armor"},{id:"Boots",name:"Boots",type:"armor"},{id:"Weapon1",name:"Weapon 1",type:"weapon"},{id:"Weapon2",name:"Weapon 2",type:"weapon"},{id:"Ring1",name:"Ring 1",type:"accessory"},{id:"Ring2",name:"Ring 2",type:"accessory"},{id:"Ring3",name:"Ring 3",type:"accessory"},{id:"Ring4",name:"Ring 4",type:"accessory"},{id:"Amulet",name:"Amulet",type:"accessory"},{id:"Earring1",name:"Earring 1",type:"accessory"},{id:"Earring2",name:"Earring 2",type:"accessory"},{id:"Bracelet1",name:"Bracelet 1",type:"accessory"},{id:"Bracelet2",name:"Bracelet 2",type:"accessory"},{id:"Bike",name:"Bike",type:"vehicle"},{id:"Epaulet",name:"Epaulet",type:"accessory"},{id:"Belt",name:"Belt",type:"accessory"},{id:"Carnelian",name:"Carnelian",type:"accessory"},{id:"Brooch",name:"Brooch",type:"accessory"},{id:"Charm",name:"Charm",type:"accessory"},{id:"Talisman",name:"Talisman",type:"accessory"},{id:"Effector1",name:"Effector 1",type:"effector"},{id:"Effector2",name:"Effector 2",type:"effector"},{id:"Effector3",name:"Effector 3",type:"effector"},{id:"Effector4",name:"Effector 4",type:"effector"},{id:"Arcana1",name:"Arcana 1",type:"arcana"},{id:"Arcana2",name:"Arcana 2",type:"arcana"}],availableItems:{weapons:[],armor:[],accessories:[]},equippedItems:{},selectedSlot:null,weaponUpgrades:{activeWeapon:null,settings:{grade:0,epicOption:{id:null,level:0},activeSlots:1,slotOptions:[],extremeLevel:0,divineLevel:0,weaponId:null},items:{}},uiImagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/equipment_ui_base.png",clickableRegions:{Helmet:{left:197.111083984375,top:123.99652099609375,right:361.111083984375,bottom:246.99652099609375,width:164,height:123},Bike:{left:372.111083984375,top:126.99652099609375,right:535.111083984375,bottom:184.99652099609375,width:163,height:58},Epaulet:{left:369.111083984375,top:191.99652099609375,right:538.111083984375,bottom:246.99652099609375,width:169,height:55},Belt:{left:369.111083984375,top:253.99652099609375,right:537.111083984375,bottom:308.99652099609375,width:168,height:55},Carnelian:{left:197.111083984375,top:253.99652099609375,right:362.111083984375,bottom:308.99652099609375,width:165,height:55},Amulet:{left:22.111083984375,top:124.99652099609375,right:188.111083984375,bottom:183.99652099609375,width:166,height:59},Armor:{left:196.111083984375,top:312.99652099609375,right:361.111083984375,bottom:487.99652099609375,width:165,height:175},Weapon2:{left:371.111083984375,top:314.99652099609375,right:535.111083984375,bottom:488.99652099609375,width:164,height:174},Weapon1:{left:23.111083984375,top:313.99652099609375,right:187.111083984375,bottom:489.99652099609375,width:164,height:176},Boots:{left:371.111083984375,top:494.1076354980469,right:534.111083984375,bottom:602.1076354980469,width:163,height:108},Gloves:{left:23.111083984375,top:492.1076354980469,right:187.111083984375,bottom:601.1076354980469,width:164,height:109},Earring1:{left:197.111083984375,top:493.1076354980469,right:278.111083984375,bottom:542.1076354980469,width:81,height:49},Earring2:{left:282.111083984375,top:495.1076354980469,right:363.111083984375,bottom:543.1076354980469,width:81,height:48},Bracelet1:{left:197.111083984375,top:554.1076354980469,right:279.111083984375,bottom:600.1076354980469,width:82,height:46},Bracelet2:{left:284.111083984375,top:555.1076354980469,right:362.111083984375,bottom:599.1076354980469,width:78,height:44},Brooch:{left:196.111083984375,top:611.2187461853027,right:361.111083984375,bottom:669.2187461853027,width:165,height:58},Effector2:{left:371.111083984375,top:617.2187461853027,right:537.111083984375,bottom:675.2187461853027,width:166,height:58},Effector1:{left:23.111083984375,top:617.2187461853027,right:188.111083984375,bottom:675.2187461853027,width:165,height:58},Effector3:{left:23.111083984375,top:684.2187461853027,right:187.111083984375,bottom:742.2187461853027,width:164,height:58},Effector4:{left:372.111083984375,top:684.2187461853027,right:537.111083984375,bottom:739.2187461853027,width:165,height:55},Charm:{left:196.111083984375,top:679.2187461853027,right:362.111083984375,bottom:736.2187461853027,width:166,height:57},Arcana1:{left:369.111083984375,top:745.2187461853027,right:535.111083984375,bottom:805.2187461853027,width:166,height:64},Arcana2:{left:24.111083984375,top:745.2187461853027,right:188.111083984375,bottom:805.2187461853027,width:164,height:60},Talisman:{left:198.111083984375,top:745.2187461853027,right:362.111083984375,bottom:803.2187461853027,width:164,height:58},Ring1:{left:109.111083984375,top:193.21874618530273,right:189.111083984375,bottom:247.21874618530273,width:80,height:54},Ring2:{left:23.111083984375,top:191.21874618530273,right:105.111083984375,bottom:246.21874618530273,width:82,height:55},Ring3:{left:108.111083984375,top:253.21874618530273,right:188.111083984375,bottom:307.21874618530273,width:80,height:54},Ring4:{left:23.111083984375,top:254.21874618530273,right:104.111083984375,bottom:307.21874618530273,width:81,height:53}}},init:function(){if(this.isInitialized)return;const e=document.getElementById("fg-equipment-system");e&&(this.elements.panel=e,"undefined"!=typeof StatsConfig?(this.loadEquipmentData(),this.initUI(),this.detailView=window.EquipmentDetailView.init(this),this.setupEventListeners(),this.createClickableRegions(),this.showTooltipGuide(),this.loadFromStore(),this.isInitialized=!0,this.addActivationListener(),this.waitForBuildPlanner(10)):setTimeout((()=>{this.isInitialized||this.init()}),100))},addActivationListener:function(){if(window.BuildPlanner&&BuildPlanner.showActiveSystem){const e=BuildPlanner.showActiveSystem;BuildPlanner.showActiveSystem=function(){const t=e.call(this);return"equipment"===this.activeSystem&&window.EquipmentSystem&&setTimeout((()=>{window.EquipmentSystem.renderEquippedItems()}),50),t}}},loadEquipmentData:function(){"undefined"!=typeof EquipmentData&&"function"==typeof EquipmentData.init&&EquipmentData.init(),"undefined"!=typeof EquipmentData&&EquipmentData.items&&EquipmentData.items.weapons&&(this.data.availableItems.weapons=EquipmentData.items.weapons),"undefined"!=typeof EquipmentData&&EquipmentData.items&&(this.data.availableItems.armor=[...EquipmentData.items.armor||[],...EquipmentData.items.helmets||[],...EquipmentData.items.gloves||[],...EquipmentData.items.boots||[]]),"undefined"!=typeof EquipmentData&&EquipmentData.items&&(this.data.availableItems.accessories=[...EquipmentData.items.rings||[],...EquipmentData.items.earrings||[],...EquipmentData.items.bracelets||[],...EquipmentData.items.amulets||[],...EquipmentData.items.belts||[],...EquipmentData.items.carnelians||[],...EquipmentData.items.talismans||[],...EquipmentData.items.epaulets||[]])},initUI:function(){const e=`\n            <div class="fg-equipment-container">\n                <div class="fg-equipment-image-container">\n                    <div class="fg-equipment-image-overlay" id="fg-equipment-overlay"></div>\n                    <img src="${this.data.uiImagePath}" class="fg-equipment-ui-image" id="fg-equipment-ui-image" alt="Equipment UI">\n                    <div class="fg-equipment-equipped-items-container"></div>\n                </div>\n\n                <div class="fg-equipment-details-panel">\n                    <h3 class="fg-equipment-details-title">Equipment Details</h3>\n                    <div class="fg-equipment-details-content">\n                        <p class="fg-equipment-details-placeholder">Select an equipment slot to view details</p>\n                    </div>\n                </div>\n            </div>\n\n            \x3c!-- Modal for equipment selection (initially hidden) --\x3e\n            <div class="fg-equipment-modal" id="fg-equipment-modal">\n                <div class="fg-equipment-modal-content">\n                    <span class="fg-equipment-modal-close">&times;</span>\n                    <h3 id="fg-equipment-modal-title">Equipment Selection</h3>\n                    <div id="fg-equipment-modal-body">\n                        <p>Select an item to equip in this slot.</p>\n                    </div>\n                </div>\n            </div>\n        `;this.elements.panel.innerHTML=e,this.elements.imageContainer=this.elements.panel.querySelector(".fg-equipment-image-container"),this.elements.uiImage=document.getElementById("fg-equipment-ui-image"),this.elements.overlay=document.getElementById("fg-equipment-overlay"),this.elements.equippedItemsContainer=this.elements.panel.querySelector(".fg-equipment-equipped-items-container"),this.elements.detailsPanel=this.elements.panel.querySelector(".fg-equipment-details-panel"),this.elements.detailsTitle=this.elements.panel.querySelector(".fg-equipment-details-title"),this.elements.detailsContent=this.elements.panel.querySelector(".fg-equipment-details-content"),this.elements.modal=document.getElementById("fg-equipment-modal"),this.elements.modalTitle=document.getElementById("fg-equipment-modal-title"),this.elements.modalBody=document.getElementById("fg-equipment-modal-body"),this.elements.modalClose=this.elements.modal.querySelector(".fg-equipment-modal-close")},showTooltipGuide:function(){const e=document.createElement("div");e.className="fg-equipment-tooltip",e.textContent="Click on equipment slots to view details and equip items",this.elements.imageContainer.appendChild(e),setTimeout((()=>{e&&e.parentNode&&e.parentNode.removeChild(e)}),5e3)},setupEventListeners:function(){this.elements.overlay.addEventListener("click",(e=>{const t=this.elements.uiImage.getBoundingClientRect(),n=e.clientX-t.left,s=e.clientY-t.top,a=this.findClickedRegion(n,s);if(a){const e=this.data.slots.find((e=>e.id===a));e&&this.selectSlot(a,e.type)}})),window.addEventListener("resize",(()=>{this.updateClickableRegionsPositions(),this.updateEquippedItemsPositions()})),this.elements.modalClose&&this.elements.modalClose.addEventListener("click",(()=>{this.closeEquipmentModal()})),window.addEventListener("click",(e=>{e.target===this.elements.modal&&this.closeEquipmentModal()}))},createClickableRegions:function(){const e=this.data.clickableRegions;for(const t in e){const e=this.data.slots.find((e=>e.id===t))||{id:t,name:t,type:"equipment"},n=document.createElement("div");n.id=`fg-rect-${t}`,n.className="fg-equipment-region",n.setAttribute("data-slot-id",t),n.setAttribute("data-slot-type",e.type),this.elements.overlay.appendChild(n)}this.elements.uiImage.complete?(this.updateClickableRegionsPositions(),this.renderEquippedItems()):this.elements.uiImage.onload=()=>{this.updateClickableRegionsPositions(),this.renderEquippedItems()}},updateClickableRegionsPositions:function(){const e=this.elements.uiImage.getBoundingClientRect(),t=e.width/560,n=e.height/820,s=this.data.clickableRegions;for(const e in s){const a=s[e],i=document.getElementById(`fg-rect-${e}`);if(i){a.left,a.right,a.top,a.bottom;const e=a.left*t,s=a.top*n,l=a.width*t,o=a.height*n;i.style.left=`${e}px`,i.style.top=`${s}px`,i.style.width=`${l}px`,i.style.height=`${o}px`}}this._resizeListenerAdded||(window.addEventListener("resize",(()=>{this.updateClickableRegionsPositions()})),this._resizeListenerAdded=!0)},findClickedRegion:function(e,t){const n=this.elements.uiImage.getBoundingClientRect(),s=e*(560/n.width),a=t*(820/n.height),i=this.data.clickableRegions;for(const e in i){const t=i[e];if(s>=t.left&&s<=t.right&&a>=t.top&&a<=t.bottom)return e}return null},renderEquippedItems:function(){if(this.elements.equippedItemsContainer){this.elements.equippedItemsContainer.innerHTML="";for(const e in this.data.equippedItems){const t=this.data.equippedItems[e];this.renderEquippedItem(e,t)}}},renderEquippedItem:function(e,t){const n=this.data.clickableRegions[e];if(!n)return;const s=document.createElement("div");s.className="fg-equipment-item",s.setAttribute("data-slot-id",e),s.setAttribute("data-item-id",t.id);const a=document.createElement("img");a.src=t.imagePath,a.alt=t.name,a.className="fg-equipment-item-image",s.appendChild(a),this.elements.equippedItemsContainer.appendChild(s),this.positionEquippedItem(s,n)},positionEquippedItem:function(e,t){const n=this.elements.uiImage.getBoundingClientRect(),s=n.width/560,a=n.height/820,i=t.left*s,l=t.top*a,o=t.width*s,r=t.height*a;e.style.left=`${i}px`,e.style.top=`${l}px`,e.style.width=`${o}px`,e.style.height=`${r}px`},updateEquippedItemsPositions:function(){this.elements.equippedItemsContainer.querySelectorAll(".fg-equipment-item").forEach((e=>{const t=e.getAttribute("data-slot-id"),n=this.data.clickableRegions[t];n&&this.positionEquippedItem(e,n)}))},getSlotTypeIcon:function(e){return{weapon:"fas fa-sword",armor:"fas fa-shield-alt",accessory:"fas fa-gem",vehicle:"fas fa-motorcycle",effector:"fas fa-bolt",arcana:"fas fa-hat-wizard"}[e]||"fas fa-circle"},selectSlot:function(e,t){let n=this.data.slots.find((t=>t.id===e));n||(n={id:e,name:e,type:t||"equipment"});const s=!!this.data.equippedItems[e],a=this.data.selectedSlot&&this.data.selectedSlot.id===e;this.data.selectedSlot=n,this.elements.overlay.querySelectorAll(".fg-selected-region").forEach((e=>{e.classList.remove("fg-selected-region")}));const i=document.getElementById(`fg-rect-${e}`);if(i&&i.classList.add("fg-selected-region"),this.detailView.updateDetailsPanel(n),s&&!a)return this.data.lastSlotClickTime=Date.now(),void(this.data.lastClickedSlotId=e);const l=a&&this.data.lastClickedSlotId===e&&Date.now()-this.data.lastSlotClickTime<1500;s&&!l||this.openEquipmentModal(e,n.type)},getItemCategoryForSlot:function(e){return e.startsWith("Ring")?"rings":e.startsWith("Earring")?"earrings":e.startsWith("Bracelet")?"bracelets":e.startsWith("Weapon")?"weapons":e.startsWith("Effector")?"effectors":e.startsWith("Arcana")?"arcanas":{Bike:"bikes",Epaulet:"epaulets",Belt:"belts",Carnelian:"carnelians",Talisman:"talismans",Amulet:"amulets",Helmet:"helmets",Armor:"armor",Gloves:"gloves",Boots:"boots"}[e]||null},openEquipmentModal:function(e,t){const n=this.data.slots.find((t=>t.id===e))||{id:e,name:e,type:t};this.elements.modalTitle.textContent=`Select ${n.name}`;let s=[];const a=this.getItemCategoryForSlot(e);if(a&&EquipmentData.items[a])s=EquipmentData.items[a];else switch(t){case"weapon":s=this.data.availableItems.weapons;break;case"armor":s=this.data.availableItems.armor;break;case"accessory":s=this.data.availableItems.accessories;break;default:s=[]}let i="";s.length>0?(i='<div class="fg-item-selection-grid">',s.forEach((e=>{i+=`\n                    <div class="fg-item-selection-card" data-item-id="${e.id}">\n                        <img src="${e.imagePath}" alt="${e.name}" class="fg-item-selection-image">\n                        <div class="fg-item-selection-name">${e.name}</div>\n                    </div>\n                `})),i+="</div>"):i=`<p>No ${n.name} items available for this slot.</p>`,this.elements.modalBody.innerHTML=`\n            ${i}\n            <div class="fg-modal-buttons">\n                <button class="fg-button fg-modal-close-btn">Cancel</button>\n            </div>\n        `,this.elements.modalBody.querySelectorAll(".fg-item-selection-card").forEach((t=>{t.addEventListener("click",(()=>{const n=t.getAttribute("data-item-id"),a=s.find((e=>e.id===n));a&&(this.handleItemSelect(e,n,a),this.closeEquipmentModal())}))}));const l=this.elements.modalBody.querySelector(".fg-modal-close-btn");l&&l.addEventListener("click",(()=>{this.closeEquipmentModal()})),this.elements.modal.style.display="block"},closeEquipmentModal:function(){this.elements.modal&&(this.elements.modal.style.display="none")},handleItemSelect:function(e,t,n){if(!e||!t)return;const s=JSON.parse(JSON.stringify(n));s.slotId=e,this.data.equippedItems[e]=s,"weapon"===s.type&&(this.data.weaponUpgrades.settings.weaponId=s.id,this.data.weaponUpgrades.activeWeapon=s.id,this.data.weaponUpgrades.settings.weaponId!==s.id&&(this.data.weaponUpgrades.settings={weaponId:s.id,grade:0,epicOption:{id:null,level:0},activeSlots:1,slotOptions:new Array(3).fill(null),extremeLevel:0,divineLevel:0})),this.renderEquippedItem(e,s),this.data.slots.find((t=>t.id===e))&&this.selectItem(e,s),this.updateStats()},removeEquippedItem:function(e){if(this.data.equippedItems[e]){delete this.data.equippedItems[e];const t=this.elements.equippedItemsContainer.querySelector(`.fg-equipment-item[data-slot-id="${e}"]`);t&&t.remove();const n=this.data.slots.find((t=>t.id===e));n&&this.detailView.updateDetailsPanel(n),this.updateStats()}},calculateStats:function(){const e={};for(const t in this.data.equippedItems){const n=this.data.equippedItems[t];if(n){this.data.weaponUpgrades.items||(this.data.weaponUpgrades.items={}),this.data.weaponUpgrades.items[t]||(this.data.weaponUpgrades.items[t]={weaponId:n.id,grade:0,epicOption:{id:null,level:0},activeSlots:1,slotOptions:new Array(3).fill(null),extremeLevel:0,divineLevel:0,chaosLevel:0});const s=this.data.weaponUpgrades.items[t];if("weapon"===n.type)this.addWeaponStatsToTotal(e,n,s);else if("belt"===n.type)this.addBeltStatsToTotal(e,n,s);else if(["earring","bracelet","amulet"].includes(n.type))this.addAccessoryStatsToTotal(e,n,s);else if(["armor","helmet","gloves","boots"].includes(n.type))this.addArmorStatsToTotal(e,n,s);else if(["arcana","carnelian","talisman","epaulet"].includes(n.type))this.addBaseUpgradeStatsToTotal(e,n,s);else if(n.baseStats)for(const t in n.baseStats){const s=n.baseStats[t];e[t]||(e[t]=0),e[t]+=s}}}return e},addAccessoryStatsToTotal:function(e,t,n){if(t.baseStats)for(const n in t.baseStats){const s=t.baseStats[n];e[n]||(e[n]=0),e[n]+=s}if(n.chaosLevel>0){const s=t.chaosTier||"gold";if(window.EquipmentData&&EquipmentData.chaosUpgrades&&EquipmentData.chaosUpgrades[s]&&n.chaosLevel<EquipmentData.chaosUpgrades[s].length){const t=EquipmentData.chaosUpgrades[s][n.chaosLevel];Array.isArray(t)&&t.forEach((t=>{e[t.stat]?e[t.stat]+=t.value:e[t.stat]=t.value}))}}},addWeaponStatsToTotal:function(e,t,n){const s=t.subtype||t.material||"orb";if(t.baseStats){if(t.baseStats.attack){const a=t.baseStats.attack,i=window.WeaponsData?WeaponsData.getUpgradeStat(s,"attack",n.grade):0;e.attack=(e.attack||0)+a+i}if(t.baseStats.magicAttack){const a=t.baseStats.magicAttack,i=window.WeaponsData?WeaponsData.getUpgradeStat(s,"magicAttack",n.grade):0;e.magicAttack=(e.magicAttack||0)+a+i}if(t.baseStats.attackRate){const a=t.baseStats.attackRate,i=window.WeaponsData?WeaponsData.getUpgradeStat(s,"attackRate",n.grade):0;e.attackRate=(e.attackRate||0)+a+i}for(const n in t.baseStats)["attack","magicAttack","attackRate"].includes(n)||(e[n]||(e[n]=0),e[n]+=t.baseStats[n])}const a=EquipmentData.epicOptions.find((e=>e.id===n.epicOption.id));if(a){const t=a.levels[n.epicOption.level]?.value||0,s=a.id;e[s]||(e[s]=0),e[s]+=t}for(let t=0;t<n.activeSlots;t++){const s=n.slotOptions[t];if(s){const t=EquipmentData.slotOptions.find((e=>e.id===s));if(t){const n=t.id;e[n]||(e[n]=0),e[n]+=t.value}}}const i=["greatsword","daikatana"].includes(t.subtype);if(n.extremeLevel>0&&EquipmentData.extremeUpgrades){const t=EquipmentData.extremeUpgrades.level7||[];n.extremeLevel<t.length&&t[n.extremeLevel].forEach((t=>{e[t.stat]?e[t.stat]+=i?2*t.value:t.value:e[t.stat]=i?2*t.value:t.value}))}if(n.divineLevel>0){const t=window.WeaponsData&&WeaponsData.divineUpgrades?WeaponsData.divineUpgrades.high:null;if(t)for(const s in t){const a=t[s];if(a&&n.divineLevel<a.length){const t=a[n.divineLevel];t>0&&(e[s]||(e[s]=0),e[s]+=t)}}}},addArmorStatsToTotal:function(e,t,n){const s=t.material||t.type,a=EquipmentData.gradeUpgrades[s]||EquipmentData.gradeUpgrades.plate||[];let i={defenseMod:1,magicDefenseMod:1,hpMod:1};if(a[n.grade]&&(i=a[n.grade]),t.baseStats){if(t.baseStats.defense){const n=Math.floor(t.baseStats.defense*(i.defenseMod||1));e.defense=(e.defense||0)+n}if(t.baseStats.magicDefense){const n=Math.floor(t.baseStats.magicDefense*(i.magicDefenseMod||1));e.magicDefense=(e.magicDefense||0)+n}if(t.baseStats.hpMax){const n=Math.floor(t.baseStats.hpMax*(i.hpMod||1));e.hpMax=(e.hpMax||0)+n}for(const n in t.baseStats)["defense","magicDefense","hpMax"].includes(n)||(e[n]||(e[n]=0),e[n]+=t.baseStats[n])}if(n.extremeLevel>0&&EquipmentData.extremeUpgrades){const t=EquipmentData.extremeUpgrades.level7||[];if(n.extremeLevel<t.length){const s=t[n.extremeLevel];Array.isArray(s)&&s.forEach((t=>{e[t.stat]?e[t.stat]+=t.value:e[t.stat]=t.value}))}}if(n.divineLevel>0){const t=window.WeaponsData&&WeaponsData.divineUpgrades?WeaponsData.divineUpgrades.high:null;if(t)for(const s in t){const a=t[s];if(a&&n.divineLevel<a.length){const t=a[n.divineLevel];t>0&&(e[s]||(e[s]=0),e[s]+=t)}}}},addBaseUpgradeStatsToTotal:function(e,t,n){if(t.baseStats){const s=((EquipmentData.baseUpgrades.generic||[])[n.grade]||{statMod:1}).statMod||1;for(const n in t.baseStats){const a=Math.floor(t.baseStats[n]*s);e[n]||(e[n]=0),e[n]+=a}}},updateStats:function(){const e=this.calculateStats();"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats("equipment",e)},waitForBuildPlanner:function(e){const t=this;let n=0;!function s(){return n++,"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats?(t.updateStats(),!0):n<e&&(setTimeout(s,200*n),!1)}()},loadFromStore:function(){if(window.BuildSaverStore&&BuildSaverStore.dataLoaded){const e=BuildSaverStore.getSystemData("equipment");if(e)return this.loadFromData(e)}return!1},loadFromData:function(e){if(e){if(this.ensureDataStructure(),e.equippedItems){this.data.equippedItems={};for(const t in e.equippedItems){const n=e.equippedItems[t];n&&n.id&&(this.data.equippedItems[t]=JSON.parse(JSON.stringify(n)))}}return e.weaponUpgrades&&(e.weaponUpgrades.items&&(this.data.weaponUpgrades.items=JSON.parse(JSON.stringify(e.weaponUpgrades.items))),e.weaponUpgrades.settings&&(this.data.weaponUpgrades.settings=JSON.parse(JSON.stringify(e.weaponUpgrades.settings)))),this.isInitialized?this.renderEquippedItems():setTimeout((()=>{this.renderEquippedItems()}),100),this.updateStats(),this.data.selectedSlot&&this.data.equippedItems[this.data.selectedSlot.id]&&"weapon"===this.data.equippedItems[this.data.selectedSlot.id].type&&this.detailView&&this.detailView.updateWeaponStats&&this.detailView.updateWeaponStats(),!0}return!1},getEssentialData:function(){this.ensureDataStructure();const e={};for(const t in this.data.equippedItems){const n=this.data.equippedItems[t];n&&(e[t]={id:n.id,name:n.name,type:n.type,imagePath:n.imagePath,baseStats:n.baseStats?JSON.parse(JSON.stringify(n.baseStats)):{},upgradeData:n.upgradeData||null,slotId:n.slotId})}return{equippedItems:e,weaponUpgrades:{items:JSON.parse(JSON.stringify(this.data.weaponUpgrades.items||{})),settings:JSON.parse(JSON.stringify(this.data.weaponUpgrades.settings||{}))}}},ensureDataStructure:function(){this.data.equippedItems||(this.data.equippedItems={}),this.data.weaponUpgrades||(this.data.weaponUpgrades={activeWeapon:null,settings:{grade:0,epicOption:{id:null,level:0},activeSlots:1,slotOptions:[],extremeLevel:0,divineLevel:0,weaponId:null},items:{}}),this.data.weaponUpgrades.items||(this.data.weaponUpgrades.items={}),this.data.weaponUpgrades.settings||(this.data.weaponUpgrades.settings={grade:0,epicOption:{id:null,level:0},activeSlots:1,slotOptions:[],extremeLevel:0,divineLevel:0,weaponId:null})},saveToStore:function(){if(this.ensureDataStructure(),"undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("equipment",e),!0}return!1},refreshUI:function(){this.data.selectedSlot&&this.detailView.updateDetailsPanel(this.data.selectedSlot),this.renderEquippedItems()},selectItem:function(e,t){if(!this.detailView)return;const n=this.data.slots.find((t=>t.id===e));n&&(this.data.selectedSlot=n,t&&"weapon"===t.type&&(this.data.weaponUpgrades.settings.weaponId=t.id),this.detailView.updateDetailsPanel(n))},addBeltStatsToTotal:function(e,t,n){if(t.baseStats)for(const n in t.baseStats){const s=t.baseStats[n];e[n]||(e[n]=0),e[n]+=s}if(window.BeltsData&&t.upgradeData){const s=window.BeltsData[t.upgradeData];if(s&&Array.isArray(s.upgrades)){const t=n.grade||0;if(t>=0&&t<s.upgrades.length){const n=s.upgrades[t];for(const t in n){const s=n[t];s&&(e[t]||(e[t]=0),e[t]+=s)}}}}},isPercentStat:function(e){return"undefined"!=typeof StatsConfig?StatsConfig.getStatInfo(e).isPercentage||!1:["critRate","critDamage","swordSkillAmp","magicSkillAmp","resistCritRate","resistCritDamage","resistSkillAmp"].includes(e)},formatStatName:function(e){return"undefined"!=typeof StatsConfig&&StatsConfig.getStatInfo(e).name||e}},function(){const e=document.createElement("style");e.textContent="\n        .fg-costume-slot-small {\n            min-height: 45px;\n            max-height: 52px;\n            aspect-ratio: 1/1;\n            display: inline-flex;\n            justify-content: center;\n            align-items: center;\n            margin: 5px;\n            vertical-align: top;\n            overflow: hidden;\n        }\n\n        .fg-costume-slot-small .fg-costume-slot-stat-icon {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            padding: 0;\n        }\n\n        .fg-costume-slot-small .fg-costume-slot-stat-icon img {\n            width: 100%;\n            height: 100%;\n            object-fit: contain;\n        }\n\n        .fg-costume-slot-small .fg-costume-slot-empty {\n            font-size: 24px;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            width: 100%;\n            height: 100%;\n            color: #aaa;\n        }\n\n        .fg-costume-slot-remove {\n            position: absolute;\n            top: 2px;\n            left: 2px;\n            width: 16px;\n            height: 16px;\n            background: rgba(187, 66, 66, 0.7);\n            color: white;\n            border: none;\n            border-radius: 50%;\n            font-size: 12px;\n            line-height: 1;\n            padding: 0;\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            transition: background-color 0.2s;\n            z-index: 5;\n        }\n\n        .fg-costume-slot-stat-value-below {\n            text-align: center;\n            font-size: 12px;\n            margin-top: 2px;\n            height: 16px;\n            color: #33ff33;\n            display: block;\n            width: 40px;\n            position: relative;\n        }\n\n        .fg-costume-slots {\n            display: flex;\n            align-items: flex-start;\n        }\n\n        /* Wrapper for slot and its value */\n        .fg-costume-slot-wrapper {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            margin-right: 10px;\n        }\n    ",document.head.appendChild(e)}(),function(){if(void 0!==window.CostumeData)return;const e=document.createElement("script"),t=document.getElementsByTagName("script"),n=(t[t.length-1].src,window.forceguidesPlannerData?forceguidesPlannerData.pluginUrl:"");e.src=n+"js/costume-system/costume-data.js",document.head.appendChild(e)}(),window.CostumesSystem={isInitialized:!1,elements:{},costumeTypes:["generic","forceWing","vehicle"],selectedStats:{generic:Array(3).fill(null),forceWing:Array(3).fill(null),vehicle:Array(3).fill(null)},epicCraftOptions:{generic:null,forceWing:null,vehicle:null},currentSlot:{type:null,index:null},init:function(){if(this.isInitialized)return;const e=document.getElementById("fg-costumes-system");e&&(this.elements.panel=e,"undefined"!=typeof CostumeData&&"undefined"!=typeof StatsConfig?("undefined"!=typeof StatIntegrationService&&StatIntegrationService.addSummaryStyles&&StatIntegrationService.addSummaryStyles(),this.createStatSelectionPopup(),this.initUI(),this.loadFromStore(),this.setupEventListeners(),this.isInitialized=!0,this.updateCostumeStats(),window.BuildPlanner&&"function"==typeof BuildPlanner.refreshAllSystemStats&&setTimeout((()=>BuildPlanner.refreshAllSystemStats()),200)):setTimeout((()=>this.init()),100))},initUI:function(){this.createCostumeSystemUI()},createCostumeSystemUI:function(){if(!this.elements.panel)return;let e="";this.costumeTypes.forEach((t=>{const n=this.getCostumeDisplayName(t);e+=`\n                <div class="fg-costume-row" data-costume-type="${t}">\n                    <div class="fg-costume-name">\n                        <span class="fg-costume-name-prefix">[${n}]</span>\n                    </div>\n                    <div class="fg-costume-slots">\n                        ${this.createSlotHTML(t,3)}\n                    </div>\n                    <div class="fg-costume-epic-craft">\n                        ${this.createEpicCraftDropdown(t)}\n                    </div>\n                </div>\n            `}));const t=`\n            <div class="fg-costume-system-container">\n                <h2>Costume System</h2>\n\n                \x3c!-- Costume rows --\x3e\n                <div class="fg-costume-rows-container">\n                    ${e}\n                </div>\n\n                \x3c!-- Selected stats summary --\x3e\n                <div class="fg-costume-selected-stats">\n                    <h3>Selected Costume Stats</h3>\n                    <div class="fg-costume-selected-list">\n                        \x3c!-- Selected stats will be shown here --\x3e\n                    </div>\n                </div>\n            </div>\n        `;this.elements.panel.innerHTML=t,this.elements.selectedList=this.elements.panel.querySelector(".fg-costume-selected-list"),setTimeout((()=>{this.setupSlotClickHandlers(),this.setupEpicCraftDropdowns()}),0)},getCostumeDisplayName:function(e){switch(e){case"generic":return"Costume";case"forceWing":return"Wing Costume";case"vehicle":return"Vehicle Costume";default:return"Unknown Costume"}},createSlotHTML:function(e,t){let n="";for(let s=0;s<t;s++){const t=null!==this.selectedStats[e][s];if(n+='<div class="fg-costume-slot-wrapper">',n+=`\n                <div class="fg-costume-slot ${t?"selected":"empty"} fg-costume-slot-small"\n                     data-costume-type="${e}"\n                     data-slot-index="${s}">\n                `,t){const t=this.selectedStats[e][s],a=StatsConfig.getStatInfo(t.id),i=a?a.name:t.id;n+=`\n                    <div class="fg-costume-slot-stat">\n                        <div class="fg-costume-slot-stat-icon">\n                            <img src="${StatsConfig.getStatIconUrl(t.id)}" alt="${i}">\n                        </div>\n                        <div class="fg-costume-slot-remove" data-costume-type="${e}" data-slot-index="${s}">×</div>\n                    </div>\n                `}else n+='\n                    <div class="fg-costume-slot-empty">\n                        +\n                    </div>\n                ';if(n+="</div>",t){const t=this.selectedStats[e][s],a=StatsConfig.getStatInfo(t.id);n+=`\n                    <div class="fg-costume-slot-stat-value-below">\n                        +${t.value}${a&&a.isPercentage?"%":""}\n                    </div>\n                `}else n+='<div class="fg-costume-slot-stat-value-below"></div>';n+="</div>"}return n},createEpicCraftDropdown:function(e){return`\n            <div class="fg-costume-epic-craft-button" data-costume-type="${e}">\n                <span class="fg-costume-epic-craft-label">Select Epic Craft</span>\n            </div>\n        `},setupSlotClickHandlers:function(){this.elements.panel.querySelectorAll(".fg-costume-slot").forEach((e=>{e.addEventListener("click",(t=>{if(t.target.classList.contains("fg-costume-slot-remove"))return;const n=e.getAttribute("data-costume-type"),s=parseInt(e.getAttribute("data-slot-index"));this.showStatSelectionPopup(n,s)}))})),this.elements.panel.querySelectorAll(".fg-costume-slot-remove").forEach((e=>{e.addEventListener("click",(t=>{t.stopPropagation();const n=e.getAttribute("data-costume-type"),s=parseInt(e.getAttribute("data-slot-index"));this.selectedStats[n][s]=null,this.updateSlotUI(n,s),this.updateSelectedStatsDisplay(),this.updateCostumeStats(),this.saveToStore()}))}))},setupEpicCraftDropdowns:function(){this.elements.panel.querySelectorAll(".fg-costume-epic-craft-button").forEach((e=>{const t=e.getAttribute("data-costume-type");this.updateEpicCraftButtonLabel(e,t),e.addEventListener("click",(()=>{this.showEpicCraftSelectionPopup(t)}))}))},updateEpicCraftButtonLabel:function(e,t){const n=this.epicCraftOptions[t],s=e.querySelector(".fg-costume-epic-craft-label");if(n){let e,a,i;if("generic"===t?e=n.match(/generic_(\w+)_(\d+)/):"forceWing"===t?e=n.match(/fw_(\w+)_(\d+)/):"vehicle"===t&&(e=n.match(/v_(\w+)_(\d+)/)),e&&e.length>=3){a=e[1],i=parseInt(e[2]);const n=CostumeData.metaOptions[t][a];if(n){const e=n.grades.indexOf(i);if(-1!==e){const t=n.values[e];return s.textContent=`${n.name} +${t}`,void s.classList.add("epic-craft-text")}}}s.textContent="Select Epic Option",s.classList.remove("epic-craft-text")}else s.textContent="Select Epic Option",s.classList.remove("epic-craft-text")},showEpicCraftSelectionPopup:function(e){this.currentSlot={type:e,index:-1};let t='<div class="fg-costume-stat-option" data-epic-id="">No Epic Option</div>';CostumeData.metaOptions[e]&&Object.keys(CostumeData.metaOptions[e]).forEach((n=>{const s=CostumeData.metaOptions[e][n];t+=`<div class="fg-costume-stat-group-header">${s.name}</div>`;for(let a=0;a<s.grades.length;a++){const i=s.grades[a],l=s.values[a];let o;"generic"===e?o=`generic_${n}_${i}`:"forceWing"===e?o=`fw_${n}_${i}`:"vehicle"===e&&(o=`v_${n}_${i}`),t+=`\n                        <div class="fg-costume-stat-option" data-epic-id="${o}">\n                            <div class="fg-costume-stat-option-left">\n                                <div class="fg-costume-stat-option-icon">\n                                    <img src="${StatsConfig.getStatIconUrl(s.statId)}" alt="${s.name}">\n                                </div>\n                                <div class="fg-costume-stat-option-name">${s.name} +${l}</div>\n                            </div>\n                            <div class="fg-costume-stat-option-value grade-${i}">\n                                Grade ${i}\n                            </div>\n                        </div>\n                    `}})),this.elements.popupOptions.innerHTML=t;const n=this.elements.popup.querySelector(".fg-costume-stat-popup-header h3");n&&(n.textContent="Select Epic Option"),this.elements.popupOptions.querySelectorAll(".fg-costume-stat-option").forEach((t=>{t.addEventListener("click",(()=>{const n=t.getAttribute("data-epic-id");this.epicCraftOptions[e]=n||null;const s=this.elements.panel.querySelector(`.fg-costume-epic-craft-button[data-costume-type="${e}"]`);s&&this.updateEpicCraftButtonLabel(s,e),this.updateSelectedStatsDisplay(),this.updateCostumeStats(),this.saveToStore(),this.hideStatSelectionPopup()}))})),this.elements.popup.classList.add("active")},createStatSelectionPopup:function(){const e=document.createElement("div");e.id="fg-costume-stat-popup",e.className="fg-costume-stat-popup",e.innerHTML='\n            <div class="fg-costume-stat-popup-content">\n                <div class="fg-costume-stat-popup-header">\n                    <h3>Select Costume Stat</h3>\n                    <button class="fg-costume-stat-popup-close">&times;</button>\n                </div>\n                <div class="fg-costume-stat-popup-body">\n                    <div class="fg-costume-stat-options">\n                        \x3c!-- Stat options will be populated dynamically --\x3e\n                    </div>\n                </div>\n            </div>\n        ',document.body.appendChild(e),this.elements.popup=e,this.elements.popupClose=e.querySelector(".fg-costume-stat-popup-close"),this.elements.popupContent=e.querySelector(".fg-costume-stat-popup-content"),this.elements.popupOptions=e.querySelector(".fg-costume-stat-options"),this.elements.popupClose.addEventListener("click",(()=>{this.hideStatSelectionPopup()})),e.addEventListener("click",(t=>{t.target===e&&this.hideStatSelectionPopup()}));const t=document.createElement("style");t.textContent="\n            .fg-costume-stat-popup {\n                display: none;\n                position: fixed;\n                top: 0;\n                left: 0;\n                width: 100%;\n                height: 100%;\n                background: rgba(0, 0, 0, 0.7);\n                z-index: 1000;\n                justify-content: center;\n                align-items: center;\n            }\n\n            .fg-costume-stat-popup.active {\n                display: flex;\n            }\n\n            .fg-costume-stat-popup-content {\n                background: #1c1e22;\n                border-radius: 5px;\n                width: 90%;\n                max-width: 500px;\n                max-height: 80vh;\n                overflow: hidden;\n                box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);\n                border: 1px solid #3c3f44;\n            }\n\n            .fg-costume-stat-popup-header {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                padding: 10px 15px;\n                border-bottom: 1px solid #3c3f44;\n                background: #272a2e;\n            }\n\n            .fg-costume-stat-popup-header h3 {\n                margin: 0;\n                color: #e0e0e0;\n                font-size: 1.1rem;\n            }\n\n            .fg-costume-stat-popup-close {\n                background: none;\n                border: none;\n                color: #999;\n                font-size: 24px;\n                cursor: pointer;\n                padding: 0;\n                line-height: 1;\n            }\n\n            .fg-costume-stat-popup-close:hover {\n                color: #fff;\n            }\n\n            .fg-costume-stat-popup-body {\n                padding: 15px;\n                max-height: calc(80vh - 50px);\n                overflow-y: auto;\n            }\n\n            .fg-costume-stat-options {\n                display: flex;\n                flex-direction: column;\n                gap: 8px;\n            }\n\n            .fg-costume-stat-option {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                padding: 8px 12px;\n                background: rgba(30, 30, 40, 0.6);\n                border-radius: 4px;\n                cursor: pointer;\n                transition: background 0.2s;\n            }\n\n            .fg-costume-stat-option:hover {\n                background: rgba(50, 50, 70, 0.8);\n            }\n\n            .fg-costume-stat-option-left {\n                display: flex;\n                align-items: center;\n            }\n\n            .fg-costume-stat-option-icon {\n                width: 24px;\n                height: 24px;\n                margin-right: 10px;\n                border-radius: 3px;\n                background-color: rgba(0, 0, 0, 0.2);\n                padding: 2px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n            }\n\n            .fg-costume-stat-option-icon img {\n                max-width: 100%;\n                max-height: 100%;\n            }\n\n            .fg-costume-stat-option-name {\n                color: #d0d0d0;\n                font-size: 0.9rem;\n            }\n\n            .fg-costume-stat-option-value {\n                color: #66bb6a;\n                font-weight: bold;\n                font-size: 0.9rem;\n            }\n\n            .fg-costume-stat-option-value.grade-1 {\n                color: #66bb6a; /* Green for Grade 1 */\n            }\n\n            .fg-costume-stat-option-value.grade-2 {\n                color: #42a5f5; /* Blue for Grade 2 */\n            }\n\n            .fg-costume-stat-option-value.grade-3 {\n                color: #ba68c8; /* Purple for Grade 3 */\n            }\n\n            .fg-costume-stat-option-value.grade-4 {\n                color: #ffa726; /* Orange for Grade 4 */\n            }\n\n            .fg-costume-stat-group-header {\n                padding: 5px 0;\n                margin-top: 10px;\n                color: #999;\n                font-size: 0.85rem;\n                border-bottom: 1px solid #3c3f44;\n            }\n\n            .fg-costume-epic-craft-button {\n                background-color: rgba(30, 30, 40, 0.9);\n                border: 1px solid #3c3f44;\n                border-radius: 4px;\n                padding: 8px 12px;\n                cursor: pointer;\n                transition: all 0.2s;\n                min-width: 180px;\n                max-width: 100%;\n                text-align: center;\n                white-space: nowrap;\n                overflow: hidden;\n                text-overflow: ellipsis;\n                box-sizing: border-box;\n            }\n\n            .fg-costume-epic-craft-button:hover {\n                background-color: rgba(40, 40, 50, 0.9);\n                border-color: #5e6d7f;\n            }\n\n            .fg-costume-epic-craft-label {\n                color: #e0e0e0;\n                font-size: 0.9rem;\n                display: block;\n                overflow: hidden;\n                text-overflow: ellipsis;\n            }\n\n            .fg-costume-epic-craft-label.epic-craft-text {\n                color: #a9e34b;\n            }\n\n            .fg-costume-epic-craft {\n                min-width: 200px;\n                flex-shrink: 0;\n                padding-right: 10px;\n                box-sizing: border-box;\n                display: flex;\n                align-items: center;\n                overflow: hidden;\n            }\n        ",document.head.appendChild(t)},showStatSelectionPopup:function(e,t){this.currentSlot={type:e,index:t};const n=CostumeData.typeStats[e]||[];let s="";n.forEach((e=>{const t=StatsConfig.getStatInfo(e.id),n=t?t.name:e.id;s+=`\n                <div class="fg-costume-stat-option" data-stat-id="${e.id}" data-stat-value="${e.value}">\n                    <div class="fg-costume-stat-option-left">\n                        <div class="fg-costume-stat-option-icon">\n                            <img src="${StatsConfig.getStatIconUrl(e.id)}" alt="${n}">\n                        </div>\n                        <div class="fg-costume-stat-option-name">${n}</div>\n                    </div>\n                    <div class="fg-costume-stat-option-value">+${e.value}${t&&t.isPercentage?"%":""}</div>\n                </div>\n            `})),this.elements.popupOptions.innerHTML=s,this.elements.popupOptions.querySelectorAll(".fg-costume-stat-option").forEach((e=>{e.addEventListener("click",(()=>{const t=e.getAttribute("data-stat-id"),n=parseInt(e.getAttribute("data-stat-value"));this.selectStat(t,n),this.hideStatSelectionPopup()}))})),this.elements.popup.classList.add("active")},hideStatSelectionPopup:function(){this.elements.popup.classList.remove("active")},selectStat:function(e,t){const{type:n,index:s}=this.currentSlot,a={id:e,value:t};this.selectedStats[n][s]=a,this.updateSlotUI(n,s),this.updateSelectedStatsDisplay(),this.updateCostumeStats(),this.saveToStore()},updateSlotUI:function(e,t){const n=this.elements.panel.querySelector(`.fg-costume-slot[data-costume-type="${e}"][data-slot-index="${t}"]`);if(!n)return;const s=n.closest(".fg-costume-slot-wrapper");if(!s)return;let a=s.querySelector(".fg-costume-slot-stat-value-below");a||(a=document.createElement("div"),a.className="fg-costume-slot-stat-value-below",s.appendChild(a));const i=this.selectedStats[e][t];if(i?(n.classList.add("selected"),n.classList.remove("empty")):(n.classList.remove("selected"),n.classList.add("empty")),i){const s=StatsConfig.getStatInfo(i.id),l=s?s.name:i.id;n.innerHTML=`\n                <div class="fg-costume-slot-stat">\n                    <div class="fg-costume-slot-stat-icon">\n                        <img src="${StatsConfig.getStatIconUrl(i.id)}" alt="${l}">\n                    </div>\n                    <div class="fg-costume-slot-remove" data-costume-type="${e}" data-slot-index="${t}">×</div>\n                </div>\n            `,a.innerHTML=`+${i.value}${s&&s.isPercentage?"%":""}`;const o=n.querySelector(".fg-costume-slot-remove");o&&o.addEventListener("click",(n=>{n.stopPropagation(),this.selectedStats[e][t]=null,this.updateSlotUI(e,t),this.updateSelectedStatsDisplay(),this.updateCostumeStats(),this.saveToStore()}))}else n.innerHTML='\n                <div class="fg-costume-slot-empty">\n                    +\n                </div>\n            ',a.innerHTML=""},updateSelectedStatsDisplay:function(){const e={};if(this.costumeTypes.forEach((t=>{for(let n=0;n<this.selectedStats[t].length;n++){const s=this.selectedStats[t][n];if(s){const{id:t,value:n}=s;e[t]||(e[t]=0),e[t]+=n}}})),this.costumeTypes.forEach((t=>{const n=this.epicCraftOptions[t];if(!n)return;let s,a,i;if("generic"===t?s=n.match(/generic_(\w+)_(\d+)/):"forceWing"===t?s=n.match(/fw_(\w+)_(\d+)/):"vehicle"===t&&(s=n.match(/v_(\w+)_(\d+)/)),s&&s.length>=3){a=s[1],i=parseInt(s[2]);const n=CostumeData.metaOptions[t][a];if(n){const t=n.grades.indexOf(i);if(-1!==t){const s=n.values[t],a=n.statId;e[a]||(e[a]=0),e[a]+=s}}}})),"undefined"!=typeof StatIntegrationService)this.elements.selectedList.innerHTML=StatIntegrationService.createStatSummaryHTML(e);else{let t="";for(const n in e){const s=StatsConfig.getStatInfo(n),a=s?s.name:n,i=!!s&&s.isPercentage;t+=`\n                    <div class="fg-costume-stat-summary-item">\n                        <div class="fg-costume-stat-summary-name">${a}</div>\n                        <div class="fg-costume-stat-summary-value">+${e[n]}${i?"%":""}</div>\n                    </div>\n                `}this.elements.selectedList.innerHTML=t||'<div class="fg-costume-no-stats">No stats selected</div>'}},updateCostumeStats:function(){const e={};this.costumeTypes.forEach((t=>{for(let n=0;n<this.selectedStats[t].length;n++){const s=this.selectedStats[t][n];if(s){const{id:t,value:n}=s;e[t]||(e[t]=0),e[t]+=n}}})),this.costumeTypes.forEach((t=>{const n=this.epicCraftOptions[t];if(!n)return;let s,a,i;if("generic"===t?s=n.match(/generic_(\w+)_(\d+)/):"forceWing"===t?s=n.match(/fw_(\w+)_(\d+)/):"vehicle"===t&&(s=n.match(/v_(\w+)_(\d+)/)),s&&s.length>=3){a=s[1],i=parseInt(s[2]);const n=CostumeData.metaOptions[t][a];if(n){const t=n.grades.indexOf(i);if(-1!==t){const s=n.values[t],a=n.statId;e[a]||(e[a]=0),e[a]+=s}}}})),"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats("costumes",e)},setupEventListeners:function(){},getEssentialData:function(){return{selectedStats:JSON.parse(JSON.stringify(this.selectedStats)),epicCraftOptions:JSON.parse(JSON.stringify(this.epicCraftOptions))}},ensureDataStructure:function(){this.selectedStats||(this.selectedStats={generic:Array(3).fill(null),forceWing:Array(3).fill(null),vehicle:Array(3).fill(null)});const e=["generic","forceWing","vehicle"];e.forEach((e=>{if(this.selectedStats[e]||(this.selectedStats[e]=Array(3).fill(null)),this.selectedStats[e].length<3){const t=Array(3-this.selectedStats[e].length).fill(null);this.selectedStats[e]=this.selectedStats[e].concat(t)}else this.selectedStats[e].length>3&&(this.selectedStats[e]=this.selectedStats[e].slice(0,3))})),this.epicCraftOptions||(this.epicCraftOptions={generic:null,forceWing:null,vehicle:null}),e.forEach((e=>{void 0===this.epicCraftOptions[e]&&(this.epicCraftOptions[e]=null)}))},saveToStore:function(){if(this.ensureDataStructure(),"undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("costumes",e),!0}return!1},ensureAndSave:function(){if(this.ensureDataStructure(),"undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("costumes",e),!0}return!1},loadFromStore:function(){if(window.BuildSaverStore&&BuildSaverStore.dataLoaded){const e=BuildSaverStore.getSystemData("costumes");if(e)return this.loadFromData(e)}return!1},loadFromData:function(e){return!!e&&(e.selectedStats&&(this.selectedStats=JSON.parse(JSON.stringify(e.selectedStats))),e.epicCraftOptions&&(this.epicCraftOptions=JSON.parse(JSON.stringify(e.epicCraftOptions))),this.ensureDataStructure(),this.costumeTypes.forEach((e=>{for(let t=0;t<this.selectedStats[e].length;t++)this.updateSlotUI(e,t);const t=document.querySelector(`.fg-costume-epic-craft-button[data-costume-type="${e}"]`);t&&this.updateEpicCraftButtonLabel(t,e)})),this.updateSelectedStatsDisplay(),this.updateCostumeStats(),!0)}},document.addEventListener("DOMContentLoaded",(function(){})),window.CostumeSystem=window.CostumesSystem,window.OverlordMasterySystem={isInitialized:!1,dataLoaded:!1,elements:{},activeCategory:"attack",skillLevels:{attack:{},defense:{}},usedPoints:0,init:function(){if(this.isInitialized)return;const e=document.getElementById("fg-overlord-mastery-system");e&&("undefined"!=typeof OverlordMasteryData&&"undefined"!=typeof StatsConfig?(this.elements.panel=e,this.initUI(),this.initializeSkillLevels(),this.loadFromStore(),this.updateOverlordMasteryStats(),this.isInitialized=!0):setTimeout((()=>this.init()),100))},initUI:function(){this.createOverlordMasteryUI()},createOverlordMasteryUI:function(){if(!this.elements.panel)return;let e="";Object.keys(OverlordMasteryData.categories).forEach((t=>{OverlordMasteryData.categories[t];const n=t===this.activeCategory;e+=`\n                <div class="fg-overlord-mastery-panel ${n?"active":""}" data-category="${t}">\n                    <div class="fg-overlord-mastery-skills-grid" id="fg-overlord-mastery-${t}-skills">\n                        ${this.createSkillsHTML(t)}\n                    </div>\n                </div>\n            `}));const t=`\n            <div class="fg-overlord-mastery-container">\n                <div class="fg-overlord-mastery-header">\n                    <h2>Overlord Mastery (Level 200+)</h2>\n                    <div class="fg-overlord-mastery-points">\n                        Points Used: <span class="used-points">0</span>\n                    </div>\n                </div>\n\n                \x3c!-- Tab Navigation --\x3e\n                <div class="fg-overlord-mastery-tabs">\n                    ${Object.keys(OverlordMasteryData.categories).map((e=>{const t=OverlordMasteryData.categories[e];return`<div class="fg-overlord-mastery-tab ${e===this.activeCategory?"active":""}" data-category="${e}">${t.name}</div>`})).join("")}\n                </div>\n\n                \x3c!-- Tab Content --\x3e\n                <div class="fg-overlord-mastery-content">\n                    ${e}\n                </div>\n\n                \x3c!-- Stats Summary Section --\x3e\n                <div class="fg-overlord-mastery-summary">\n                    <h3>Mastery Stats Summary</h3>\n                    <div class="fg-overlord-mastery-summary-content">\n                        \x3c!-- Stats will be dynamically populated here --\x3e\n                    </div>\n                </div>\n            </div>\n        `;this.elements.panel.innerHTML=t,this.elements.container=this.elements.panel.querySelector(".fg-overlord-mastery-container"),this.elements.pointsDisplay=this.elements.panel.querySelector(".fg-overlord-mastery-points"),this.elements.tabNav=this.elements.panel.querySelector(".fg-overlord-mastery-tabs"),this.elements.tabContent=this.elements.panel.querySelector(".fg-overlord-mastery-content"),this.elements.summaryContent=this.elements.panel.querySelector(".fg-overlord-mastery-summary-content"),this.setupTabClickHandlers(),this.setupSkillClickHandlers(),"undefined"!=typeof QuickFillButtonConfigs&&(this.quickFillManager=QuickFillButtonConfigs.initializeSystem("overlord-mastery",this.elements.container)),this.updatePointsDisplay()},createSkillsHTML:function(e){const t=OverlordMasteryData.skills[e];if(!t)return"";const n=Array(4).fill(null).map((()=>Array(4).fill(null)));t.forEach((e=>{if(e.gridPosition){const t=e.gridPosition.row-1,s=e.gridPosition.col-1;t>=0&&t<4&&s>=0&&s<4&&(n[t][s]=e)}}));let s="";for(let t=0;t<4;t++)for(let a=0;a<4;a++){const i=n[t][a];s+=i?this.createSkillHTML(i,e):`<div class="fg-overlord-mastery-skill-empty" style="grid-row: ${t+1}; grid-column: ${a+1};"></div>`}return s},createSkillHTML:function(e,t){const n=this.skillLevels[t][e.id]||0,s=this.calculateSkillValue(e,n),a=n>0,i=n>=e.maxLevel;let l="";"undefined"!=typeof StatsConfig&&(l=StatsConfig.getStatIconUrl(e.statId));const o=e.gridPosition?`grid-row: ${e.gridPosition.row}; grid-column: ${e.gridPosition.col};`:"",r=s>0?`+${e.isPercentage?s+"%":s}`:e.isPercentage?"0%":"0";return`\n            <div class="fg-overlord-mastery-skill ${a?"selected":"empty"} ${i?"maxed":""}"\n                 data-skill-id="${e.id}"\n                 data-category-id="${t}"\n                 style="${o}"\n                 title="${e.name}: 0-${e.values[e.maxLevel-1]||0}${e.isPercentage?"%":""} (Level ${n}/${e.maxLevel})">\n                <div class="fg-overlord-mastery-skill-slot ${a?"selected":"empty"}">\n                    <div class="fg-overlord-mastery-skill-icon">\n                        <img src="${l}" alt="${e.name}">\n                        <div class="fg-overlord-mastery-skill-level">${n}/${e.maxLevel}</div>\n                    </div>\n                </div>\n                <div class="fg-overlord-mastery-skill-value">${r}</div>\n                <div class="fg-overlord-mastery-skill-name">${e.name}</div>\n            </div>\n        `},setupTabClickHandlers:function(){this.elements.tabNav&&this.elements.tabNav.addEventListener("click",(e=>{const t=e.target.closest(".fg-overlord-mastery-tab");if(!t)return;const n=t.dataset.category;this.switchCategory(n)}))},setupSkillClickHandlers:function(){this.elements.container&&(this.elements.container.addEventListener("click",(e=>{const t=e.target.closest(".fg-overlord-mastery-skill");if(!t)return;const n=t.dataset.skillId,s=t.dataset.categoryId;this.increaseSkillLevel(n,s)})),this.elements.container.addEventListener("contextmenu",(e=>{e.preventDefault();const t=e.target.closest(".fg-overlord-mastery-skill");if(!t)return;const n=t.dataset.skillId,s=t.dataset.categoryId;this.decreaseSkillLevel(n,s)})))},initializeSkillLevels:function(){Object.keys(OverlordMasteryData.categories).forEach((e=>{this.skillLevels[e]={},OverlordMasteryData.skills[e].forEach((t=>{this.skillLevels[e][t.id]=0}))})),this.usedPoints=0},increaseSkillLevel:function(e,t){const n=this.findSkill(e,t);if(!n)return;const s=this.skillLevels[t][e]||0;if(s>=n.maxLevel)return;const a=n.opRequired||1;this.skillLevels[t][e]=s+1,this.usedPoints+=a,this.updateSkillUI(n,t),this.updatePointsDisplay(),this.updateOverlordMasteryStats(),this.saveToStore()},decreaseSkillLevel:function(e,t){const n=this.findSkill(e,t);if(!n)return;const s=this.skillLevels[t][e]||0;if(s<=0)return;const a=n.opRequired||1;this.skillLevels[t][e]=s-1,this.usedPoints-=a,this.updateSkillUI(n,t),this.updatePointsDisplay(),this.updateOverlordMasteryStats(),this.saveToStore()},findSkill:function(e,t){return OverlordMasteryData.skills[t]?OverlordMasteryData.skills[t].find((t=>t.id===e)):null},updateSkillUI:function(e,t){const n=this.elements.container.querySelector(`.fg-overlord-mastery-skill[data-skill-id="${e.id}"][data-category-id="${t}"]`);if(!n)return;const s=this.skillLevels[t][e.id]||0,a=this.calculateSkillValue(e,s),i=s>0,l=s>=e.maxLevel;i?(n.classList.add("selected"),n.classList.remove("empty")):(n.classList.remove("selected"),n.classList.add("empty")),l?n.classList.add("maxed"):n.classList.remove("maxed");const o=n.querySelector(".fg-overlord-mastery-skill-slot");o&&(i?(o.classList.add("selected"),o.classList.remove("empty")):(o.classList.remove("selected"),o.classList.add("empty")));const r=n.querySelector(".fg-overlord-mastery-skill-level");r&&(r.textContent=`${s}/${e.maxLevel}`);const c=n.querySelector(".fg-overlord-mastery-skill-value");if(c){const t=a>0?`+${e.isPercentage?a+"%":a}`:e.isPercentage?"0%":"0";c.textContent=t}},updatePointsDisplay:function(){if(!this.elements.pointsDisplay)return;const e=this.elements.pointsDisplay.querySelector(".used-points");e&&(e.textContent=this.usedPoints)},calculateSkillValue:function(e,t){return t<=0?0:(t>e.maxLevel&&(t=e.maxLevel),e.values[t-1]||0)},updateOverlordMasteryStats:function(){const e=this.calculateStats();"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats("overlord-mastery",e),this.updateStatsSummary(e)},calculateStats:function(){const e={};return Object.keys(this.skillLevels).forEach((t=>{OverlordMasteryData.skills[t].forEach((n=>{const s=this.skillLevels[t][n.id]||0;if(s<=0)return;const a=this.calculateSkillValue(n,s);e[n.statId]||(e[n.statId]=0),e[n.statId]+=a}))})),e},updateStatsSummary:function(e){this.elements.summaryContent&&("undefined"==typeof StatIntegrationService?this.elements.summaryContent.innerHTML='<p class="no-stats">No overlord mastery stats selected yet.</p>':this.elements.summaryContent.innerHTML=StatIntegrationService.createStatSummaryHTML(e))},getEssentialData:function(){return{skillLevels:JSON.parse(JSON.stringify(this.skillLevels)),usedPoints:this.usedPoints}},saveToStore:function(){if("undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("overlord-mastery",e),!0}return!1},loadFromStore:function(){if(this.initializeSkillLevels(),window.BuildSaverStore&&BuildSaverStore.dataLoaded){const e=BuildSaverStore.getSystemData("overlord-mastery");if(e)return this.loadFromData(e)}return!1},loadFromData:function(e){return!!e&&(e.skillLevels&&Object.keys(e.skillLevels).forEach((t=>{this.skillLevels[t]||(this.skillLevels[t]={}),Object.keys(e.skillLevels[t]).forEach((n=>{this.skillLevels[t][n]=e.skillLevels[t][n]}))})),"number"==typeof e.usedPoints?this.usedPoints=e.usedPoints:this.recalculateUsedPoints(),Object.keys(this.skillLevels).forEach((e=>{Object.keys(this.skillLevels[e]).forEach((t=>{const n=this.findSkill(t,e);n&&this.updateSkillUI(n,e)}))})),this.updatePointsDisplay(),this.updateOverlordMasteryStats(),!0)},recalculateUsedPoints:function(){this.usedPoints=0,Object.keys(this.skillLevels).forEach((e=>{Object.keys(this.skillLevels[e]).forEach((t=>{const n=this.skillLevels[e][t],s=this.findSkill(t,e);s&&n>0&&(this.usedPoints+=(s.opRequired||1)*n)}))}))},switchCategory:function(e){e!==this.activeCategory&&(this.activeCategory=e,this.elements.tabNav.querySelectorAll(".fg-overlord-mastery-tab").forEach((t=>{t.dataset.category===e?t.classList.add("active"):t.classList.remove("active")})),this.elements.tabContent.querySelectorAll(".fg-overlord-mastery-panel").forEach((t=>{t.dataset.category===e?t.classList.add("active"):t.classList.remove("active")})))},quickFillAllSkills:function(){this.usedPoints=0,Object.keys(OverlordMasteryData.categories).forEach((e=>{const t=OverlordMasteryData.skills[e];t&&t.forEach((t=>{this.skillLevels[e][t.id]=t.maxLevel,this.usedPoints+=(t.opRequired||1)*t.maxLevel,this.updateSkillUI(t,e)}))})),this.updatePointsDisplay(),this.updateOverlordMasteryStats(),this.saveToStore()},resetAllSkills:function(){Object.keys(this.skillLevels).forEach((e=>{Object.keys(this.skillLevels[e]).forEach((t=>{this.skillLevels[e][t]=0;const n=this.findSkill(t,e);n&&this.updateSkillUI(n,e)}))})),this.usedPoints=0,this.updatePointsDisplay(),this.updateOverlordMasteryStats(),this.saveToStore()}},window.ForceWingSystem={isInitialized:!1,elements:{},wingLevel:1,maxWingLevel:400,slots:Array(12).fill(null).map(((e,t)=>({id:t,selectedStat:null,statLevel:1,maxStatLevel:5}))),selectionWindow:null,currentSlot:null,init:function(){if(this.isInitialized)return;const e=document.getElementById("fg-force-wing-system");e&&(this.elements.panel=e,this.initUI(),this.initSelectionWindow(),this.loadFromStore(),this.setupEventListeners(),this.updateStats(),this.isInitialized=!0)},initUI:function(){this.elements.levelInput=this.elements.panel.querySelector("#fg-force-wing-level"),this.elements.levelStats=this.elements.panel.querySelector(".fg-force-wing-level-stats"),this.elements.skillTree=this.elements.panel.querySelector(".fg-force-wing-skill-tree"),this.elements.uiImage=this.elements.panel.querySelector(".fg-force-wing-ui-image"),this.elements.slotsContainer=this.elements.panel.querySelector(".fg-force-wing-slots-container"),this.elements.summaryContent=this.elements.panel.querySelector(".fg-force-wing-summary-content"),this.createSlots(),this.updateLevelDisplay()},createSlots:function(){if(!this.elements.slotsContainer)return;this.elements.slotsContainer.innerHTML="";const e=[{x:.140992,y:.637473},{x:.138381,y:.781847},{x:.281984,y:.688429},{x:.279373,y:.82431},{x:.425587,y:.737261},{x:.420366,y:.873142},{x:.574413,y:.741507},{x:.574413,y:.877389},{x:.718016,y:.699045},{x:.720627,y:.834926},{x:.856397,y:.64172},{x:.859008,y:.781847}];this.slots.forEach(((t,n)=>{const s=document.createElement("div");s.className="fg-force-wing-slot",s.setAttribute("data-slot-id",t.id),s.setAttribute("title",`Force Wing Slot ${t.id+1}`);const a=e[n];s.style.left=100*a.x+"%",s.style.top=100*a.y+"%",this.elements.slotsContainer.appendChild(s)})),this.elements.slotElements=this.elements.panel.querySelectorAll(".fg-force-wing-slot")},initSelectionWindow:function(){this.selectionWindow=new SelectionWindowManager({id:"fg-force-wing-stat-selector",title:"Select Force Wing Stat",className:"fg-force-wing-stat-selector",fixedPosition:!0,onSelect:e=>{null!==this.currentSlot&&("clear"===e.action?this.selectSlotStat(this.currentSlot,null):e.statId&&this.selectSlotStat(this.currentSlot,e.statId))},onClose:()=>{this.currentSlot=null}})},setupEventListeners:function(){this.elements.levelInput&&this.elements.levelInput.addEventListener("input",(e=>{this.setWingLevel(parseInt(e.target.value)||1)})),this.elements.slotElements&&this.elements.slotElements.forEach((e=>{e.addEventListener("click",(e=>{const t=parseInt(e.currentTarget.getAttribute("data-slot-id"));this.selectSlot(t)}))}))},setWingLevel:function(e){e=Math.max(1,Math.min(this.maxWingLevel,e)),this.wingLevel=e,this.updateLevelDisplay(),this.updateStats(),this.saveToStore()},updateLevelDisplay:function(){this.elements.levelInput&&(this.elements.levelInput.value=this.wingLevel),this.elements.levelStats&&(this.elements.levelStats.textContent=`+${this.wingLevel} HP, +${this.wingLevel} All Attack, +${this.wingLevel} Defense`)},selectSlot:function(e){this.currentSlot=e,this.showSlotStatSelection(e)},showSlotStatSelection:function(e){if("undefined"==typeof ForceWingData)return;const t=ForceWingData.getSlotStats(e);if(!t||0===t.length)return;const n=[];t.forEach((t=>{if("undefined"==typeof StatsConfig)return;const s=StatsConfig.getStatInfo(t.statId);if(!s)return;let a="";if("undefined"!=typeof StatsConfig)try{a=`<img src="${StatsConfig.getStatIconUrl(t.statId)}" alt="${s.name}" class="fg-selection-option-icon" onerror="this.onerror=null; this.style.display='none';">`}catch(e){}const i=ForceWingData.getStatValue(e,t.statId,1),l=s.isPercentage?"%":"";n.push({html:`\n                    <div class="fg-selection-option-with-icon">\n                        ${a}\n                        <div class="fg-selection-option-details">\n                            <span class="fg-selection-option-name">${s.name}</span>\n                            <span class="fg-selection-option-value">+${i}${l} (Level 1)</span>\n                            <span class="fg-selection-option-max">Max Level: ${t.maxLevel}</span>\n                        </div>\n                    </div>\n                `,data:{statId:t.statId}})}));const s=this.slots[e];s&&s.selectedStat&&n.push({html:'\n                    <div class="fg-selection-option-clear">\n                        <span class="fg-selection-option-name">Clear Selection</span>\n                    </div>\n                ',className:"fg-selection-clear-option",data:{action:"clear"}}),this.selectionWindow.show({title:`Select Stat for Force Wing Slot ${e+1}`,options:n})},selectSlotStat:function(e,t){t?this.setSlotStat(e,t,1):this.clearSlot(e),this.selectionWindow.hide(),this.currentSlot=null},setSlotStat:function(e,t,n){const s=this.slots[e];if(!s)return;const a=ForceWingData.getSlotStats(e).find((e=>e.statId===t));a&&(n=Math.max(1,Math.min(a.maxLevel,n)),s.selectedStat=t,s.statLevel=n,s.maxStatLevel=a.maxLevel,this.updateSlotDisplay(e),this.updateStats(),this.saveToStore())},clearSlot:function(e){const t=this.slots[e];t&&(t.selectedStat=null,t.statLevel=1,t.maxStatLevel=5,this.updateSlotDisplay(e),this.updateStats(),this.saveToStore())},updateSlotDisplay:function(e){const t=this.elements.panel.querySelector(`[data-slot-id="${e}"]`);if(!t)return;const n=this.slots[e];if(n.selectedStat){t.classList.add("filled"),t.classList.remove("empty"),this.createSlotContent(t,n);const e="undefined"!=typeof StatsConfig?StatsConfig.getStatInfo(n.selectedStat):null,s=e?e.name:n.selectedStat;t.title=`${s} Level ${n.statLevel}`}else t.classList.remove("filled"),t.classList.add("empty"),t.innerHTML="",t.title=`Force Wing Slot ${e+1}`},createSlotContent:function(e,t){const n="undefined"!=typeof StatsConfig?StatsConfig.getStatInfo(t.selectedStat):null;if(!n)return void(e.innerHTML=`<div class="fg-force-wing-slot-level">${t.statLevel}</div>`);let s="";try{s=`<img src="${StatsConfig.getStatIconUrl(t.selectedStat)}" alt="${n.name}" class="fg-force-wing-slot-icon" onerror="this.style.display='none';">`}catch(e){}e.innerHTML=`\n            <div class="fg-force-wing-slot-content">\n                <div class="fg-force-wing-slot-icon-container">\n                    ${s}\n                    <div class="fg-force-wing-slot-level">${t.statLevel}</div>\n                </div>\n            </div>\n        `},calculateStats:function(){const e={};return e.hp=this.wingLevel,e.allAttackUp=this.wingLevel,e.defense=this.wingLevel,this.slots.forEach(((t,n)=>{if(t.selectedStat&&"undefined"!=typeof ForceWingData){const s=ForceWingData.getStatValue(n,t.selectedStat,t.statLevel);s>0&&(e[t.selectedStat]?e[t.selectedStat]+=s:e[t.selectedStat]=s)}})),e},updateStats:function(){const e=this.calculateStats();this.updateStatsSummary(e),"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats("force-wing",e)},updateStatsSummary:function(e){this.elements.summaryContent&&("undefined"!=typeof StatIntegrationService?this.elements.summaryContent.innerHTML=StatIntegrationService.createStatSummaryHTML(e):this.elements.summaryContent.innerHTML='<p class="no-stats">Stats service not available.</p>')},getEssentialData:function(){return{wingLevel:this.wingLevel,slots:this.slots.map((e=>({id:e.id,selectedStat:e.selectedStat,statLevel:e.statLevel})))}},saveToStore:function(){if("undefined"!=typeof BuildSaverStore&&BuildSaverStore.saveData){const e=this.getEssentialData();return BuildSaverStore.saveData("force-wing",e),!0}return!1},loadFromStore:function(){if("undefined"==typeof BuildSaverStore)return!1;const e=BuildSaverStore.getSystemData("force-wing");return!!e&&this.loadFromData(e)},loadFromData:function(e){return!!e&&(e.wingLevel&&(this.wingLevel=e.wingLevel,this.updateLevelDisplay()),e.slots&&Array.isArray(e.slots)&&e.slots.forEach((e=>{const t=this.slots.find((t=>t.id===e.id));t&&(t.selectedStat=e.selectedStat,t.statLevel=e.statLevel||1,this.updateSlotDisplay(t.id))})),this.updateStats(),!0)}},function(){if(void 0!==window.AchievementData)return;const e=window.forceguidesPlannerData?forceguidesPlannerData.pluginUrl:"";var t;(t=e+"js/achievement-system/achievement-data.js",new Promise(((e,n)=>{const s=document.createElement("script");s.src=t,s.onload=e,s.onerror=n,document.head.appendChild(s)}))).then((()=>{})).catch((e=>{}))}(),window.AchievementSystem={isInitialized:!1,elements:{},activeCategory:"quests",selectedAchievements:{},init:function(){this.isInitialized||(void 0!==window.AchievementData?(this.createSystemHTML(),this.setupEventListeners(),this.loadFromStore(),this.isInitialized=!0):setTimeout((()=>this.init()),100))},createSystemHTML:function(){const e=document.getElementById("fg-achievement-system");e&&(e.innerHTML=`\n            <div class="fg-achievement-system-container">\n                <div class="fg-achievement-layout">\n                    <div class="fg-achievement-sidebar">\n                        ${this.createCategorySidebar()}\n                    </div>\n                    <div class="fg-achievement-main-content">\n                        ${this.createCategoryPanels()}\n                    </div>\n                </div>\n            </div>\n        `,this.elements.container=e,this.elements.sidebar=e.querySelector(".fg-achievement-sidebar"),this.elements.mainContent=e.querySelector(".fg-achievement-main-content"),this.renderCategory(this.activeCategory))},createCategorySidebar:function(){const e=window.AchievementData.categories,t=this.getCategoryCounts();let n="";for(const s in e){const a=e[s],i=t[s];n+=`\n                <div class="fg-achievement-category-item ${s===this.activeCategory?"active":""}" data-category="${s}">\n                    ${a.name}\n                    <span class="fg-achievement-category-count">(${i.completed}/${i.total})</span>\n                </div>\n            `}return n},createCategoryPanels:function(){const e=window.AchievementData.categories;let t="";for(const n in e)t+=`\n                <div id="fg-achievement-${n}-content" class="fg-achievement-category-panel ${n===this.activeCategory?"active":""}">\n                    \x3c!-- Content will be populated by renderCategory --\x3e\n                </div>\n            `;return t},setupEventListeners:function(){this.elements.sidebar.addEventListener("click",(e=>{const t=e.target.closest(".fg-achievement-category-item");if(t){const e=t.dataset.category;this.switchCategory(e)}})),this.elements.mainContent.addEventListener("change",(e=>{if(e.target.matches(".fg-achievement-milestone-select")){const t=e.target.dataset.achievementId,n=parseInt(e.target.value);this.updateMilestone(t,n)}else if(e.target.matches(".fg-achievement-checkbox")){const t=e.target.dataset.achievementId,n=e.target.checked;this.toggleAchievement(t,n)}}))},switchCategory:function(e){this.activeCategory=e,this.elements.sidebar.querySelectorAll(".fg-achievement-category-item").forEach((t=>{t.classList.toggle("active",t.dataset.category===e)})),this.elements.mainContent.querySelectorAll(".fg-achievement-category-panel").forEach((t=>{t.classList.toggle("active",t.id===`fg-achievement-${e}-content`)})),this.renderCategory(e)},renderCategory:function(e){const t=document.getElementById(`fg-achievement-${e}-content`);if(!t)return;const n=window.AchievementData.getCategoryAchievements(e);if(0===Object.keys(n).length)return void(t.innerHTML=`\n                <div class="fg-achievement-empty-state">\n                    ${window.AchievementData.categories[e].name} achievements coming soon...\n                </div>\n            `);let s="";for(const e in n){const t=n[e];s+=this.createAchievementCard(e,t)}t.innerHTML=s},createAchievementCard:function(e,t){const n=this.selectedAchievements[e]||{};return"milestone"===t.type?this.createMilestoneCard(e,t,n):this.createSingleCard(e,t,n)},createMilestoneCard:function(e,t,n){const s=n.milestone||0,a=s/t.milestones.length*100;let i='<option value="0">Not Started</option>';return t.milestones.forEach(((e,t)=>{i+=`\n                <option value="${t+1}" ${s===t+1?"selected":""}>\n                    ${e.threshold} completions\n                </option>\n            `})),`\n            <div class="fg-achievement-card" data-achievement-id="${e}">\n                <div class="fg-achievement-header">\n                    <div class="fg-achievement-name">${t.name}</div>\n                    <div class="fg-achievement-type">Milestone</div>\n                </div>\n                <div class="fg-achievement-progress-container">\n                    <div class="fg-achievement-progress-bar">\n                        <div class="fg-achievement-progress-fill" style="width: ${a}%"></div>\n                        <div class="fg-achievement-progress-text">${s}/${t.milestones.length}</div>\n                    </div>\n                </div>\n                <div class="fg-achievement-milestone-selector">\n                    <label>Current Milestone:</label>\n                    <select class="fg-achievement-milestone-select" data-achievement-id="${e}">\n                        ${i}\n                    </select>\n                </div>\n            </div>\n        `},createSingleCard:function(e,t,n){const s=n.completed||!1,a=s?100:0;return`\n            <div class="fg-achievement-card" data-achievement-id="${e}">\n                <div class="fg-achievement-header">\n                    <div class="fg-achievement-name">${t.name}</div>\n                    <div class="fg-achievement-type">Single</div>\n                </div>\n                <div class="fg-achievement-checkbox-container">\n                    <input type="checkbox" class="fg-achievement-checkbox"\n                           data-achievement-id="${e}" ${s?"checked":""}>\n                    <label>Achievement Completed</label>\n                </div>\n                <div class="fg-achievement-progress-container">\n                    <div class="fg-achievement-progress-bar">\n                        <div class="fg-achievement-progress-fill" style="width: ${a}%"></div>\n                        <div class="fg-achievement-progress-text">${s?"Complete":"Not Complete"}</div>\n                    </div>\n                </div>\n            </div>\n        `},renderStatsDisplay:function(e,t){if(!e||0===Object.keys(e).length)return`\n                <div class="fg-achievement-stats-display">\n                    <div class="fg-achievement-stats-title">${t}:</div>\n                    <div class="fg-achievement-empty-state">No stats</div>\n                </div>\n            `;let n="";if("undefined"!=typeof StatIntegrationService)n=StatIntegrationService.createStatSummaryHTML(e);else for(const t in e){const s=e[t],a=window.StatsConfig?window.StatsConfig.getStatInfo(t):null;n+=`\n                    <div class="fg-achievement-stat-item">\n                        <span class="fg-achievement-stat-name">${a?a.name:t}:</span>\n                        <span class="fg-achievement-stat-value">+${s}${a&&a.isPercentage?"%":""}</span>\n                    </div>\n                `}return`\n            <div class="fg-achievement-stats-display">\n                <div class="fg-achievement-stats-title">${t}:</div>\n                ${n}\n            </div>\n        `},calculateCumulativeStats:function(e,t){const n={};for(let s=0;s<t;s++){const t=e.milestones[s];if(t&&t.stats)for(const e in t.stats)n[e]=(n[e]||0)+t.stats[e]}return n},updateMilestone:function(e,t){this.selectedAchievements[e]||(this.selectedAchievements[e]={}),this.selectedAchievements[e].milestone=t,this.updateProgressBar(e,t),this.updateAchievementStats(),this.saveData(),this.updateCategoryCounts()},updateProgressBar:function(e,t){const n=this.elements.mainContent.querySelector(`[data-achievement-id="${e}"]`);if(!n)return;const s=window.AchievementData.getAchievementById(e);if(!s||"milestone"!==s.type)return;const a=n.querySelector(".fg-achievement-progress-fill"),i=n.querySelector(".fg-achievement-progress-text");if(a&&i){const e=t/s.milestones.length*100;a.style.width=`${e}%`,i.textContent=`${t}/${s.milestones.length}`}},toggleAchievement:function(e,t){this.selectedAchievements[e]||(this.selectedAchievements[e]={}),this.selectedAchievements[e].completed=t,this.updateSingleProgressBar(e,t),this.updateAchievementStats(),this.saveData(),this.updateCategoryCounts()},updateSingleProgressBar:function(e,t){const n=this.elements.mainContent.querySelector(`[data-achievement-id="${e}"]`);if(!n)return;const s=n.querySelector(".fg-achievement-progress-fill"),a=n.querySelector(".fg-achievement-progress-text");if(s&&a){const e=t?100:0;s.style.width=`${e}%`,a.textContent=t?"Complete":"Not Complete"}},calculateTotalStats:function(){const e={};for(const t in this.selectedAchievements){const n=this.selectedAchievements[t],s=window.AchievementData.getAchievementById(t);if(s)if("milestone"===s.type&&n.milestone){const t=this.calculateCumulativeStats(s,n.milestone);for(const n in t)e[n]=(e[n]||0)+t[n]}else if("single"===s.type&&n.completed)for(const t in s.stats)e[t]=(e[t]||0)+s.stats[t]}return e},updateAchievementStats:function(){const e=this.calculateTotalStats();"undefined"!=typeof BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats("achievement",e),this.updateStatsSummary(e)},updateStatsSummary:function(e){let t=this.elements.container.querySelector(".fg-achievement-stats-summary");if(!t){const e=this.elements.container.querySelector(".fg-achievement-main-content");e&&(t=document.createElement("div"),t.className="fg-achievement-stats-summary",t.innerHTML='\n                    <div class="fg-achievement-stats-title">Total Achievement Stats:</div>\n                    <div class="fg-achievement-summary-content"></div>\n                ',e.appendChild(t))}const n=t?t.querySelector(".fg-achievement-summary-content"):null;n&&("undefined"!=typeof StatIntegrationService?n.innerHTML=StatIntegrationService.createStatSummaryHTML(e):n.innerHTML='<p class="no-stats">No achievement stats selected yet.</p>')},updateStats:function(){this.updateAchievementStats()},getCategoryCounts:function(){const e=window.AchievementData.getCategoryCounts();for(const t in this.selectedAchievements){const n=this.selectedAchievements[t],s=window.AchievementData.getAchievementById(t);if(!s)continue;const a=s.category;e[a]&&("milestone"===s.type&&n.milestone>0||"single"===s.type&&n.completed)&&e[a].completed++}return e},updateCategoryCounts:function(){const e=this.getCategoryCounts();for(const t in e){const n=this.elements.sidebar.querySelector(`[data-category="${t}"]`);if(n){const s=n.querySelector(".fg-achievement-category-count");if(s){const n=e[t];s.textContent=`(${n.completed}/${n.total})`}}}},saveData:function(){window.BuildSaverStore&&"function"==typeof BuildSaverStore.saveData&&BuildSaverStore.saveData()},getEssentialData:function(){return{selectedAchievements:JSON.parse(JSON.stringify(this.selectedAchievements)),activeCategory:this.activeCategory}},loadFromStore:function(){if(window.BuildSaverStore&&BuildSaverStore.dataLoaded){const e=BuildSaverStore.getSystemData("achievement");if(e)return this.loadFromData(e)}return!1},loadFromData:function(e){return e.selectedAchievements&&(this.selectedAchievements=e.selectedAchievements),e.activeCategory&&(this.activeCategory=e.activeCategory),this.updateAchievementStats(),this.updateCategoryCounts(),this.isInitialized&&this.renderCategory(this.activeCategory),!0}},document.addEventListener("DOMContentLoaded",(function(){setTimeout((()=>{window.AchievementSystem&&AchievementSystem.init()}),100)}));