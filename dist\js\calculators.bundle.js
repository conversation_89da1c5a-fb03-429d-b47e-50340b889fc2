(()=>{const e={previousSwordNormal:0,previousSwordCrit:0,previousMagicNormal:0,previousMagicCrit:0,previousSwordDPS:0,previousMagicDPS:0,enemyConfig:{level:1,defense:0,damageReduction:0,damageReductionPercent:0,finalDamageDecrease:0},init:function(){if(this.createDamageDisplayIfNeeded(),"undefined"!=typeof StatsSummary){const e=StatsSummary.updateTotalStats;StatsSummary.updateTotalStats=n=>{e.call(StatsSummary,n),this.calculateAndUpdateDamage(StatsSummary.derivedStats||n)},StatsSummary.derivedStats&&Object.keys(StatsSummary.derivedStats).length>0?setTimeout((()=>{this.calculateAndUpdateDamage(StatsSummary.derivedStats)}),500):StatsSummary.totalStats&&Object.keys(StatsSummary.totalStats).length>0&&setTimeout((()=>{this.calculateAndUpdateDamage(StatsSummary.totalStats)}),500)}},createDamageDisplayIfNeeded:function(){if(!document.getElementById("fg-damage-display")){const e=document.createElement("div");e.id="fg-damage-display-container",e.className="fg-damage-display-container",e.innerHTML='\n                <div class="fg-damage-title">Damage estimates</div>\n                <div class="fg-damage-right-section">\n                    <div class="fg-damage-section">\n                        <div class="fg-damage-type">Sword</div>\n                        <div class="fg-damage-hit-types">\n                            <div class="fg-damage-hit-type">\n                                <span class="fg-damage-hit-label">Normal:</span>\n                                <span id="fg-sword-normal-display" class="fg-damage-value">0</span>\n                                <span id="fg-sword-normal-change" class="fg-damage-change"></span>\n                            </div>\n                            <div class="fg-damage-hit-type">\n                                <span class="fg-damage-hit-label">Critical:</span>\n                                <span id="fg-sword-crit-display" class="fg-damage-value">0</span>\n                                <span id="fg-sword-crit-change" class="fg-damage-change"></span>\n                            </div>\n                            <div class="fg-damage-hit-type fg-dps-row">\n                                <span class="fg-damage-hit-label">DPS:</span>\n                                <span id="fg-sword-dps-display" class="fg-damage-value">0</span>\n                                <span id="fg-sword-dps-change" class="fg-damage-change"></span>\n                            </div>\n                        </div>\n                    </div>\n                    <div class="fg-damage-section">\n                        <div class="fg-damage-type">Magic</div>\n                        <div class="fg-damage-hit-types">\n                            <div class="fg-damage-hit-type">\n                                <span class="fg-damage-hit-label">Normal:</span>\n                                <span id="fg-magic-normal-display" class="fg-damage-value">0</span>\n                                <span id="fg-magic-normal-change" class="fg-damage-change"></span>\n                            </div>\n                            <div class="fg-damage-hit-type">\n                                <span class="fg-damage-hit-label">Critical:</span>\n                                <span id="fg-magic-crit-display" class="fg-damage-value">0</span>\n                                <span id="fg-magic-crit-change" class="fg-damage-change"></span>\n                            </div>\n                            <div class="fg-damage-hit-type fg-dps-row">\n                                <span class="fg-damage-hit-label">DPS:</span>\n                                <span id="fg-magic-dps-display" class="fg-damage-value">0</span>\n                                <span id="fg-magic-dps-change" class="fg-damage-change"></span>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <div class="fg-damage-config-button" id="fg-damage-config-button">⚙️</div>\n            ';const n=document.querySelector(".fg-main-content");n&&(n.insertBefore(e,n.firstChild),this.createEnemyConfigPanel(n),this.addDamageStyles(),document.getElementById("fg-damage-config-button").addEventListener("click",(()=>{const e=document.getElementById("fg-enemy-config-panel");e&&e.classList.toggle("fg-enemy-config-panel-visible")})))}},createEnemyConfigPanel:function(e){const n=document.createElement("div");n.id="fg-enemy-config-panel",n.className="fg-enemy-config-panel",n.innerHTML=`\n            <div class="fg-enemy-config-title">Enemy Configuration</div>\n            <div class="fg-enemy-config-row">\n                <label for="fg-enemy-level" title="Enemy level affects damage calculations through level difference penalty">Level:</label>\n                <input type="number" id="fg-enemy-level" min="1" max="100" value="${this.enemyConfig.level}">\n            </div>\n            <div class="fg-enemy-config-row">\n                <label for="fg-enemy-defense" title="Higher defense reduces incoming damage. Counteracted by penetration">Defense:</label>\n                <input type="number" id="fg-enemy-defense" min="0" value="${this.enemyConfig.defense}">\n            </div>\n            <div class="fg-enemy-config-row">\n                <label for="fg-enemy-dmg-reduction" title="Flat damage reduction applied after other calculations">Damage Reduction:</label>\n                <input type="number" id="fg-enemy-dmg-reduction" min="0" value="${this.enemyConfig.damageReduction}">\n            </div>\n            <div class="fg-enemy-config-row">\n                <label for="fg-enemy-dmg-reduction-pct" title="Percentage-based damage reduction">Damage Reduction %:</label>\n                <input type="number" id="fg-enemy-dmg-reduction-pct" min="0" max="100" value="${this.enemyConfig.damageReductionPercent}">\n            </div>\n            <div class="fg-enemy-config-row">\n                <label for="fg-enemy-final-dmg-decrease" title="Applied at the end of all damage calculations">Final Damage Decrease %:</label>\n                <input type="number" id="fg-enemy-final-dmg-decrease" min="0" max="100" value="${this.enemyConfig.finalDamageDecrease}">\n            </div>\n            <button id="fg-enemy-config-save" class="fg-enemy-config-save">Apply</button>\n            <div class="fg-enemy-config-info">\n                <p>Configure enemy stats to get more accurate damage calculations. Hover over labels for more information.</p>\n            </div>\n        `,e.insertBefore(n,e.firstChild.nextSibling),setTimeout((()=>{document.getElementById("fg-enemy-config-save").addEventListener("click",(()=>{this.saveEnemyConfig()}))}),100)},saveEnemyConfig:function(){const e=parseInt(document.getElementById("fg-enemy-level").value)||1,n=parseInt(document.getElementById("fg-enemy-defense").value)||0,a=parseInt(document.getElementById("fg-enemy-dmg-reduction").value)||0,t=parseInt(document.getElementById("fg-enemy-dmg-reduction-pct").value)||0,i=parseInt(document.getElementById("fg-enemy-final-dmg-decrease").value)||0;this.configureEnemy({level:e,defense:n,damageReduction:a,damageReductionPercent:t,finalDamageDecrease:i});const s=document.getElementById("fg-enemy-config-panel");s&&s.classList.remove("fg-enemy-config-panel-visible")},addDamageStyles:function(){const e=document.createElement("style");e.textContent="\n            .fg-damage-display-container {\n                background-color: #2a2a2a;\n                border: 1px solid #4e4e4e;\n                border-radius: 6px;\n                padding: 10px;\n                margin-bottom: 15px;\n                text-align: center;\n                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n                display: flex;\n                flex-direction: row;\n                align-items: center;\n                justify-content: space-between;\n                position: relative;\n                max-width: 100%;\n                min-height: 46px;\n            }\n\n            .fg-damage-title {\n                font-size: 14px;\n                color: #aaa;\n                text-transform: uppercase;\n                letter-spacing: 0.5px;\n                margin-right: 10px;\n                white-space: nowrap;\n                flex: 1;\n                text-align: left;\n            }\n\n            .fg-damage-right-section {\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                flex: 2;\n                text-align: center;\n                gap: 15px;\n            }\n\n            .fg-damage-section {\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                min-width: 120px;\n            }\n\n            .fg-damage-type {\n                font-size: 14px;\n                color: #ccc;\n                margin-bottom: 2px;\n                font-weight: bold;\n            }\n\n            .fg-damage-hit-types {\n                display: flex;\n                flex-direction: column;\n                gap: 4px;\n            }\n\n            .fg-damage-hit-type {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                gap: 5px;\n            }\n\n            .fg-damage-hit-label {\n                font-size: 12px;\n                color: #aaa;\n                min-width: 55px;\n                text-align: right;\n            }\n\n            .fg-damage-value {\n                font-size: 14px;\n                font-weight: bold;\n                color: #fff;\n                text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);\n                min-width: 60px;\n                text-align: right;\n            }\n\n            .fg-damage-change {\n                font-size: 10px;\n                min-height: 12px;\n                min-width: 40px;\n                text-align: left;\n            }\n\n            .fg-damage-increase {\n                color: #4caf50;\n            }\n\n            .fg-damage-decrease {\n                color: #f44336;\n            }\n\n            .fg-damage-config-button {\n                cursor: pointer;\n                font-size: 16px;\n                margin-left: 10px;\n                opacity: 0.7;\n                transition: opacity 0.2s;\n            }\n\n            .fg-damage-config-button:hover {\n                opacity: 1;\n            }\n\n            .fg-enemy-config-panel {\n                background-color: #2a2a2a;\n                border: 1px solid #4e4e4e;\n                border-radius: 6px;\n                padding: 15px;\n                margin-bottom: 15px;\n                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n                max-width: 100%;\n                display: none;\n            }\n\n            .fg-enemy-config-panel-visible {\n                display: block;\n            }\n\n            .fg-enemy-config-title {\n                font-size: 16px;\n                color: #fff;\n                margin-bottom: 15px;\n                text-align: center;\n                font-weight: bold;\n            }\n\n            .fg-enemy-config-row {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                margin-bottom: 10px;\n            }\n\n            .fg-enemy-config-row label {\n                flex: 1;\n                color: #ccc;\n                font-size: 14px;\n            }\n\n            .fg-enemy-config-row input {\n                flex: 1;\n                background-color: #333;\n                border: 1px solid #555;\n                border-radius: 4px;\n                color: #fff;\n                padding: 5px;\n                width: 80px;\n            }\n\n            .fg-enemy-config-save {\n                background-color: #4caf50;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                padding: 8px 15px;\n                margin-top: 10px;\n                cursor: pointer;\n                width: 100%;\n                font-weight: bold;\n            }\n\n            .fg-enemy-config-save:hover {\n                background-color: #45a049;\n            }\n\n            .fg-enemy-config-info {\n                margin-top: 10px;\n                font-size: 12px;\n                color: #999;\n                text-align: center;\n            }\n\n            .fg-enemy-config-row label[title] {\n                cursor: help;\n                border-bottom: 1px dotted #777;\n            }\n\n            @keyframes pulse {\n                0% { transform: scale(1); }\n                50% { transform: scale(1.03); }\n                100% { transform: scale(1); }\n            }\n\n            .fg-damage-pulse {\n                animation: pulse 0.4s ease;\n            }\n\n            .fg-dps-row {\n                margin-top: 5px;\n                border-top: 1px dotted #444;\n                padding-top: 5px;\n            }\n        ",document.head.appendChild(e)},calculateDamage:function(e){if(!e||"object"!=typeof e)return{sword:{normal:0,crit:0},magic:{normal:0,crit:0}};let n=e.attack||100,a=e.magicAttack||100,t=e.penetration||0,i=e.critDamage||0,s=e.skillAmp||0,o=e.swordSkillAmp||0,d=e.magicSkillAmp||0,c=e.addDamage||0,g=e.finalDamageIncrease||0,m=e.level||1,r=e.enemyLevel||this.enemyConfig.level||m,l=e.enemyDefense||this.enemyConfig.defense||0,f=e.enemyDamageReduction||this.enemyConfig.damageReduction||0,p=e.enemyDamageReductionPercent||this.enemyConfig.damageReductionPercent||0,u=e.enemyFinalDamageDecrease||this.enemyConfig.finalDamageDecrease||0,y=l;if(t>0){const e=1e6*t/(t+400);y=l*Math.max(.5,Math.min(1,(1e6-e)/1e6))}let h=n;h=h*(100+(s+o))/100;let v=Math.max(0,Math.min(25,r-m));h=h*(100-2*v)/100,h=2*h*(1e4-1e4*y/(y+h))/1e4,h+=c,h-=f,h=h*(100-p)/100,h=h*(100+g)/100,h=h*(100-u)/100,h=Math.max(1,h);let S=h*(100+i)/100,x=a;x=x*(100+(s+d))/100,x=x*(100-2*v)/100,x=2*x*(1e4-1e4*y/(y+x))/1e4,x+=c,x-=f,x=x*(100-p)/100,x=x*(100+g)/100,x=x*(100-u)/100,x=Math.max(1,x);let b=x*(100+i)/100,C=e.critRate||0,w=e.maxCritRate||70;C=Math.min(C,w);const D=C/100,P=h*(1-D)+S*D,I=x*(1-D)+b*D,E=Math.round(P),B=Math.round(I);return{sword:{normal:Math.round(h),crit:Math.round(S),dps:E},magic:{normal:Math.round(x),crit:Math.round(b),dps:B}}},calculateAndUpdateDamage:function(e){if(!e||"object"!=typeof e)return;const n=this.calculateDamage(e),a=document.getElementById("fg-sword-normal-display"),t=document.getElementById("fg-sword-normal-change"),i=document.getElementById("fg-sword-crit-display"),s=document.getElementById("fg-sword-crit-change"),o=document.getElementById("fg-sword-dps-display"),d=document.getElementById("fg-sword-dps-change"),c=document.getElementById("fg-magic-normal-display"),g=document.getElementById("fg-magic-normal-change"),m=document.getElementById("fg-magic-crit-display"),r=document.getElementById("fg-magic-crit-change"),l=document.getElementById("fg-magic-dps-display"),f=document.getElementById("fg-magic-dps-change");if(a&&i&&c&&m){const e=n.sword.normal-this.previousSwordNormal,p=n.sword.crit-this.previousSwordCrit,u=n.sword.dps-this.previousSwordDPS,y=n.magic.normal-this.previousMagicNormal,h=n.magic.crit-this.previousMagicCrit,v=n.magic.dps-this.previousMagicDPS,S=this.formatNumber(n.sword.normal),x=this.formatNumber(n.sword.crit),b=this.formatNumber(n.sword.dps),C=this.formatNumber(n.magic.normal),w=this.formatNumber(n.magic.crit),D=this.formatNumber(n.magic.dps);a.textContent=S,i.textContent=x,o.textContent=b,c.textContent=C,m.textContent=w,l.textContent=D,[a,i,o,c,m,l].forEach((e=>{e.classList.remove("fg-damage-pulse"),e.offsetWidth,e.classList.add("fg-damage-pulse")})),this.updateChangeIndicator(t,this.previousSwordNormal,e),this.updateChangeIndicator(s,this.previousSwordCrit,p),this.updateChangeIndicator(d,this.previousSwordDPS,u),this.updateChangeIndicator(g,this.previousMagicNormal,y),this.updateChangeIndicator(r,this.previousMagicCrit,h),this.updateChangeIndicator(f,this.previousMagicDPS,v),this.previousSwordNormal=n.sword.normal,this.previousSwordCrit=n.sword.crit,this.previousSwordDPS=n.sword.dps,this.previousMagicNormal=n.magic.normal,this.previousMagicCrit=n.magic.crit,this.previousMagicDPS=n.magic.dps,"undefined"!=typeof BuildPlanner&&(BuildPlanner.damageValues=n,BuildPlanner.swordDPS=n.sword.dps,BuildPlanner.magicDPS=n.magic.dps),"undefined"!=typeof StatsSummary&&(StatsSummary.damageValues=n,StatsSummary.swordDPS=n.sword.dps,StatsSummary.magicDPS=n.magic.dps)}},updateChangeIndicator:function(e,n,a){if(n>0){const n=Math.abs(a).toLocaleString(void 0,{maximumFractionDigits:0});a>0?(e.textContent=`+${n}`,e.className="fg-damage-change fg-damage-increase"):a<0?(e.textContent=`-${n}`,e.className="fg-damage-change fg-damage-decrease"):(e.textContent="",e.className="fg-damage-change")}else e.textContent=""},formatNumber:function(e){return Math.round(e).toLocaleString(void 0,{maximumFractionDigits:0})},configureEnemy:function(e){e&&"object"==typeof e&&("number"==typeof e.level&&(this.enemyConfig.level=e.level),"number"==typeof e.defense&&(this.enemyConfig.defense=e.defense),"number"==typeof e.damageReduction&&(this.enemyConfig.damageReduction=e.damageReduction),"number"==typeof e.damageReductionPercent&&(this.enemyConfig.damageReductionPercent=e.damageReductionPercent),"number"==typeof e.finalDamageDecrease&&(this.enemyConfig.finalDamageDecrease=e.finalDamageDecrease),"undefined"!=typeof StatsSummary?StatsSummary.derivedStats&&Object.keys(StatsSummary.derivedStats).length>0?this.calculateAndUpdateDamage(StatsSummary.derivedStats):StatsSummary.totalStats&&Object.keys(StatsSummary.totalStats).length>0&&this.calculateAndUpdateDamage(StatsSummary.totalStats):"undefined"!=typeof BuildPlanner&&BuildPlanner.totalStats&&this.calculateAndUpdateDamage(BuildPlanner.totalStats))}};document.addEventListener("DOMContentLoaded",(function(){setTimeout((()=>{e.init()}),500)}))})(),(()=>{const e={previousCP:0,cpWeights:{critDamage:177,resistCritDmg:150,ignoreResistCritDmg:142.5,allSkillAmp:349,resistSkillAmp:296.5,ignoreResistSkillAmp:267,penetration:71,ignorePenetration:53.1,cancelIgnorePenetration:47.8,allAttackUp:34.5,normalDamageUp:86,defense:21,hp:5,critRate:750,resistCritRate:636,ignoreResistCritRate:574,attackRate:3,defenseRate:2.4,damageReduce:19.5,ignoreDamageReduce:16.8,accuracy:6.5,ignoreAccuracy:5.3,evasion:5.3,addDamage:35,cancelIgnoreDamageReduce:19.9,ignoreEvasion:4.5,finalDamageIncreased:1604,finalDamageDecrease:1451},init:function(){if(this.createCPDisplayIfNeeded(),"undefined"!=typeof StatsSummary){const e=StatsSummary.updateTotalStats;StatsSummary.updateTotalStats=n=>{e.call(StatsSummary,n),this.calculateAndUpdateCP(StatsSummary.derivedStats||n)},StatsSummary.derivedStats&&Object.keys(StatsSummary.derivedStats).length>0?setTimeout((()=>{this.calculateAndUpdateCP(StatsSummary.derivedStats)}),500):StatsSummary.totalStats&&Object.keys(StatsSummary.totalStats).length>0&&setTimeout((()=>{this.calculateAndUpdateCP(StatsSummary.totalStats)}),500)}},createCPDisplayIfNeeded:function(){if(!document.getElementById("fg-cp-display")){const e=document.getElementById("fg-damage-display-container");if(e){const n=document.createElement("div");n.id="fg-cp-display",n.className="fg-cp-display",n.innerHTML='\n                    <span class="fg-cp-label">Combat Power:</span>\n                    <span id="fg-cp-value" class="fg-cp-value">0</span>\n                    <span id="fg-cp-change" class="fg-cp-change"></span>\n                ';const a=document.createElement("div");a.className="fg-title-cp-container";const t=e.querySelector(".fg-damage-title");t&&(t.parentNode.insertBefore(a,t),a.appendChild(t),a.appendChild(n)),this.addCPStyles()}}},addCPStyles:function(){const e=document.createElement("style");e.textContent="\n            .fg-title-cp-container {\n                display: flex;\n                align-items: center;\n                flex: 1;\n                text-align: left;\n            }\n            \n            .fg-cp-display {\n                display: flex;\n                align-items: center;\n                margin-left: 15px;\n                padding: 4px 8px;\n                background-color: rgba(0, 0, 0, 0.2);\n                border-radius: 4px;\n                white-space: nowrap;\n            }\n            \n            .fg-cp-label {\n                font-size: 12px;\n                color: #aaa;\n                margin-right: 6px;\n            }\n            \n            .fg-cp-value {\n                font-size: 16px;\n                font-weight: bold;\n                color: #FFD700; /* Golden color for CP */\n                text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);\n            }\n            \n            .fg-cp-change {\n                font-size: 10px;\n                min-height: 12px;\n                min-width: 40px;\n                margin-left: 4px;\n                text-align: left;\n            }\n            \n            .fg-cp-increase {\n                color: #4caf50;\n            }\n            \n            .fg-cp-decrease {\n                color: #f44336;\n            }\n            \n            @keyframes cp-pulse {\n                0% { transform: scale(1); }\n                50% { transform: scale(1.05); }\n                100% { transform: scale(1); }\n            }\n            \n            .fg-cp-pulse {\n                animation: cp-pulse 0.5s ease;\n            }\n        ",document.head.appendChild(e)},calculateCP:function(e){if(!e||"object"!=typeof e)return 0;let n=0;for(const a in e){const t=e[a],i=this.cpWeights[a];"number"==typeof t&&i&&(n+=t*i)}return Math.round(n)},calculateAndUpdateCP:function(e){if(!e||"object"!=typeof e)return;const n=this.calculateCP(e),a=document.getElementById("fg-cp-value"),t=document.getElementById("fg-cp-change");if(a&&t){const e=n-this.previousCP,i=this.formatNumber(n);a.textContent=i,a.classList.remove("fg-cp-pulse"),a.offsetWidth,a.classList.add("fg-cp-pulse"),this.updateChangeIndicator(t,this.previousCP,e),this.previousCP=n,"undefined"!=typeof BuildPlanner&&(BuildPlanner.cp=n),"undefined"!=typeof StatsSummary&&(StatsSummary.cp=n)}},updateChangeIndicator:function(e,n,a){if(n>0){const n=Math.abs(a).toLocaleString(void 0,{maximumFractionDigits:0});a>0?(e.textContent=`+${n}`,e.className="fg-cp-change fg-cp-increase"):a<0?(e.textContent=`-${n}`,e.className="fg-cp-change fg-cp-decrease"):(e.textContent="",e.className="fg-cp-change")}else e.textContent=""},formatNumber:function(e){return Math.round(e).toLocaleString(void 0,{maximumFractionDigits:0})}};document.addEventListener("DOMContentLoaded",(function(){setTimeout((()=>{e.init()}),600)}))})();