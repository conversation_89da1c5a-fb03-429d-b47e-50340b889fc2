:root{--bg-primary:#1a1a1a;--bg-secondary:#252525;--bg-tertiary:#333;--bg-panel:#202020;--text-primary:#f5f5f5;--text-secondary:#b0b0b0;--accent-primary:#f90;--accent-secondary:#fb3;--border-light:hsla(0,0%,100%,.15);--border-dark:rgba(0,0,0,.3);--positive-value:#4caf50;--negative-value:#f44336;--pet-color:#f90;--stellar-color:#4a90e2;--honor-color:#9c27b0;--offensive-color:#ff6b6b;--defensive-color:#4ecdc4;--utility-color:#f9d423;--text-muted:#8b92a5;--shadow-light:0 2px 10px rgba(0,0,0,.15);--shadow-medium:0 4px 20px rgba(0,0,0,.25)}.forceguides-build-planner,.forceguides-build-planner *,[class*=essence-],[class*=fg-]{scrollbar-color:#3c3f44 #121416;scrollbar-width:thin}.forceguides-build-planner ::-webkit-scrollbar,[class*=essence-]::-webkit-scrollbar,[class*=fg-]::-webkit-scrollbar{width:8px}.forceguides-build-planner ::-webkit-scrollbar-track,[class*=essence-]::-webkit-scrollbar-track,[class*=fg-]::-webkit-scrollbar-track{background:#121416;border-radius:4px}.forceguides-build-planner ::-webkit-scrollbar-thumb,[class*=essence-]::-webkit-scrollbar-thumb,[class*=fg-]::-webkit-scrollbar-thumb{background:#3c3f44;border-radius:4px}.forceguides-build-planner ::-webkit-scrollbar-thumb:hover,[class*=essence-]::-webkit-scrollbar-thumb:hover,[class*=fg-]::-webkit-scrollbar-thumb:hover{background:#4f5a68}.fg-build-planner-container{background:var(--bg-primary);border-radius:10px;box-shadow:var(--shadow-medium);color:var(--text-primary);display:flex;font-family:Inter,system-ui,-apple-system,BlinkMacSystemFont,sans-serif;margin:20px auto;max-width:1200px;min-height:800px;overflow:hidden}h3,h4{font-weight:600;margin-bottom:15px;margin-top:0}.fg-systems-sidebar{background:var(--bg-sidebar);border-right:1px solid var(--border-dark);display:flex;flex-direction:column;flex-shrink:0;padding:20px 0;width:200px}.fg-sidebar-title{border-bottom:1px solid var(--border-light);font-size:1.1rem;font-weight:600;margin-bottom:15px;padding:0 20px 15px}.fg-system-buttons{display:flex;flex-direction:column;gap:8px;padding:0 10px}.fg-system-button{align-items:center;border-radius:8px;cursor:pointer;display:flex;gap:12px;padding:12px 15px;transition:background-color .2s,transform .2s;user-select:none}.fg-system-button:hover:not(.disabled){background-color:hsla(0,0%,100%,.05);transform:translateX(3px)}.fg-system-button.active{background-color:rgba(61,132,230,.15);border-left:3px solid var(--accent-primary);padding-left:12px}.fg-system-button.disabled{cursor:not-allowed;opacity:.5}.fg-system-button .icon{align-items:center;background-color:#444;border-radius:50%;box-sizing:border-box;display:flex;height:40px;justify-content:center;margin-right:8px;padding:5px;width:40px}.fg-system-button .class-icon{background-color:#0097a7}.fg-system-button .stellar-icon{background-color:#4a148c}.fg-system-button .honor-icon{background-color:#b71c1c}.fg-system-button .pet-icon{background-color:#1b5e20}.fg-system-button .equipment-icon{background-color:#827717}.fg-system-button .costume-icon{background-color:#880e4f}.fg-system-button .gold-merit-icon{background-color:#ff6f00}.fg-system-button .platinum-merit-icon{background-color:#757575}.fg-system-button .force-wing-icon{background-color:#01579b}.fg-system-button .stats-icon{background-color:#6a1b9a}.fg-system-button .essence-runes-icon{background-color:#1a237e}.fg-system-button .karma-runes-icon{background-color:#bf360c}.fg-system-button .gear-icon{background-color:#0d47a1}.fg-system-button .overlord-mastery-icon{background-color:#c2185b}.fg-system-button .placeholder-icon{background-color:#555}.fg-system-button svg{height:24px;width:24px}.fg-main-content{display:flex;flex:1;flex-direction:column;min-width:0}.fg-system-display{background:var(--bg-secondary);flex:1;min-height:600px;overflow:hidden;padding:20px;position:relative}.fg-system-panel{display:none;height:100%;width:100%}.fg-system-panel.active{display:block}.stellar-canvas-container{align-items:center;background-color:var(--bg-primary);border-radius:8px;display:flex;height:100%;justify-content:center;min-height:600px;overflow:hidden;width:100%}#stellar-canvas{display:block;height:100%;width:100%}.gear-placeholder{align-items:center;background:rgba(0,0,0,.1);border:1px dashed var(--border-light);border-radius:8px;display:flex;height:100%;justify-content:center}.fg-stats-summary{background:var(--bg-panel);border-top:1px solid var(--border-dark);padding:20px}.fg-summary-tabs{border-bottom:1px solid var(--border-light);display:flex;margin-bottom:15px}.fg-summary-tab{background:transparent;border:none;color:var(--text-secondary);cursor:pointer;font-weight:600;padding:10px 20px;position:relative;transition:color .2s}.fg-summary-tab.active,.fg-summary-tab:hover{color:var(--text-primary)}.fg-summary-tab.active:after{background-color:var(--accent-primary);bottom:-1px;content:"";height:2px;left:0;position:absolute;width:100%}.fg-summary-tab[data-tab=atk-stats].active:after{background-color:var(--offensive-color)}.fg-summary-tab[data-tab=def-stats].active:after{background-color:var(--defensive-color)}.fg-summary-tab[data-tab=other-stats].active:after{background-color:var(--utility-color)}.fg-summary-content{padding:15px 0}.fg-stats-table{border-radius:4px;display:flex;overflow:hidden;width:100%}.fg-stats-column{background-color:rgba(0,0,0,.2);border-right:1px solid var(--border-light);flex:1;min-width:0;padding:10px}.fg-stats-column:last-child{border-right:none}.fg-stat-item{align-items:center;border-bottom:1px solid hsla(0,0%,100%,.05);display:flex;font-size:13px;justify-content:space-between;line-height:1.2;margin-bottom:2px;padding:4px 8px}.fg-stat-item:last-child{border-bottom:none;margin-bottom:0}.fg-stat-name{color:var(--text-primary);margin-right:8px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.fg-stat-value{font-weight:600;text-align:right;white-space:nowrap}#atk-stats-list .fg-stat-value{color:var(--offensive-color,#ff6b6b)}#def-stats-list .fg-stat-value{color:var(--defensive-color,#4ecdc4)}#other-stats-list .fg-stat-value{color:var(--utility-color,#f9d423)}.fg-stat-icon{height:16px;margin-right:6px;object-fit:contain;width:16px}.fg-summary-tab-content{display:none}.fg-summary-tab-content.active{display:block}.fg-summary-tab-content::-webkit-scrollbar{display:none;width:0}.fg-summary-tab-content::-webkit-scrollbar-thumb,.fg-summary-tab-content::-webkit-scrollbar-track{display:none}.fg-summary-tab-content::-webkit-scrollbar-thumb:hover{display:none}@media (max-width:768px){.fg-build-planner-container{flex-direction:column}.fg-systems-sidebar{border-bottom:1px solid var(--border-dark);border-right:none;padding:15px;width:100%}.fg-system-buttons{flex-direction:row;flex-wrap:wrap;justify-content:center}.fg-system-button{width:auto}}#stellar-node-panel{animation:fade-in .3s ease-in-out;background-color:rgba(25,30,45,.95);border-radius:8px;box-shadow:0 4px 20px rgba(0,0,0,.5);color:#fff;max-height:80vh;overflow-y:auto;padding:15px;position:absolute;right:20px;top:20px;width:300px;z-index:1000}#stellar-node-panel h3{border-bottom:1px solid #4a90e2;color:#8ce3ff;margin:0 0 15px;padding-bottom:8px}#stellar-node-panel h4{color:#ffd866;margin:0 0 10px}#stellar-node-panel button{cursor:pointer;transition:all .2s ease}#stellar-node-panel button:hover{opacity:.8}.stat-item{transition:background-color .2s ease}.stat-item:hover{background-color:rgba(80,90,110,.6)}@keyframes fade-in{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.stellar-placeholder{align-items:center;background-color:var(--bg-secondary);border-radius:8px;color:#fff;display:flex;flex-direction:column;height:100%;justify-content:center;min-height:500px;padding:30px;text-align:center}.stellar-placeholder .star-icon{color:#9c27b0;font-size:80px;margin-bottom:20px;text-shadow:0 0 20px rgba(156,39,176,.5)}.stellar-placeholder h2{color:#e1bee7;font-size:28px;margin-bottom:15px}.stellar-placeholder .coming-soon{font-size:20px;font-weight:700;letter-spacing:1px;margin-bottom:30px;opacity:.7}.stellar-placeholder .description{line-height:1.6;max-width:500px;opacity:.8}.system-placeholder{align-items:center;background:rgba(0,0,0,.1);border:1px dashed var(--border-light);border-radius:8px;display:flex;flex-direction:column;height:100%;justify-content:center;padding:30px;text-align:center}.system-placeholder h2{color:var(--text-primary);font-size:1.8rem;margin-bottom:10px;margin-top:0}.system-placeholder .coming-soon{background:rgba(61,132,230,.15);border-radius:20px;color:var(--accent-primary);font-size:1.2rem;font-weight:600;margin-bottom:20px;padding:5px 15px}.system-placeholder .description{color:var(--text-secondary);line-height:1.6;max-width:500px}.fg-selection-window{align-items:center;background:rgba(0,0,0,.8);display:flex;height:100%;justify-content:center;left:0;opacity:0;pointer-events:none;position:fixed;top:0;transition:opacity .3s ease,visibility .3s ease;visibility:hidden;width:100%;z-index:9999}.fg-selection-window.active{opacity:1;pointer-events:auto;visibility:visible}.fg-selection-window-content{background:#1c1e22;border:1px solid #3c3f44;border-radius:8px;box-shadow:0 5px 20px rgba(0,0,0,.5);display:flex;flex-direction:column;max-height:80vh;max-width:600px;overflow:hidden;position:relative;width:90%}.fg-selection-window-fixed .fg-selection-window-content{position:relative;transform:none}.fg-selection-window-relative .fg-selection-window-content{position:absolute;transform:translate(-50%,-50%)}.fg-selection-window-header{align-items:center;background:linear-gradient(180deg,#2a2d31,#1c1e22);border-bottom:1px solid #3c3f44;display:flex;justify-content:space-between;padding:15px 20px}.fg-selection-window-title{color:#e0e0e0;font-size:1.2rem;font-weight:600;margin:0;padding:0}.fg-selection-window-close{align-items:center;background:none;border:none;border-radius:50%;color:#aaa;cursor:pointer;display:flex;font-size:1.5rem;height:30px;justify-content:center;padding:0;transition:background-color .2s,color .2s;width:30px}.fg-selection-window-close:hover{background-color:hsla(0,0%,100%,.1);color:#fff}.fg-selection-window-body{max-height:calc(80vh - 60px);overflow-y:auto;padding:20px}.fg-selection-window-options{display:grid;gap:15px;grid-template-columns:repeat(auto-fill,minmax(180px,1fr))}.fg-selection-option{align-items:center;background-color:#2a2d31;border:1px solid #3c3f44;border-radius:6px;cursor:pointer;display:flex;justify-content:space-between;padding:12px;transition:all .2s ease}.fg-selection-option:hover{background-color:#33363a;border-color:#4a4d52;box-shadow:0 4px 8px rgba(0,0,0,.2);transform:translateY(-2px)}.fg-selection-option.disabled{cursor:not-allowed;opacity:.5;pointer-events:none}.fg-selection-option-with-icon{align-items:center;display:flex;gap:10px}.fg-selection-option-icon{height:24px;object-fit:contain;width:24px}.fg-selection-option-color-preview{border-radius:50%;height:20px;margin-left:auto;width:20px}.fg-selection-option-level{color:#aaa;display:inline-block;font-weight:600;margin-right:5px;min-width:20px;text-align:center}.fg-selection-option-name{color:#d0d0d0;font-size:.9rem;font-weight:500}.fg-selection-option-value{color:#66bb6a;font-size:.9rem;font-weight:600}.fg-selection-window-search{margin-bottom:15px;position:relative}.fg-selection-window-search input{background-color:#252830;border:1px solid #3c3f44;border-radius:6px;color:#e0e0e0;font-size:.9rem;padding:10px 15px;width:100%}.fg-selection-window-search input:focus{border-color:#4a90e2;box-shadow:0 0 0 2px rgba(74,144,226,.2);outline:none}.fg-selection-category-header{border-bottom:1px solid #3c3f44;color:#e0e0e0;font-size:1rem;font-weight:600;margin:15px 0 10px;padding-bottom:5px}.fg-selection-empty-state{color:#aaa;font-style:italic;padding:30px;text-align:center}@media (max-width:768px){.fg-selection-window-content{max-height:90vh;width:95%}.fg-selection-window-options{gap:10px;grid-template-columns:repeat(auto-fill,minmax(140px,1fr))}.fg-selection-window-header{padding:12px 15px}.fg-selection-window-body{padding:15px}}.fg-quick-fill-container{align-items:center;display:flex;gap:8px;margin-bottom:15px}.fg-quick-fill-container.fg-quick-fill-top{margin-bottom:15px;margin-top:0}.fg-quick-fill-container.fg-quick-fill-header{margin-bottom:0;margin-left:auto}.fg-quick-fill-container.fg-quick-fill-custom{margin-bottom:10px}.fg-quick-fill-container.fg-quick-fill-align-left{justify-content:flex-start}.fg-quick-fill-container.fg-quick-fill-align-center{justify-content:center}.fg-quick-fill-container.fg-quick-fill-align-right{justify-content:flex-end}.fg-quick-fill-button{align-items:center;background-color:#4e9a06;border:1px solid #5fb107;border-radius:4px;color:#fff;cursor:pointer;display:inline-flex;font-size:.85rem;font-weight:700;justify-content:center;min-height:32px;padding:6px 12px;transition:all .2s ease;white-space:nowrap}.fg-quick-fill-button:hover:not(:disabled){background-color:#5fb107;box-shadow:0 0 8px rgba(95,177,7,.4);transform:translateY(-1px)}.fg-quick-fill-button:active:not(:disabled){box-shadow:0 0 4px rgba(95,177,7,.6);transform:translateY(0)}.fg-quick-fill-button:disabled{cursor:not-allowed;opacity:.6}.fg-quick-fill-button:disabled:hover{box-shadow:none;transform:none}.fg-quick-fill-button-danger{background-color:#c62828;border:1px solid #d32f2f;color:#fff}.fg-quick-fill-button-danger:hover:not(:disabled){background-color:#d32f2f;box-shadow:0 0 8px rgba(211,47,47,.5);transform:translateY(-1px)}.fg-quick-fill-button-danger:active:not(:disabled){box-shadow:0 0 4px rgba(211,47,47,.7);transform:translateY(0)}@media screen and (max-width:768px){.fg-quick-fill-container{flex-wrap:wrap;gap:6px}.fg-quick-fill-button{font-size:.8rem;min-height:28px;padding:5px 10px}}@media screen and (max-width:480px){.fg-quick-fill-container{justify-content:center;width:100%}.fg-quick-fill-container.fg-quick-fill-header{margin-left:0;margin-top:8px}.fg-quick-fill-button{flex:1;max-width:120px;min-width:80px}}.essence-runes-header .fg-quick-fill-container,.karma-runes-header .fg-quick-fill-container{margin-bottom:0}.essence-runes-buttons .fg-quick-fill-container{gap:8px;margin-bottom:0}.fg-quick-fill-button:focus{outline:2px solid #fff;outline-offset:2px}.fg-quick-fill-button:focus:not(:focus-visible){outline:none}.fg-quick-fill-button.loading{color:transparent;position:relative}.fg-quick-fill-button.loading:after{animation:button-spin 1s linear infinite;border:2px solid transparent;border-radius:50%;border-top-color:currentcolor;content:"";height:16px;left:50%;margin:-8px 0 0 -8px;position:absolute;top:50%;width:16px}@keyframes button-spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}