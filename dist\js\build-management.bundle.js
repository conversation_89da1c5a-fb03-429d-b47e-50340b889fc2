(()=>{var e={5616:(e,t,n)=>{var s,o=function(){function e(e,t){if(!o[e]){o[e]={};for(var n=0;n<e.length;n++)o[e][e.charAt(n)]=n}return o[e][t]}var t=String.fromCharCode,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",o={},a={compressToBase64:function(e){if(null==e)return"";var t=a._compress(e,6,(function(e){return n.charAt(e)}));switch(t.length%4){default:case 0:return t;case 1:return t+"===";case 2:return t+"==";case 3:return t+"="}},decompressFromBase64:function(t){return null==t?"":""==t?null:a._decompress(t.length,32,(function(s){return e(n,t.charAt(s))}))},compressToUTF16:function(e){return null==e?"":a._compress(e,15,(function(e){return t(e+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:a._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=a.compress(e),n=new Uint8Array(2*t.length),s=0,o=t.length;o>s;s++){var r=t.charCodeAt(s);n[2*s]=r>>>8,n[2*s+1]=r%256}return n},decompressFromUint8Array:function(e){if(null==e)return a.decompress(e);for(var n=new Array(e.length/2),s=0,o=n.length;o>s;s++)n[s]=256*e[2*s]+e[2*s+1];var r=[];return n.forEach((function(e){r.push(t(e))})),a.decompress(r.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":a._compress(e,6,(function(e){return s.charAt(e)}))},decompressFromEncodedURIComponent:function(t){return null==t?"":""==t?null:(t=t.replace(/ /g,"+"),a._decompress(t.length,32,(function(n){return e(s,t.charAt(n))})))},compress:function(e){return a._compress(e,16,(function(e){return t(e)}))},_compress:function(e,t,n){if(null==e)return"";var s,o,a,r={},i={},c="",l="",d="",u=2,p=3,h=2,f=[],m=0,y=0;for(a=0;a<e.length;a+=1)if(c=e.charAt(a),Object.prototype.hasOwnProperty.call(r,c)||(r[c]=p++,i[c]=!0),l=d+c,Object.prototype.hasOwnProperty.call(r,l))d=l;else{if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(s=0;h>s;s++)m<<=1,y==t-1?(y=0,f.push(n(m)),m=0):y++;for(o=d.charCodeAt(0),s=0;8>s;s++)m=m<<1|1&o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o>>=1}else{for(o=1,s=0;h>s;s++)m=m<<1|o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o=0;for(o=d.charCodeAt(0),s=0;16>s;s++)m=m<<1|1&o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o>>=1}0==--u&&(u=Math.pow(2,h),h++),delete i[d]}else for(o=r[d],s=0;h>s;s++)m=m<<1|1&o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o>>=1;0==--u&&(u=Math.pow(2,h),h++),r[l]=p++,d=String(c)}if(""!==d){if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(s=0;h>s;s++)m<<=1,y==t-1?(y=0,f.push(n(m)),m=0):y++;for(o=d.charCodeAt(0),s=0;8>s;s++)m=m<<1|1&o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o>>=1}else{for(o=1,s=0;h>s;s++)m=m<<1|o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o=0;for(o=d.charCodeAt(0),s=0;16>s;s++)m=m<<1|1&o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o>>=1}0==--u&&(u=Math.pow(2,h),h++),delete i[d]}else for(o=r[d],s=0;h>s;s++)m=m<<1|1&o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o>>=1;0==--u&&(u=Math.pow(2,h),h++)}for(o=2,s=0;h>s;s++)m=m<<1|1&o,y==t-1?(y=0,f.push(n(m)),m=0):y++,o>>=1;for(;;){if(m<<=1,y==t-1){f.push(n(m));break}y++}return f.join("")},decompress:function(e){return null==e?"":""==e?null:a._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(e,n,s){var o,a,r,i,c,l,d,u=[],p=4,h=4,f=3,m="",y=[],g={val:s(0),position:n,index:1};for(o=0;3>o;o+=1)u[o]=o;for(r=0,c=Math.pow(2,2),l=1;l!=c;)i=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=s(g.index++)),r|=(i>0?1:0)*l,l<<=1;switch(r){case 0:for(r=0,c=Math.pow(2,8),l=1;l!=c;)i=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=s(g.index++)),r|=(i>0?1:0)*l,l<<=1;d=t(r);break;case 1:for(r=0,c=Math.pow(2,16),l=1;l!=c;)i=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=s(g.index++)),r|=(i>0?1:0)*l,l<<=1;d=t(r);break;case 2:return""}for(u[3]=d,a=d,y.push(d);;){if(g.index>e)return"";for(r=0,c=Math.pow(2,f),l=1;l!=c;)i=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=s(g.index++)),r|=(i>0?1:0)*l,l<<=1;switch(d=r){case 0:for(r=0,c=Math.pow(2,8),l=1;l!=c;)i=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=s(g.index++)),r|=(i>0?1:0)*l,l<<=1;u[h++]=t(r),d=h-1,p--;break;case 1:for(r=0,c=Math.pow(2,16),l=1;l!=c;)i=g.val&g.position,g.position>>=1,0==g.position&&(g.position=n,g.val=s(g.index++)),r|=(i>0?1:0)*l,l<<=1;u[h++]=t(r),d=h-1,p--;break;case 2:return y.join("")}if(0==p&&(p=Math.pow(2,f),f++),u[d])m=u[d];else{if(d!==h)return null;m=a+a.charAt(0)}y.push(m),u[h++]=a+m.charAt(0),a=m,0==--p&&(p=Math.pow(2,f),f++)}}};return a}();void 0===(s=function(){return o}.call(t,n,t,e))||(e.exports=s)},7637:()=>{window.BuildSaverStore={buildData:null,dataLoaded:!1,getSystemData:function(e){return this.dataLoaded&&this.buildData&&this.buildData.systems&&this.buildData.systems[e]||null},setBuildData:function(e){this.buildData=e,this.dataLoaded=!0},getData:function(e){return this.getSystemData(e)},saveData:function(e,t){this.buildData||(this.buildData={timestamp:Date.now(),version:"1.0",systems:{}}),this.buildData.systems||(this.buildData.systems={}),this.buildData.systems[e]=t,this.dataLoaded=!0}},window.BuildSaver={STORAGE_KEY:"fg_current_build",init:function(){this.loadSavedBuildToStore(),this.createSaveButton(),this.setupEventListeners()},createSaveButton:function(){const e=document.createElement("div");e.className="fg-system-button fg-save-build-button",e.id="fg-save-build-btn",e.innerHTML='\n            <span class="icon save-icon">\n                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="white">\n                    <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>\n                </svg>\n            </span>\n            <span>Save Build</span>\n        ';const t=document.createElement("style");t.textContent="\n            .fg-save-build-button {\n                background-color: #ff5500 !important;\n                margin-top: 20px !important;\n                border-top: 1px solid #3a3a48;\n                padding-top: 20px !important;\n            }\n\n            .fg-save-build-button:hover {\n                background-color: #ff7722 !important;\n            }\n\n            .fg-save-notification {\n                position: fixed;\n                top: 20px;\n                right: 20px;\n                background: #2a2a36;\n                color: #fff;\n                padding: 12px 16px;\n                border-radius: 4px;\n                box-shadow: 0 4px 12px rgba(0,0,0,0.2);\n                z-index: 9999;\n                transform: translateY(-100px);\n                opacity: 0;\n                transition: all 0.3s ease;\n            }\n\n            .fg-save-notification.show {\n                transform: translateY(0);\n                opacity: 1;\n            }\n        ",document.head.appendChild(t);const n=document.querySelector(".fg-system-buttons");n&&n.appendChild(e)},setupEventListeners:function(){const e=document.getElementById("fg-save-build-btn");e&&e.addEventListener("click",(()=>{this.saveBuild(),this.showNotification("Build saved successfully!")}))},loadSavedBuildToStore:function(){const e=localStorage.getItem(this.STORAGE_KEY);if(!e)return!1;try{const t=JSON.parse(e);return BuildSaverStore.setBuildData(t),!0}catch(e){return!1}},saveBuild:function(){try{this.verifySystemsData();const e=this.captureBuildState();return 0===Object.keys(e.systems).length?(this.showNotification("No data found to save. Have you made any changes?",!0),!1):(localStorage.setItem(this.STORAGE_KEY,JSON.stringify(e)),BuildSaverStore.setBuildData(e),!0)}catch(e){return this.showNotification("Failed to save build: "+e.message,!0),!1}},verifySystemsData:function(){if(window.EssenceRunesSystem&&window.EssenceRunesSystem.isInitialized&&(window.EssenceRunesSystem.data||(window.EssenceRunesSystem.data={}),window.EssenceRunesSystem.data.selectedRunes||(window.EssenceRunesSystem.data.selectedRunes=[]),Array.isArray(window.EssenceRunesSystem.data.selectedRunes)||(window.EssenceRunesSystem.data.selectedRunes=[])),window.HonorMedalSystem&&window.HonorMedalSystem.isInitialized){window.HonorMedalSystem.selectedStats||(window.HonorMedalSystem.selectedStats={captain:Array(4).fill(null),general:Array(6).fill(null),commander:Array(8).fill(null),hero:Array(10).fill(null),legend:Array(12).fill(null)});["captain","general","commander","hero","legend"].forEach((e=>{window.HonorMedalSystem.selectedStats[e]||(window.HonorMedalSystem.selectedStats[e]=Array("captain"===e?4:"general"===e?6:"commander"===e?8:"hero"===e?10:12).fill(null))}))}if(window.CostumesSystem&&window.CostumesSystem.isInitialized){window.CostumesSystem.selectedStats||(window.CostumesSystem.selectedStats={generic:Array(3).fill(null),forceWing:Array(3).fill(null),vehicle:Array(3).fill(null)});["generic","forceWing","vehicle"].forEach((e=>{window.CostumesSystem.selectedStats[e]||(window.CostumesSystem.selectedStats[e]=Array(3).fill(null))})),window.CostumesSystem.epicCraftOptions||(window.CostumesSystem.epicCraftOptions={generic:null,forceWing:null,vehicle:null})}},captureBuildState:function(){const e={timestamp:Date.now(),version:"1.0",systems:{}};return["PetSystem","EssenceRunesSystem","KarmaRunesSystem","EquipmentSystem","ClassSystem","StellarSystem","HonorMedalSystem","CostumesSystem","OverlordMasterySystem"].forEach((t=>{const n=window[t];if(!n)return;const s=t.replace("System","").replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();if("function"==typeof n.getEssentialData)try{const t=n.getEssentialData();e.systems[s]=t}catch(e){}})),e},showNotification:function(e,t=!1){const n=document.querySelector(".fg-save-notification");n&&n.remove();const s=document.createElement("div");s.className="fg-save-notification",s.innerHTML=e,t&&(s.style.backgroundColor="#8B0000"),document.body.appendChild(s),setTimeout((()=>{s.classList.add("show")}),10),setTimeout((()=>{s.classList.remove("show"),setTimeout((()=>{s.remove()}),300)}),3e3)}},document.addEventListener("DOMContentLoaded",(()=>{document.querySelector(".fg-build-planner-container")&&BuildSaver.init()}))},8002:()=>{window.BuildCompression={version:3,systems:{honor:{id:"honor",prefix:"h",encode:null,decode:null,priority:1},pet:{id:"pet",prefix:"p",encode:null,decode:null,priority:2}},encodeHonorSystem:function(e){if(!e||!e.selectedStats)return null;let t=[];t.push(this.version);for(const n in e.selectedStats){const s=e.selectedStats[n];if(!s||!s.length)continue;if(!s.some((e=>null!=e&&e.statId)))continue;const o={captain:0,general:1,commander:2,hero:3,legend:4};t.push(o[n]||0);const a=s.find((e=>null!=e&&e.statId)),r=a&&s.every((e=>!e||!e.statId||e.statId===a.statId&&e.level===a.level));let i=0;if(s.forEach(((e,t)=>{e&&e.statId&&(i|=1<<t)})),t.push((r?1:0)<<7|i>>8&127),t.push(255&i),r&&a){const e={hp:0,mp:1,attack:2,defense:3,attackRate:4,defenseRate:5,str:6,int:7,dex:8,penetration:9,accuracy:10,evasion:11,critRate:12,critDamage:13,allAttackUp:14,hpAutoHeal:15,mpAutoHeal:16,ignoreEvasion:17,ignoreAccuracy:18,ignoreDamageReduce:19,ignoreResistCritDmg:20,addDamage:21,cancelIgnorePenetration:22,cancelIgnoreDefense:23,cancelIgnoreEvasion:24,cancelIgnoreAccuracy:25,cancelIgnoreDamageReduce:26,cancelIgnoreResistCritDmg:27};t.push(e[a.statId]||0),t.push(a.level)}else s.forEach((e=>{if(!e||!e.statId)return;t.push({hp:0,mp:1,attack:2,defense:3,attackRate:4,defenseRate:5,str:6,int:7,dex:8,penetration:9,accuracy:10,evasion:11,critRate:12,critDamage:13,allAttackUp:14,hpAutoHeal:15,mpAutoHeal:16,ignoreEvasion:17,ignoreAccuracy:18,ignoreDamageReduce:19,ignoreResistCritDmg:20,addDamage:21,cancelIgnorePenetration:22,cancelIgnoreDefense:23,cancelIgnoreEvasion:24,cancelIgnoreAccuracy:25,cancelIgnoreDamageReduce:26,cancelIgnoreResistCritDmg:27}[e.statId]||0),t.push(e.level)}))}return this.arrayToBase64Url(t)},decodeHonorSystem:function(e){if(!e)return null;const t=this.base64UrlToArray(e),n={selectedStats:{captain:Array(4).fill(null),general:Array(6).fill(null),commander:Array(8).fill(null),hero:Array(10).fill(null),legend:Array(12).fill(null)}};t[0];let s=1;for(;s<t.length;){const e={0:"captain",1:"general",2:"commander",3:"hero",4:"legend"}[t[s++]]||"captain",o=t[s++],a=!!(128&o),r=(127&o)<<8|t[s++];if(a){const o=t[s++],a=t[s++],i={0:"hp",1:"mp",2:"attack",3:"defense",4:"attackRate",5:"defenseRate",6:"str",7:"int",8:"dex",9:"penetration",10:"accuracy",11:"evasion",12:"critRate",13:"critDamage",14:"allAttackUp",15:"hpAutoHeal",16:"mpAutoHeal",17:"ignoreEvasion",18:"ignoreAccuracy",19:"ignoreDamageReduce",20:"ignoreResistCritDmg",21:"addDamage",22:"cancelIgnorePenetration",23:"cancelIgnoreDefense",24:"cancelIgnoreEvasion",25:"cancelIgnoreAccuracy",26:"cancelIgnoreDamageReduce",27:"cancelIgnoreResistCritDmg"}[o]||"hp";for(let t=0;t<16;t++)r&1<<t&&n.selectedStats[e]&&t<n.selectedStats[e].length&&(n.selectedStats[e][t]={statId:i,level:a})}else for(let o=0;o<16;o++){if(!(r&1<<o))continue;const a={0:"hp",1:"mp",2:"attack",3:"defense",4:"attackRate",5:"defenseRate",6:"str",7:"int",8:"dex",9:"penetration",10:"accuracy",11:"evasion",12:"critRate",13:"critDamage",14:"allAttackUp",15:"hpAutoHeal",16:"mpAutoHeal",17:"ignoreEvasion",18:"ignoreAccuracy",19:"ignoreDamageReduce",20:"ignoreResistCritDmg",21:"addDamage",22:"cancelIgnorePenetration",23:"cancelIgnoreDefense",24:"cancelIgnoreEvasion",25:"cancelIgnoreAccuracy",26:"cancelIgnoreDamageReduce",27:"cancelIgnoreResistCritDmg"}[t[s++]]||"hp",i=t[s++];n.selectedStats[e]&&o<n.selectedStats[e].length&&(n.selectedStats[e][o]={statId:a,level:i})}}return n},arrayToBase64Url:function(e){const t=String.fromCharCode.apply(null,e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")},base64UrlToArray:function(e){const t=e.replace(/-/g,"+").replace(/_/g,"/"),n=atob(t),s=new Uint8Array(n.length);for(let e=0;e<n.length;e++)s[e]=n.charCodeAt(e);return s},encodeBuildData:function(e){if(!e||!e.systems)return null;if(e.systems.honor){const t=this.encodeHonorSystem(e.systems.honor);if(t)return"h"+t}const t=JSON.stringify(e);return LZString.compressToBase64(t)},encodePetSystem:function(e){if(!e||!e.selectedStats)return null;let t=[];t.push(this.version);const n=["normal","covenant","trust"];for(let s=0;s<n.length;s++){const o=n[s],a=e.selectedStats[o];if(!a||!a.length)continue;if(!a.some((e=>null!=e)))continue;t.push(s);const r=a.find((e=>null!=e)),i=r&&a.every((e=>!e||e.id===r.id&&e.value===r.value));let c=0;if(a.forEach(((e,t)=>{e&&(c|=1<<t)})),t.push((i?1:0)<<7|c>>8&127),t.push(255&c),i&&r){let e={},n=0;if(window.PetSystemData&&window.PetSystemData.statIdMap)e=window.PetSystemData.statIdMap,n=e[r.id]||0;else{n={hp:0,mp:1,attack:2,defense:3,attackRate:4,defenseRate:5,str:6,int:7,dex:8,penetration:9,accuracy:10,evasion:11,critRate:12,critDamage:13,allAttackUp:14,hpAutoHeal:15,mpAutoHeal:16}[r.id]||0}t.push(n);const s=r.value||0;t.push(s>>8&255),t.push(255&s)}else a.forEach((e=>{if(!e)return;let n={},s=0;if(window.PetSystemData&&window.PetSystemData.statIdMap)n=window.PetSystemData.statIdMap,s=n[e.id]||0;else{s={hp:0,mp:1,attack:2,defense:3,attackRate:4,defenseRate:5,str:6,int:7,dex:8,penetration:9,accuracy:10,evasion:11,critRate:12,critDamage:13,allAttackUp:14,hpAutoHeal:15,mpAutoHeal:16}[e.id]||0}t.push(s);const o=e.value||0;t.push(o>>8&255),t.push(255&o)}))}return this.arrayToBase64Url(t)},decodePetSystem:function(e){if(!e)return null;const t=this.base64UrlToArray(e),n={selectedStats:{normal:Array(10).fill(null),covenant:Array(10).fill(null),trust:Array(10).fill(null)}};let s=1;for(;s<t.length;){const e=["normal","covenant","trust"][t[s++]]||"normal",o=t[s++],a=!!(128&o),r=(127&o)<<8|t[s++];if(a){const o=t[s++],a=t[s++]<<8|t[s++];let i="hp";if(window.PetSystemData&&window.PetSystemData.reverseStatIdMap)i=window.PetSystemData.reverseStatIdMap[o]||"hp";else{i={0:"hp",1:"mp",2:"attack",3:"defense",4:"attackRate",5:"defenseRate",6:"str",7:"int",8:"dex",9:"penetration",10:"accuracy",11:"evasion",12:"critRate",13:"critDamage",14:"allAttackUp",15:"hpAutoHeal",16:"mpAutoHeal"}[o]||"hp"}for(let t=0;t<10;t++)r&1<<t&&n.selectedStats[e]&&(n.selectedStats[e][t]={id:i,value:a})}else for(let o=0;o<10;o++){if(!(r&1<<o))continue;const a=t[s++],i=t[s++]<<8|t[s++];let c="hp";if(window.PetSystemData&&window.PetSystemData.reverseStatIdMap)c=window.PetSystemData.reverseStatIdMap[a]||"hp";else{c={0:"hp",1:"mp",2:"attack",3:"defense",4:"attackRate",5:"defenseRate",6:"str",7:"int",8:"dex",9:"penetration",10:"accuracy",11:"evasion",12:"critRate",13:"critDamage",14:"allAttackUp",15:"hpAutoHeal",16:"mpAutoHeal"}[a]||"hp"}n.selectedStats[e]&&(n.selectedStats[e][o]={id:c,value:i})}}return n},registerSystem:function(e,t,n,s,o){if(!e||!t||"function"!=typeof n||"function"!=typeof s)return!1;for(const e in this.systems)if(this.systems[e].prefix===t)return!1;return this.systems[e]={id:e,prefix:t,encode:n,decode:s,priority:o||999},!0},encodeMultiSystem:function(e){if(!e||!e.systems)return null;let t=[];t.push(this.version);const n=[],s=Object.keys(this.systems).sort(((e,t)=>(this.systems[e].priority||999)-(this.systems[t].priority||999)));for(const t of s){const s=this.systems[t],o=e.systems[t];if(!o||!s.encode)continue;const a=s.encode.call(this,o);a&&n.push({prefix:s.prefix,data:a})}if(0===n.length)return null;if(1===n.length)return n[0].prefix+n[0].data;t.push(n.length);for(const e of n){t.push(e.prefix.charCodeAt(0));const n=this.base64UrlToArray(e.data);t.push(n.length>>8&255),t.push(255&n.length),t.push(...n)}return"m"+this.arrayToBase64Url(t)},decodeMultiSystem:function(e){if(!e||e.length<2)return null;const t=e.substring(1),n=this.base64UrlToArray(t);let s=1;const o=n[s++],a={timestamp:Date.now(),version:"1.0",systems:{}};for(let e=0;e<o;e++){const e=n[s++],t=String.fromCharCode(e),o=n[s++]<<8|n[s++],r=n.slice(s,s+o);s+=o;const i=this.arrayToBase64Url(r);let c=null;for(const e in this.systems)if(this.systems[e].prefix===t){c=e;break}if(!c||!this.systems[c].decode)continue;const l=this.systems[c].decode.call(this,i);l&&(a.systems[c]=l)}return a},encodeBuildData:function(e){if(!e||!e.systems)return null;this.systems.honor.encode||(this.systems.honor.encode=this.encodeHonorSystem,this.systems.honor.decode=this.decodeHonorSystem),this.systems.pet.encode||(this.systems.pet.encode=this.encodePetSystem,this.systems.pet.decode=this.decodePetSystem);const t=Object.keys(e.systems).filter((t=>e.systems[t]&&this.systems[t]&&this.systems[t].encode));if(t.length>1){const t=this.encodeMultiSystem(e);if(t)return t}else if(1===t.length){const n=t[0],s=this.systems[n];if(s&&s.encode){const t=s.encode.call(this,e.systems[n]);if(t)return s.prefix+t}}const n=JSON.stringify(e);return LZString.compressToBase64(n)},decodeBuildData:function(e){if(!e)return null;if(this.systems.honor.encode||(this.systems.honor.encode=this.encodeHonorSystem,this.systems.honor.decode=this.decodeHonorSystem),this.systems.pet.encode||(this.systems.pet.encode=this.encodePetSystem,this.systems.pet.decode=this.decodePetSystem),e.startsWith("m"))return this.decodeMultiSystem(e);for(const t in this.systems){const n=this.systems[t];if(e.startsWith(n.prefix)&&n.decode){const s=e.substring(1),o=n.decode.call(this,s);if(o)return{timestamp:Date.now(),version:"1.0",systems:{[t]:o}}}}try{const t=LZString.decompressFromBase64(e);if(!t)throw new Error("Decompression failed");return JSON.parse(t)}catch(e){return null}}}},9045:()=>{window.BuildSharer={init:function(){this.createShareButton(),this.setupEventListeners(),this.checkForSharedBuild()},createShareButton:function(){const e=document.createElement("div");e.className="fg-system-button fg-share-build-button",e.id="fg-share-build-btn",e.innerHTML='\n            <span class="icon share-icon">\n                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="white">\n                    <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/>\n                </svg>\n            </span>\n            <span>Share Build</span>\n        ';const t=document.createElement("style");t.textContent="\n            .fg-share-build-button {\n                background-color: #4CAF50 !important;\n                margin-top: 10px !important;\n            }\n\n            .fg-share-build-button:hover {\n                background-color: #45a049 !important;\n            }\n\n            .fg-share-dialog {\n                position: fixed;\n                top: 0;\n                left: 0;\n                width: 100%;\n                height: 100%;\n                background: rgba(0, 0, 0, 0.7);\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                z-index: 9999;\n            }\n\n            .fg-share-dialog-content {\n                background: #2a2a36;\n                padding: 20px;\n                border-radius: 8px;\n                max-width: 500px;\n                width: 90%;\n            }\n\n            .fg-share-url-container {\n                display: flex;\n                margin: 15px 0;\n            }\n\n            .fg-share-url {\n                flex: 1;\n                padding: 8px;\n                border: 1px solid #444;\n                background: #1a1a24;\n                color: #fff;\n                border-radius: 4px 0 0 4px;\n            }\n\n            .fg-copy-button {\n                padding: 8px 15px;\n                background: #ff5500;\n                color: white;\n                border: none;\n                border-radius: 0 4px 4px 0;\n                cursor: pointer;\n            }\n\n            .fg-close-button {\n                padding: 8px 15px;\n                background: #444;\n                color: white;\n                border: none;\n                border-radius: 4px;\n                cursor: pointer;\n                float: right;\n            }\n\n\n        ",document.head.appendChild(t);const n=document.querySelector(".fg-system-buttons");n&&n.appendChild(e)},setupEventListeners:function(){const e=document.getElementById("fg-share-build-btn");e&&e.addEventListener("click",(()=>{this.ensureSystemsInitialized(),this.createShareableLink()}))},ensureSystemsInitialized:function(){if(!["HonorMedalSystem","StellarSystem","PetSystem","CostumesSystem","EssenceRunesSystem","AchievementSystem"].some((e=>{const t=window[e];return t&&t.isInitialized}))){const e=document.querySelectorAll(".fg-system-button:not(.fg-share-build-button)");e.length>0&&e[0].click()}},createShareableLink:function(){try{const e=this.captureSpecificSystems();if(!e.systems||0===Object.keys(e.systems).length)return this.showNotification("No system data found. Make sure you have configured at least one system.",!0),null;const t=this.encodeBuildData(e);if(!t)throw new Error("Failed to encode build data");let n,s;return window.forceguidesPlannerData&&forceguidesPlannerData.buildPlannerPageUrl?(n=forceguidesPlannerData.buildPlannerPageUrl,s=n.includes("?")?n+"&build="+encodeURIComponent(t):n+"?build="+encodeURIComponent(t)):(n=window.location.href.split("?")[0],s=n+"?build="+encodeURIComponent(t)),this.showShareDialog(s),s}catch(e){return this.showNotification("Failed to create shareable link",!0),null}},captureSpecificSystems:function(){const e={timestamp:Date.now(),version:"1.0",systems:{}};return["HonorMedalSystem","StellarSystem","PetSystem","CostumesSystem","EssenceRunesSystem"].forEach((t=>{const n=window[t];if(!n||!n.isInitialized)return;const s=t.replace("System","").replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();if("function"==typeof n.getEssentialData)try{"function"==typeof n.saveToStore&&n.saveToStore();const t=n.getEssentialData();e.systems[s]=t}catch(e){}})),e},encodeBuildData:function(e){try{if(window.BuildCompression&&"function"==typeof BuildCompression.encodeBuildData){const t=BuildCompression.encodeBuildData(e);if(t)return t}const t=JSON.stringify(e);return LZString.compressToBase64(t)}catch(e){return null}},decodeBuildData:function(e){try{try{-1!==e.indexOf("%")&&(e=decodeURIComponent(e))}catch(e){}if(window.BuildCompression&&"function"==typeof BuildCompression.decodeBuildData&&(e.startsWith("h")||e.length<100)){const t=BuildCompression.decodeBuildData(e);if(t)return t}const t=LZString.decompressFromBase64(e);if(!t)throw new Error("Decompression failed");return JSON.parse(t)}catch(e){throw e}},showShareDialog:function(e){const t=document.createElement("div");t.className="fg-share-dialog",t.innerHTML=`\n            <div class="fg-share-dialog-content">\n                <h3>Share This Build</h3>\n                <p>Copy this link to share your build with others:</p>\n                <div class="fg-share-url-container">\n                    <input type="text" value="${e}" readonly class="fg-share-url" />\n                    <button class="fg-copy-button">Copy</button>\n                </div>\n                <button class="fg-close-button">Close</button>\n            </div>\n        `,document.body.appendChild(t);const n=t.querySelector(".fg-copy-button"),s=t.querySelector(".fg-close-button"),o=t.querySelector(".fg-share-url");n.addEventListener("click",(()=>{o.select(),document.execCommand("copy"),n.textContent="Copied!",setTimeout((()=>{n.textContent="Copy"}),2e3)})),s.addEventListener("click",(()=>{t.remove()}))},checkForSharedBuild:function(){const e=new URLSearchParams(window.location.search).get("build");if(e)try{const t=this.decodeBuildData(e);return this.ensureSystemsInitialized(),setTimeout((()=>{this.loadSharedBuild(t)}),500),!0}catch(e){this.showNotification("Failed to load shared build",!0)}return!1},loadSharedBuild:function(e){if(!e||!e.systems)return this.showNotification("Invalid build data format",!0),!1;const t=[];for(const n in e.systems){const s=e.systems[n],o=n.replace(/-([a-z])/g,((e,t)=>t.toUpperCase())).replace(/^([a-z])/,((e,t)=>t.toUpperCase()))+"System",a=window[o];if(a&&a.isInitialized&&"function"==typeof a.loadFromData)try{a.loadFromData(s)&&t.push(n)}catch(e){}}return BuildPlanner&&"function"==typeof BuildPlanner.calculateTotalStats&&BuildPlanner.calculateTotalStats(),t.length>0?(this.showNotification(`Build loaded successfully: ${t.join(", ")}`),!0):(this.showNotification("No systems were loaded. Try clicking on a system tab first.",!0),!1)},showNotification:function(e,t=!1){const n=document.querySelector(".fg-share-notification");n&&n.remove();const s=document.createElement("div");s.className="fg-share-notification",s.innerHTML=e,t&&(s.style.backgroundColor="#8B0000"),s.style.position="fixed",s.style.top="20px",s.style.right="20px",s.style.backgroundColor=t?"#8B0000":"#2a2a36",s.style.color="#fff",s.style.padding="12px 16px",s.style.borderRadius="4px",s.style.boxShadow="0 4px 12px rgba(0,0,0,0.2)",s.style.zIndex="9999",s.style.transform="translateY(-100px)",s.style.opacity="0",s.style.transition="all 0.3s ease",document.body.appendChild(s),setTimeout((()=>{s.style.transform="translateY(0)",s.style.opacity="1"}),10),setTimeout((()=>{s.style.transform="translateY(-100px)",s.style.opacity="0",setTimeout((()=>{s.remove()}),300)}),3e3)}},document.addEventListener("DOMContentLoaded",(()=>{document.querySelector(".fg-build-planner-container")&&BuildSharer.init()}))}},t={};function n(s){var o=t[s];if(void 0!==o)return o.exports;var a=t[s]={exports:{}};return e[s](a,a.exports,n),a.exports}n(5616),n(8002),n(7637);n(9045)})();