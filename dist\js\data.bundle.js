window.PetSystemData={tierStats:{normal:[{id:"hp",value:80},{id:"mp",value:30},{id:"allAttackUp",value:10},{id:"defense",value:12},{id:"attackRate",value:80},{id:"defenseRate",value:45},{id:"critDamage",value:2},{id:"critRate",value:1},{id:"minDamage",value:1},{id:"maxHpSteal",value:5},{id:"maxMpSteal",value:5},{id:"maxCritRate",value:1},{id:"allSkillAmp",value:1},{id:"hpAbsorb",value:1},{id:"mpAbsorb",value:1},{id:"evasion",value:100},{id:"hpAutoHeal",value:5},{id:"mpAutoHeal",value:5},{id:"addDamage",value:10},{id:"skillExp",value:10},{id:"alzDropAmount",value:10},{id:"2SlotDropRate",value:2},{id:"resistCritRate",value:1},{id:"resistCritDmg",value:2},{id:"resistUnableToMove",value:2},{id:"resistDown",value:2},{id:"resistKnockback",value:2},{id:"resistStun",value:2}],covenant:[{id:"hp",value:120},{id:"mp",value:50},{id:"allAttackUp",value:15},{id:"defense",value:20},{id:"attackRate",value:100},{id:"defenseRate",value:60},{id:"critDamage",value:4},{id:"allSkillAmp",value:2},{id:"hpAutoHeal",value:10},{id:"mpAutoHeal",value:10},{id:"resistCritDmg",value:5},{id:"resistDown",value:2},{id:"resistKnockback",value:2},{id:"resistStun",value:2},{id:"addDamage",value:20},{id:"accuracy",value:80},{id:"penetration",value:11},{id:"ignorePenetration",value:12},{id:"resistSkillAmp",value:2},{id:"ignoreAccuracy",value:100},{id:"damageReduce",value:12},{id:"ignoreDamageReduce",value:25},{id:"str",value:10},{id:"int",value:10},{id:"dex",value:10},{id:"ignoreResistCritDmg",value:6},{id:"ignoreResistSkillAmp",value:3}],trust:[{id:"hp",value:160},{id:"allAttackUp",value:20},{id:"defense",value:30},{id:"attackRate",value:120},{id:"defenseRate",value:75},{id:"hpAutoHeal",value:15},{id:"mpAutoHeal",value:15},{id:"addDamage",value:30},{id:"resistCritDmg",value:8},{id:"resistDown",value:2},{id:"resistKnockback",value:2},{id:"resistStun",value:2},{id:"accuracy",value:100},{id:"penetration",value:15},{id:"ignorePenetration",value:16},{id:"resistSkillAmp",value:3},{id:"ignoreAccuracy",value:120},{id:"damageReduce",value:15},{id:"ignoreDamageReduce",value:35},{id:"ignoreResistCritDmg",value:8},{id:"ignoreResistSkillAmp",value:4},{id:"ignoreResistCritRate",value:1},{id:"normalDamageUp",value:3},{id:"ignoreResistKnockback",value:3},{id:"ignoreResistDown",value:3},{id:"ignoreResistStun",value:3},{id:"auraDurationIncrease",value:2}]},statIdMap:{hp:0,mp:1,attack:2,defense:3,attackRate:4,defenseRate:5,str:6,int:7,dex:8,penetration:9,accuracy:10,evasion:11,critRate:12,critDamage:13,allAttackUp:14,hpAutoHeal:15,mpAutoHeal:16,minDamage:17,maxHpSteal:18,maxMpSteal:19,maxCritRate:20,allSkillAmp:21,hpAbsorb:22,mpAbsorb:23,addDamage:24,skillExp:25,alzDropAmount:26,"2SlotDropRate":27,resistCritRate:28,resistCritDmg:29,resistUnableToMove:30,resistDown:31,resistKnockback:32,resistStun:33,ignorePenetration:34,resistSkillAmp:35,ignoreAccuracy:36,damageReduce:37,ignoreDamageReduce:38,ignoreResistCritDmg:39,ignoreResistSkillAmp:40,ignoreResistCritRate:41,normalDamageUp:42,ignoreResistKnockback:43,ignoreResistDown:44,ignoreResistStun:45,auraDurationIncrease:46},reverseStatIdMap:{0:"hp",1:"mp",2:"attack",3:"defense",4:"attackRate",5:"defenseRate",6:"str",7:"int",8:"dex",9:"penetration",10:"accuracy",11:"evasion",12:"critRate",13:"critDamage",14:"allAttackUp",15:"hpAutoHeal",16:"mpAutoHeal",17:"minDamage",18:"maxHpSteal",19:"maxMpSteal",20:"maxCritRate",21:"allSkillAmp",22:"hpAbsorb",23:"mpAbsorb",24:"addDamage",25:"skillExp",26:"alzDropAmount",27:"2SlotDropRate",28:"resistCritRate",29:"resistCritDmg",30:"resistUnableToMove",31:"resistDown",32:"resistKnockback",33:"resistStun",34:"ignorePenetration",35:"resistSkillAmp",36:"ignoreAccuracy",37:"damageReduce",38:"ignoreDamageReduce",39:"ignoreResistCritDmg",40:"ignoreResistSkillAmp",41:"ignoreResistCritRate",42:"normalDamageUp",43:"ignoreResistKnockback",44:"ignoreResistDown",45:"ignoreResistStun",46:"auraDurationIncrease"}},window.enhancedStatData={daedalus:[{id:"str",name:"STR",values:[1,2,3,4,5]},{id:"int",name:"INT",values:[1,2,3,4,5]},{id:"dex",name:"DEX",values:[1,2,3,4,5]},{id:"ignoreAccuracy",name:"Ignore Accuracy",values:[2,4,6,8,10]},{id:"ignoreEvasion",name:"Ignore Evasion",values:[2,4,6,8,10]},{id:"pveDamageReduce",name:"PvE DMG Reduction",values:[1,2,3,4,5]},{id:"hp",name:"HP",values:[10,15,20,30,50]},{id:"pveDefense",name:"PvE Defense",values:[3,4,6,9,16]},{id:"resistCritDmg",name:"Resist Crit. DMG",values:[1,2,3,4,5]},{id:"ignoreDamageReduce",name:"Ignore DMG Reduction",values:[1,2,3,6,10]},{id:"hpAutoHeal",name:"HP Auto Heal",values:[1,2,3,4,5]},{id:"pvePenetration",name:"PvE Penetration",values:[2,3,5,8,15]}],icarus:[{id:"str",name:"STR",values:[1,2,3,4,5]},{id:"int",name:"INT",values:[1,2,3,4,5]},{id:"dex",name:"DEX",values:[1,2,3,4,5]},{id:"ignoreAccuracy",name:"Ignore Accuracy",values:[4,6,8,10,12]},{id:"ignoreEvasion",name:"Ignore Evasion",values:[4,6,8,10,12]},{id:"pveDamageReduce",name:"PvE DMG Reduction",values:[1,2,3,4,6]},{id:"hp",name:"HP",values:[20,25,35,50,85]},{id:"pveDefense",name:"PvE Defense",values:[4,5,8,11,20]},{id:"pvpResistSkillAmp",name:"PvP Resist All Skill Amp.",values:[1,2,3,4,5]},{id:"normalDamageUp",name:"Normal DMG Up",values:[1,2,3,4,5]},{id:"pveIgnorePenetration",name:"PvE Ignore Penetration",values:[1,2,3,6,10]},{id:"pveCritDamage",name:"PvE Crit. DMG",values:[1,2,3,4,7]}],vulcanos:[{id:"str",name:"STR",values:[1,2,3,4,5]},{id:"int",name:"INT",values:[1,2,3,4,5]},{id:"dex",name:"DEX",values:[1,2,3,4,5]},{id:"ignoreAccuracy",name:"Ignore Accuracy",values:[6,8,10,12,14]},{id:"ignoreEvasion",name:"Ignore Evasion",values:[6,8,10,12,14]},{id:"pveDamageReduce",name:"PvE DMG Reduction",values:[1,2,3,4,7]},{id:"hp",name:"HP",values:[20,30,40,55,100]},{id:"defense",name:"Defense",values:[4,6,10,15,25]},{id:"resistCritDmg",name:"Resist Crit. DMG",values:[1,2,3,4,6]},{id:"normalDamageUp",name:"Normal DMG Up",values:[1,2,3,4,6]},{id:"pveIgnorePenetration",name:"PvE Ignore Penetration",values:[2,3,5,7,13]},{id:"allAttackUp",name:"All Attack Up",values:[4,8,13,20,40]}],minerva:[{id:"str",name:"STR",values:[1,2,3,4,5]},{id:"int",name:"INT",values:[1,2,3,4,5]},{id:"dex",name:"DEX",values:[1,2,3,4,5]},{id:"ignoreAccuracy",name:"Ignore Accuracy",values:[8,10,12,14,16]},{id:"ignoreEvasion",name:"Ignore Evasion",values:[8,10,12,14,16]},{id:"pveDamageReduce",name:"PvE DMG Reduction",values:[1,2,3,5,8]},{id:"hp",name:"HP",values:[30,35,45,60,115]},{id:"defense",name:"Defense",values:[4,6,10,16,30]},{id:"pvpResistSkillAmp",name:"PvP Resist All Skill Amp.",values:[1,2,3,4,5]},{id:"pveIgnorePenetration",name:"PvE Ignore Penetration",values:[3,4,6,9,16]},{id:"cancelIgnorePenetration",name:"Cancel Ignore Penetration",values:[1,2,3,4,5]},{id:"penetration",name:"Penetration",values:[2,3,5,8,15]}],pluto:[{id:"str",name:"STR",values:[1,2,3,4,5]},{id:"int",name:"INT",values:[1,2,3,4,5]},{id:"dex",name:"DEX",values:[1,2,3,4,5]},{id:"ignoreAccuracy",name:"Ignore Accuracy",values:[12,14,16,18,20]},{id:"ignoreEvasion",name:"Ignore Evasion",values:[12,14,16,18,20]},{id:"pveDamageReduce",name:"PvE DMG Reduction",values:[1,2,3,5,9]},{id:"hp",name:"HP",values:[35,40,50,65,125]},{id:"defense",name:"Defense",values:[5,8,12,18,35]},{id:"resistCritDmg",name:"Resist Crit. DMG",values:[1,2,3,4,7]},{id:"pveIgnorePenetration",name:"PvE Ignore Penetration",values:[3,5,8,13,20]},{id:"cancelIgnorePenetration",name:"Cancel Ignore Penetration",values:[1,2,3,5,8]},{id:"critDamage",name:"Crit. DMG",values:[1,2,3,4,7]}]},window.nodeColors={desire:{name:"Desire",cssColor:"#ffcc00",borderColor:"#ffd700",glowColor:"rgba(255, 215, 0, 0.6)"},grief:{name:"Grief",cssColor:"#ff8800",borderColor:"#ff9933",glowColor:"rgba(255, 153, 51, 0.6)"},fury:{name:"Fury",cssColor:"#ff3300",borderColor:"#ff4500",glowColor:"rgba(255, 69, 0, 0.6)"},oblivion:{name:"Oblivion",cssColor:"#3366ff",borderColor:"#4477ff",glowColor:"rgba(68, 119, 255, 0.6)"},emptiness:{name:"Emptiness",cssColor:"#9933cc",borderColor:"#aa44ee",glowColor:"rgba(170, 68, 238, 0.6)"}},window.lineEffects={daedalus:{desire:{name:"Daedalus of Desire",effect1:{statId:"normalDamageUp",value:1},effect2:{statId:"defense",value:9}},grief:{name:"Daedalus of Grief",effect1:{statId:"normalDamageUp",value:2},effect2:{statId:"defense",value:18}},fury:{name:"Daedalus of Fury",effect1:{statId:"normalDamageUp",value:3},effect2:{statId:"defense",value:30}},oblivion:{name:"Daedalus of Oblivion",effect1:{statId:"normalDamageUp",value:6},effect2:{statId:"defense",value:45}},emptiness:{name:"Daedalus of Emptiness",effect1:{statId:"normalDamageUp",value:14},effect2:{statId:"defense",value:105}}},icarus:{desire:{name:"Icarus of Desire",effect1:{statId:"pvpAllSkillAmp",value:1},effect2:{statId:"resistSkillAmp",value:1}},grief:{name:"Icarus of Grief",effect1:{statId:"pvpAllSkillAmp",value:2},effect2:{statId:"resistSkillAmp",value:2}},fury:{name:"Icarus of Fury",effect1:{statId:"pvpAllSkillAmp",value:3},effect2:{statId:"resistSkillAmp",value:3}},oblivion:{name:"Icarus of Oblivion",effect1:{statId:"pvpAllSkillAmp",value:4},effect2:{statId:"resistSkillAmp",value:6}},emptiness:{name:"Icarus of Emptiness",effect1:{statId:"pvpAllSkillAmp",value:9},effect2:{statId:"resistSkillAmp",value:12}}},vulcanos:{desire:{name:"Vulcanos of Desire",effect1:{statId:"allAttackUp",value:10},effect2:{statId:"damageReduce",value:5}},grief:{name:"Vulcanos of Grief",effect1:{statId:"allAttackUp",value:20},effect2:{statId:"damageReduce",value:10}},fury:{name:"Vulcanos of Fury",effect1:{statId:"allAttackUp",value:35},effect2:{statId:"damageReduce",value:18}},oblivion:{name:"Vulcanos of Oblivion",effect1:{statId:"allAttackUp",value:55},effect2:{statId:"damageReduce",value:30}},emptiness:{name:"Vulcanos of Emptiness",effect1:{statId:"allAttackUp",value:120},effect2:{statId:"damageReduce",value:70}}},minerva:{desire:{name:"Minerva of Desire",effect1:{statId:"pvpCritDamage",value:2},effect2:{statId:"resistCritDmg",value:2}},grief:{name:"Minerva of Grief",effect1:{statId:"pvpCritDamage",value:4},effect2:{statId:"resistCritDmg",value:4}},fury:{name:"Minerva of Fury",effect1:{statId:"pvpCritDamage",value:8},effect2:{statId:"resistCritDmg",value:8}},oblivion:{name:"Minerva of Oblivion",effect1:{statId:"pvpCritDamage",value:13},effect2:{statId:"resistCritDmg",value:13}},emptiness:{name:"Minerva of Emptiness",effect1:{statId:"pvpCritDamage",value:30},effect2:{statId:"resistCritDmg",value:30}}},pluto:{desire:{name:"Pluto of Desire",effect1:{statId:"critDamage",value:2},effect2:{statId:"ignorePenetration",value:9}},grief:{name:"Pluto of Grief",effect1:{statId:"critDamage",value:4},effect2:{statId:"ignorePenetration",value:18}},fury:{name:"Pluto of Fury",effect1:{statId:"critDamage",value:8},effect2:{statId:"ignorePenetration",value:30}},oblivion:{name:"Pluto of Oblivion",effect1:{statId:"critDamage",value:13},effect2:{statId:"ignorePenetration",value:45}},emptiness:{name:"Pluto of Emptiness",effect1:{statId:"critDamage",value:30},effect2:{statId:"ignorePenetration",value:100}}}},window.HonorMedalData={maxLevel:10,ranks:{captain:{name:"Captain",slots:4},general:{name:"General",slots:6},commander:{name:"Commander",slots:8},hero:{name:"Hero",slots:10},legend:{name:"Legend",slots:12}},rankStats:{captain:[{id:"hp",chance:8,values:[6,12,18,24,30,36,42,48,54,60]},{id:"mp",chance:8,values:[3,6,9,12,15,18,21,24,27,30]},{id:"attackRate",chance:8,values:[5,10,15,20,25,30,35,40,45,50]},{id:"defenseRate",chance:8,values:[4,8,12,16,20,24,28,32,36,40]},{id:"hpAutoHeal",chance:11,values:[1,2,2,3,3,4,4,5,5,6]},{id:"mpAutoHeal",chance:11,values:[1,2,2,3,3,4,4,5,5,6]},{id:"str",chance:5,values:[1,2,3,4,5,6,7,8,9,10]},{id:"int",chance:5,values:[1,2,3,4,5,6,7,8,9,10]},{id:"dex",chance:5,values:[1,2,3,4,5,6,7,8,9,10]},{id:"ignoreDamageReduce",chance:7,values:[1,2,3,4,5,6,7,8,9,10]},{id:"ignorePenetration",chance:5,values:[1,2,2,3,3,4,4,5,5,6]},{id:"evasion",chance:6,values:[4,8,12,16,20,24,28,32,36,40]},{id:"accuracy",chance:7,values:[5,10,15,20,25,30,35,40,45,50]},{id:"damageReduce",chance:6,values:[1,2,3,4,5,6,7,8,9,10]}],general:[{id:"mp",chance:8,values:[4,8,12,16,20,24,28,32,36,40]},{id:"defense",chance:8,values:[6,7,8,9,10,11,12,13,14,15]},{id:"attackRate",chance:9,values:[6,12,18,24,30,36,42,48,54,60]},{id:"defenseRate",chance:9,values:[5,10,15,20,25,30,35,40,45,50]},{id:"hpAutoHeal",chance:12,values:[1,2,3,4,5,6,7,8,9,10]},{id:"mpAutoHeal",chance:12,values:[1,2,3,4,5,6,7,8,9,10]},{id:"allAttackUp",chance:7,values:[6,7,8,9,10,11,12,13,14,15]},{id:"ignoreResistCritDmg",chance:6,values:[2,2,2,3,3,3,4,4,4,5]},{id:"addDamage",chance:8,values:[1,2,3,4,5,6,7,8,9,10]},{id:"resistCritDmg",chance:5,values:[2,2,2,2,3,3,3,3,3,4]},{id:"resistSkillAmp",chance:5,values:[1,1,1,1,1,1,1,1,1,2]},{id:"ignoreEvasion",chance:5,values:[6,12,18,24,30,36,42,48,54,60]},{id:"ignoreAccuracy",chance:4,values:[5,10,15,20,25,30,35,40,45,50]},{id:"critDamage",chance:2,values:[2,2,2,2,3,3,3,3,3,4]}],commander:[{id:"hp",chance:8,values:[8,16,24,32,40,48,56,64,72,80]},{id:"mp",chance:8,values:[5,10,15,20,25,30,35,40,45,50]},{id:"attackRate",chance:7,values:[7,14,21,28,35,42,49,56,63,70]},{id:"defenseRate",chance:7,values:[6,12,18,24,30,36,42,48,54,60]},{id:"ignoreEvasion",chance:5,values:[7,14,21,28,35,42,49,56,63,70]},{id:"ignoreAccuracy",chance:3,values:[6,12,18,24,30,36,42,48,54,60]},{id:"ignoreDamageReduce",chance:5,values:[2,4,6,8,10,12,14,16,18,20]},{id:"defense",chance:8,values:[2,4,6,8,10,12,14,16,18,20]},{id:"damageReduce",chance:5,values:[1,2,3,4,5,7,9,11,13,15]},{id:"allAttackUp",chance:7,values:[2,4,6,8,10,12,14,16,18,20]},{id:"resistCritDmg",chance:4,values:[2,2,2,3,3,3,4,4,4,5]},{id:"resistDown",chance:8,values:[1,1,1,1,2,2,2,2,2,3]},{id:"resistKnockback",chance:8,values:[1,1,1,1,2,2,2,2,2,3]},{id:"resistStun",chance:8,values:[1,1,1,1,2,2,2,2,2,3]},{id:"resistSkillAmp",chance:4,values:[1,1,1,2,2,2,3,3,3,4]},{id:"allSkillAmp",chance:2,values:[1,1,1,1,2,2,2,2,2,3]},{id:"accuracy",chance:3,values:[7,14,21,28,35,42,49,56,63,70]}],hero:[{id:"mp",chance:8,values:[6,12,18,24,30,36,42,48,54,60]},{id:"defense",chance:8,values:[2,4,6,8,10,12,15,18,21,25]},{id:"attackRate",chance:9,values:[8,16,24,32,40,48,56,64,72,80]},{id:"defenseRate",chance:9,values:[7,14,21,28,35,42,49,56,63,70]},{id:"ignoreEvasion",chance:5,values:[8,16,24,32,40,48,56,64,72,80]},{id:"ignoreAccuracy",chance:3,values:[7,14,21,28,35,42,49,56,63,70]},{id:"ignoreDamageReduce",chance:5,values:[3,6,9,12,15,18,21,24,27,30]},{id:"penetration",chance:2,values:[1,2,3,4,5,7,9,11,13,15]},{id:"resistSkillAmp",chance:5,values:[1,1,1,2,2,2,3,3,3,4]},{id:"allAttackUp",chance:7,values:[7,9,11,13,15,17,19,21,23,25]},{id:"ignoreResistSkillAmp",chance:7,values:[1,1,1,1,2,2,2,2,2,3]},{id:"ignoreResistCritDmg",chance:8,values:[2,3,3,4,4,5,5,6,6,7]},{id:"ignoreResistDown",chance:8,values:[2,2,2,3,3,3,4,4,4,5]},{id:"ignoreResistKnockback",chance:8,values:[2,2,2,3,3,3,4,4,4,5]},{id:"ignoreResistStun",chance:8,values:[2,2,2,3,3,3,4,4,4,5]}],legend:[{id:"hp",chance:11,values:[18,27,36,45,54,63,72,81,90,100]},{id:"defense",chance:9,values:[8,10,12,14,16,18,21,24,27,30]},{id:"attackRate",chance:7,values:[16,24,32,40,48,56,64,72,80,90]},{id:"defenseRate",chance:7,values:[14,21,28,35,42,49,56,63,70,80]},{id:"ignoreEvasion",chance:6,values:[16,24,32,40,48,56,64,72,80,90]},{id:"accuracy",chance:5,values:[14,21,28,35,42,49,56,63,70,80]},{id:"ignoreDamageReduce",chance:12,values:[6,9,12,15,18,21,24,27,30,35]},{id:"cancelIgnorePenetration",chance:2,values:[2,3,4,5,7,9,11,13,15,18]},{id:"cancelIgnoreDamageReduce",chance:10,values:[6,9,12,15,18,21,24,27,30,35]},{id:"allAttackUp",chance:6,values:[8,10,12,14,16,18,21,24,27,30]},{id:"ignoreResistCritDmg",chance:5,values:[2,3,3,4,4,5,5,6,7,8]},{id:"resistSuppression",chance:10,values:[1,1,1,1,1,2,2,2,2,2]},{id:"resistSilence",chance:10,values:[1,1,1,1,1,2,2,2,2,2]}]}},window.EssenceRuneData={runeDefinitions:{hp:{baseStatType:"hp",iconId:"hp",variants:[{id:"hp",name:"HP I",tier:1,maxLevel:20,valuePerLevel:[50,100,150,200,250,300,350,400,450,500,550,600,650,700,750,800,850,900,950,1e3],apCost:[3,4,5,7,9,12,15,19,23,28,33,39,45,52,59,67,76,86,97,109],location:"Lakeside, Tower of the Dead, Essence Rune Cube (DP)",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (Highest)",quantity:1},{level:3,name:"Force Core (Highest)",quantity:1},{level:4,name:"Force Core (Highest)",quantity:1},{level:5,name:"Force Core (Highest)",quantity:1},{level:6,name:"Force Core (Highest)",quantity:1},{level:7,name:"Force Core (Highest)",quantity:1},{level:8,name:"Force Core (Highest)",quantity:2},{level:9,name:"Force Core (Highest)",quantity:2},{level:10,name:"Force Core (Highest)",quantity:2},{level:11,name:"Force Core (Highest)",quantity:2},{level:12,name:"Force Core (Highest)",quantity:2},{level:13,name:"Force Core (Highest)",quantity:3},{level:14,name:"Force Core (Highest)",quantity:3},{level:15,name:"Force Core (Highest)",quantity:3},{level:16,name:"Force Core (Highest)",quantity:4},{level:17,name:"Force Core (Highest)",quantity:4},{level:18,name:"Force Core (Highest)",quantity:5},{level:19,name:"Force Core (Highest)",quantity:5},{level:20,name:"Force Core (Highest)",quantity:6}]},{id:"hp2",name:"HP II",tier:2,maxLevel:20,valuePerLevel:[100,200,300,400,500,600,700,800,900,1e3,1100,1200,1300,1400,1500,1600,1700,1800,1900,2e3],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"NPC Chloe's Request with material from Labyrinth",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:2},{level:3,name:"Divine Stone",quantity:2},{level:4,name:"Divine Stone",quantity:2},{level:5,name:"Divine Stone",quantity:2},{level:6,name:"Divine Stone",quantity:2},{level:7,name:"Divine Stone",quantity:2},{level:8,name:"Divine Stone",quantity:2},{level:9,name:"Divine Stone",quantity:2},{level:10,name:"Divine Stone",quantity:2},{level:11,name:"Divine Stone",quantity:4},{level:12,name:"Divine Stone",quantity:6},{level:13,name:"Divine Stone",quantity:8},{level:14,name:"Divine Stone",quantity:10},{level:15,name:"Divine Stone",quantity:14},{level:16,name:"Divine Stone",quantity:14},{level:17,name:"Divine Stone",quantity:14},{level:18,name:"Divine Stone",quantity:14},{level:19,name:"Divine Stone",quantity:14},{level:20,name:"Divine Stone",quantity:20}]},{id:"hp3",name:"HP III",tier:3,maxLevel:30,valuePerLevel:[10,27,44,61,78,95,112,129,146,165,182,199,216,233,250,267,284,301,318,335,352,369,386,403,420,437,454,471,488,500],apCost:[40,65,90,115,140,165,190,215,240,265,290,315,340,365,390,415,440,465,490,515,533,551,569,587,605,623,641,659,677,695],location:"Purifier in the Woods",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:2},{level:3,name:"Divine Stone",quantity:2},{level:4,name:"Divine Stone",quantity:2},{level:5,name:"Divine Stone",quantity:2},{level:6,name:"Divine Stone",quantity:4},{level:7,name:"Divine Stone",quantity:4},{level:8,name:"Divine Stone",quantity:4},{level:9,name:"Divine Stone",quantity:4},{level:10,name:"Divine Stone",quantity:4},{level:11,name:"Divine Stone",quantity:6},{level:12,name:"Divine Stone",quantity:6},{level:13,name:"Divine Stone",quantity:6},{level:14,name:"Divine Stone",quantity:6},{level:15,name:"Divine Stone",quantity:6},{level:16,name:"Divine Stone",quantity:8},{level:17,name:"Divine Stone",quantity:8},{level:18,name:"Divine Stone",quantity:8},{level:19,name:"Divine Stone",quantity:8},{level:20,name:"Divine Stone",quantity:8},{level:21,name:"Divine Stone",quantity:10},{level:22,name:"Divine Stone",quantity:10},{level:23,name:"Divine Stone",quantity:10},{level:24,name:"Divine Stone",quantity:10},{level:25,name:"Divine Stone",quantity:10},{level:26,name:"Divine Stone",quantity:12},{level:27,name:"Divine Stone",quantity:12},{level:28,name:"Divine Stone",quantity:12},{level:29,name:"Divine Stone",quantity:12},{level:30,name:"Divine Stone",quantity:12}]}]},defenseRate:{baseStatType:"defenseRate",iconId:"defenseRate",variants:[{id:"defenseRate",name:"Defense Rate I",tier:1,maxLevel:20,valuePerLevel:[15,30,45,60,75,90,105,120,135,150,165,180,195,210,225,240,255,270,285,300],apCost:[3,4,5,7,9,12,15,19,23,28,33,39,45,52,59,67,76,86,97,109],location:"Lakeside, Forbidden Island, Tower of the Dead B3F, Forbidden Island (Awakening), Essence Rune Cube (DP), Officer's Support Cube (WExp)",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core(Highest)",quantity:1},{level:3,name:"Force Core(Highest)",quantity:1},{level:4,name:"Force Core(Highest)",quantity:1},{level:5,name:"Force Core(Highest)",quantity:1},{level:6,name:"Force Core(Highest)",quantity:1},{level:7,name:"Force Core(Highest)",quantity:1},{level:8,name:"Force Core(Highest)",quantity:2},{level:9,name:"Force Core(Highest)",quantity:2},{level:10,name:"Force Core(Highest)",quantity:2},{level:11,name:"Force Core(Highest)",quantity:2},{level:12,name:"Force Core(Highest)",quantity:2},{level:13,name:"Force Core(Highest)",quantity:3},{level:14,name:"Force Core(Highest)",quantity:3},{level:15,name:"Force Core(Highest)",quantity:3},{level:16,name:"Force Core(Highest)",quantity:4},{level:17,name:"Force Core(Highest)",quantity:4},{level:18,name:"Force Core(Highest)",quantity:5},{level:19,name:"Force Core(Highest)",quantity:5},{level:20,name:"Force Core(Highest)",quantity:6}]},{id:"defenseRate2",name:"Defense Rate II",tier:2,maxLevel:20,valuePerLevel:[15,30,45,60,75,90,105,120,135,150,165,180,195,210,225,240,255,270,285,300],apCost:[3,4,5,7,9,12,15,19,23,28,33,39,45,52,59,67,76,86,97,109],location:"Exchange Shop at Secret Dealer Hiroalev",materials:[{level:1,name:null,quantity:0},{level:2,name:"Troglo's Golden Fruit",quantity:5},{level:3,name:"Troglo's Golden Fruit",quantity:10},{level:4,name:"Troglo's Golden Fruit",quantity:15},{level:5,name:"Troglo's Golden Fruit",quantity:20},{level:6,name:"Troglo's Golden Fruit",quantity:25},{level:7,name:"Troglo's Golden Fruit",quantity:30},{level:8,name:"Troglo's Golden Fruit",quantity:35},{level:9,name:"Troglo's Golden Fruit",quantity:40},{level:10,name:"Troglo's Golden Fruit",quantity:45},{level:11,name:"Troglo's Golden Fruit",quantity:50},{level:12,name:"Troglo's Golden Fruit",quantity:55},{level:13,name:"Troglo's Golden Fruit",quantity:60},{level:14,name:"Troglo's Golden Fruit",quantity:65},{level:15,name:"Troglo's Golden Fruit",quantity:70},{level:16,name:"Troglo's Golden Fruit",quantity:75},{level:17,name:"Troglo's Golden Fruit",quantity:80},{level:18,name:"Troglo's Golden Fruit",quantity:85},{level:19,name:"Troglo's Golden Fruit",quantity:90},{level:20,name:"Troglo's Golden Fruit",quantity:95}]}]},attackRate:{baseStatType:"attackRate",iconId:"attackRate",variants:[{id:"attackRate",name:"Attack Rate I",tier:1,maxLevel:20,valuePerLevel:[15,30,45,60,75,90,105,120,135,150,165,180,195,210,225,240,255,270,285,300],apCost:[3,4,5,7,9,12,15,19,23,28,33,39,45,52,59,67,76,86,97,109],location:"Lakeside, Forbidden Island (Awakening), Essence Rune Cube (DP), Officer's Support Cube (WExp)",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core(Highest)",quantity:1},{level:3,name:"Force Core(Highest)",quantity:1},{level:4,name:"Force Core(Highest)",quantity:1},{level:5,name:"Force Core(Highest)",quantity:1},{level:6,name:"Force Core(Highest)",quantity:1},{level:7,name:"Force Core(Highest)",quantity:1},{level:8,name:"Force Core(Highest)",quantity:2},{level:9,name:"Force Core(Highest)",quantity:2},{level:10,name:"Force Core(Highest)",quantity:2},{level:11,name:"Force Core(Highest)",quantity:2},{level:12,name:"Force Core(Highest)",quantity:2},{level:13,name:"Force Core(Highest)",quantity:3},{level:14,name:"Force Core(Highest)",quantity:3},{level:15,name:"Force Core(Highest)",quantity:3},{level:16,name:"Force Core(Highest)",quantity:4},{level:17,name:"Force Core(Highest)",quantity:4},{level:18,name:"Force Core(Highest)",quantity:5},{level:19,name:"Force Core(Highest)",quantity:5},{level:20,name:"Force Core(Highest)",quantity:6}]},{id:"attackRate2",name:"Attack Rate II",tier:2,maxLevel:20,valuePerLevel:[15,30,45,60,75,90,105,120,135,150,165,180,195,210,225,240,255,270,285,300],apCost:[3,4,5,7,9,12,15,19,23,28,33,39,45,52,59,67,76,86,97,109],location:"Exchange Shop at Secret Dealer Hiroalev",materials:[{level:1,name:null,quantity:0},{level:2,name:"Troglo's Golden Fruit",quantity:5},{level:3,name:"Troglo's Golden Fruit",quantity:10},{level:4,name:"Troglo's Golden Fruit",quantity:15},{level:5,name:"Troglo's Golden Fruit",quantity:20},{level:6,name:"Troglo's Golden Fruit",quantity:25},{level:7,name:"Troglo's Golden Fruit",quantity:30},{level:8,name:"Troglo's Golden Fruit",quantity:35},{level:9,name:"Troglo's Golden Fruit",quantity:40},{level:10,name:"Troglo's Golden Fruit",quantity:45},{level:11,name:"Troglo's Golden Fruit",quantity:50},{level:12,name:"Troglo's Golden Fruit",quantity:55},{level:13,name:"Troglo's Golden Fruit",quantity:60},{level:14,name:"Troglo's Golden Fruit",quantity:65},{level:15,name:"Troglo's Golden Fruit",quantity:70},{level:16,name:"Troglo's Golden Fruit",quantity:75},{level:17,name:"Troglo's Golden Fruit",quantity:80},{level:18,name:"Troglo's Golden Fruit",quantity:85},{level:19,name:"Troglo's Golden Fruit",quantity:90},{level:20,name:"Troglo's Golden Fruit",quantity:95}]}]},critDamage:{baseStatType:"critDamage",iconId:"critDamage",variants:[{id:"critDmg",name:"Critical DMG I",tier:1,maxLevel:10,valuePerLevel:[5,10,15,20,25,30,35,40,45,50],apCost:[5,14,27,45,67,94,125,163,209,264],location:"Pontus Ferrum, Porta Inferno, Tower of the Dead B4F, Forbidden Island (Awakening)",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (Highest)",quantity:1},{level:3,name:"Force Core (Highest)",quantity:2},{level:4,name:"Force Core (Highest)",quantity:4},{level:5,name:"Force Core (Highest)",quantity:5},{level:6,name:"Force Core (Highest)",quantity:6},{level:7,name:"Force Core (Highest)",quantity:7},{level:8,name:"Force Core (Highest)",quantity:8},{level:9,name:"Force Core (Highest)",quantity:9},{level:10,name:"Force Core (Highest)",quantity:10}]},{id:"critDmg2",name:"Critical DMG II",tier:2,maxLevel:9,valuePerLevel:[10,20,30,40,50,60,70,80,90],apCost:[450,475,500,525,550,575,600,625,650],location:"Secret Base SCA-76",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:5},{level:3,name:"Divine Stone",quantity:5},{level:4,name:"Divine Stone",quantity:5},{level:5,name:"Divine Stone",quantity:10},{level:6,name:"Divine Stone",quantity:10},{level:7,name:"Divine Stone",quantity:10},{level:8,name:"Divine Stone",quantity:15},{level:9,name:"Divine Stone",quantity:20}]}]},defense:{baseStatType:"defense",iconId:"defense",variants:[{id:"defense",name:"Defense I",tier:1,maxLevel:20,valuePerLevel:[5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90,95,100],apCost:[3,4,5,7,9,12,15,19,23,28,33,39,45,52,59,67,76,86,97,109],location:"Lakeside, Forbidden Island, Tower of the Dead B3F",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core(Highest)",quantity:1},{level:3,name:"Force Core(Highest)",quantity:1},{level:4,name:"Force Core(Highest)",quantity:1},{level:5,name:"Force Core(Highest)",quantity:1},{level:6,name:"Force Core(Highest)",quantity:1},{level:7,name:"Force Core(Highest)",quantity:1},{level:8,name:"Force Core(Highest)",quantity:2},{level:9,name:"Force Core(Highest)",quantity:2},{level:10,name:"Force Core(Highest)",quantity:2},{level:11,name:"Force Core(Highest)",quantity:2},{level:12,name:"Force Core(Highest)",quantity:2},{level:13,name:"Force Core(Highest)",quantity:3},{level:14,name:"Force Core(Highest)",quantity:3},{level:15,name:"Force Core(Highest)",quantity:3},{level:16,name:"Force Core(Highest)",quantity:4},{level:17,name:"Force Core(Highest)",quantity:4},{level:18,name:"Force Core(Highest)",quantity:5},{level:19,name:"Force Core(Highest)",quantity:5},{level:20,name:"Force Core(Highest)",quantity:6}]},{id:"defense2",name:"Defense II",tier:2,maxLevel:20,valuePerLevel:[3,6,9,12,15,18,21,24,27,30,33,36,39,42,45,48,51,54,57,60],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"Illussion Castle: Radiant Hall (Apocrypha)",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:1},{level:3,name:"Divine Stone",quantity:1},{level:4,name:"Divine Stone",quantity:1},{level:5,name:"Divine Stone",quantity:1},{level:6,name:"Divine Stone",quantity:1},{level:7,name:"Divine Stone",quantity:1},{level:8,name:"Divine Stone",quantity:1},{level:9,name:"Divine Stone",quantity:1},{level:10,name:"Divine Stone",quantity:1},{level:11,name:"Divine Stone",quantity:2},{level:12,name:"Divine Stone",quantity:3},{level:13,name:"Divine Stone",quantity:4},{level:14,name:"Divine Stone",quantity:5},{level:15,name:"Divine Stone",quantity:7},{level:16,name:"Divine Stone",quantity:7},{level:17,name:"Divine Stone",quantity:7},{level:18,name:"Divine Stone",quantity:7},{level:19,name:"Divine Stone",quantity:7},{level:20,name:"Divine Stone",quantity:10}]}]},exp:{baseStatType:"exp",iconId:"exp",variants:[{id:"exp",name:"EXP",tier:1,maxLevel:10,valuePerLevel:[1,2,3,4,5,6,7,8,9,10],apCost:[1,2,3,5,7,10,13,17,21,26],location:"Lakeside, Forbidden Island (Awakening), Essence Rune Cube (DP)",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (High)",quantity:1},{level:3,name:"Force Core (High)",quantity:1},{level:4,name:"Force Core (High)",quantity:1},{level:5,name:"Force Core (High)",quantity:1},{level:6,name:"Force Core (High)",quantity:2},{level:7,name:"Force Core (High)",quantity:2},{level:8,name:"Force Core (High)",quantity:2},{level:9,name:"Force Core (High)",quantity:2},{level:10,name:"Force Core (High)",quantity:3}]}]},skillExp:{baseStatType:"skillExp",iconId:"skillExp",variants:[{id:"skillExp",name:"Skill EXP",tier:1,maxLevel:10,valuePerLevel:[1,2,3,4,5,6,7,8,9,10],apCost:[1,2,3,5,7,10,13,17,21,26],location:"Phoenix Nest, Forgotten Temple B2F, Forbidden Island",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (High)",quantity:1},{level:3,name:"Force Core (High)",quantity:1},{level:4,name:"Force Core (High)",quantity:1},{level:5,name:"Force Core (High)",quantity:1},{level:6,name:"Force Core (High)",quantity:2},{level:7,name:"Force Core (High)",quantity:2},{level:8,name:"Force Core (High)",quantity:2},{level:9,name:"Force Core (High)",quantity:2},{level:10,name:"Force Core (High)",quantity:3}]}]},partyExp:{baseStatType:"partyExp",iconId:"partyExp",variants:[{id:"partyExp",name:"Party EXP",tier:1,maxLevel:10,valuePerLevel:[1,2,3,4,5,6,7,8,9,10],apCost:[1,2,3,5,7,10,13,17,21,26],location:"Tower of the Dead B2F, Forgotten Temple B2F, Forbidden Island",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (High)",quantity:1},{level:3,name:"Force Core (High)",quantity:1},{level:4,name:"Force Core (High)",quantity:1},{level:5,name:"Force Core (High)",quantity:1},{level:6,name:"Force Core (High)",quantity:2},{level:7,name:"Force Core (High)",quantity:2},{level:8,name:"Force Core (High)",quantity:2},{level:9,name:"Force Core (High)",quantity:2},{level:10,name:"Force Core (High)",quantity:3}]}]},petExp:{baseStatType:"petExp",iconId:"petExp",variants:[{id:"petExp",name:"Pet EXP",tier:1,maxLevel:10,valuePerLevel:[5,10,15,20,25,30,35,40,45,50],apCost:[1,2,3,5,7,10,13,17,21,26],location:"Volcanic Citadel, Forgotten Temple B2F, Forbidden Island",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (High)",quantity:1},{level:3,name:"Force Core (High)",quantity:1},{level:4,name:"Force Core (High)",quantity:1},{level:5,name:"Force Core (High)",quantity:1},{level:6,name:"Force Core (High)",quantity:2},{level:7,name:"Force Core (High)",quantity:2},{level:8,name:"Force Core (High)",quantity:2},{level:9,name:"Force Core (High)",quantity:2},{level:10,name:"Force Core (High)",quantity:3}]}]},alzDropAmount:{baseStatType:"alzDropAmount",iconId:"alzDropAmount",variants:[{id:"alzDropAmount",name:"Alz Drop Amount",tier:1,maxLevel:15,valuePerLevel:[2,4,7,9,12,14,17,19,22,24,27,29,32,34,37],apCost:[2,2,3,4,6,8,10,13,16,20,24,28,33,38,44],location:"Pontus Ferrum, Porta Inferno, Arcane Trace",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (High)",quantity:1},{level:3,name:"Force Core (High)",quantity:1},{level:4,name:"Force Core (High)",quantity:1},{level:5,name:"Force Core (High)",quantity:1},{level:6,name:"Force Core (High)",quantity:1},{level:7,name:"Force Core (High)",quantity:1},{level:8,name:"Force Core (High)",quantity:2},{level:9,name:"Force Core (High)",quantity:2},{level:10,name:"Force Core (High)",quantity:2},{level:11,name:"Force Core (High)",quantity:2},{level:12,name:"Force Core (High)",quantity:2},{level:13,name:"Force Core (High)",quantity:3},{level:14,name:"Force Core (High)",quantity:3},{level:15,name:"Force Core (High)",quantity:3}]}]},alzDropRate:{baseStatType:"alzDropRate",iconId:"alzDropRate",variants:[{id:"alzDropRate",name:"Alz Drop Rate",tier:1,maxLevel:15,valuePerLevel:[1,2,4,5,7,8,10,11,13,14,16,17,19,20,22],apCost:[2,2,3,4,6,8,10,13,16,20,24,28,33,38,44],location:"Unknown - Data missing from original runeProgression",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (High)",quantity:1},{level:3,name:"Force Core (High)",quantity:1},{level:4,name:"Force Core (High)",quantity:1},{level:5,name:"Force Core (High)",quantity:1},{level:6,name:"Force Core (High)",quantity:1},{level:7,name:"Force Core (High)",quantity:1},{level:8,name:"Force Core (High)",quantity:2},{level:9,name:"Force Core (High)",quantity:2},{level:10,name:"Force Core (High)",quantity:2},{level:11,name:"Force Core (High)",quantity:2},{level:12,name:"Force Core (High)",quantity:2},{level:13,name:"Force Core (High)",quantity:2},{level:14,name:"Force Core (High)",quantity:3},{level:15,name:"Force Core (High)",quantity:3}]}]},alzBombChance:{baseStatType:"alzBombChance",iconId:"alzBombChance",variants:[{id:"alzBombChance",name:"Alz Bomb Chance",tier:1,maxLevel:15,valuePerLevel:[1,2,4,5,7,8,10,11,13,15,16,17,19,20,22],apCost:[2,2,3,4,6,8,10,13,16,20,24,28,33,38,44],location:"Unknown - Data missing from original runeProgression",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (High)",quantity:1},{level:3,name:"Force Core (High)",quantity:1},{level:4,name:"Force Core (High)",quantity:1},{level:5,name:"Force Core (High)",quantity:1},{level:6,name:"Force Core (High)",quantity:1},{level:7,name:"Force Core (High)",quantity:1},{level:8,name:"Force Core (High)",quantity:2},{level:9,name:"Force Core (High)",quantity:2},{level:10,name:"Force Core (High)",quantity:2},{level:11,name:"Force Core (High)",quantity:2},{level:12,name:"Force Core (High)",quantity:2},{level:13,name:"Force Core (High)",quantity:2},{level:14,name:"Force Core (High)",quantity:3},{level:15,name:"Force Core (High)",quantity:3}]}]},resistDown:{baseStatType:"resistDown",iconId:"resistDown",variants:[{id:"resistDown",name:"Resist Down",tier:1,maxLevel:10,valuePerLevel:[1,2,3,4,5,6,7,8,9,10],apCost:[5,14,27,45,67,94,125,163,209,264],location:"Unknown - Data missing from original runeProgression",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (Highest)",quantity:1},{level:3,name:"Force Core (Highest)",quantity:2},{level:4,name:"Force Core (Highest)",quantity:4},{level:5,name:"Force Core (Highest)",quantity:5},{level:6,name:"Force Core (Highest)",quantity:6},{level:7,name:"Force Core (Highest)",quantity:7},{level:8,name:"Force Core (Highest)",quantity:8},{level:9,name:"Force Core (Highest)",quantity:9},{level:10,name:"Force Core (Highest)",quantity:10}]}]},resistKnockBack:{baseStatType:"resistKnockback",iconId:"resistKnockback",variants:[{id:"resistKnockBack",name:"Resist Knockback",tier:1,maxLevel:10,valuePerLevel:[1,2,3,4,5,6,7,8,9,10],apCost:[5,14,27,45,67,94,125,163,209,264],location:"Unknown - Data missing from original runeProgression",materials:[{level:1,name:null,quantity:0},{level:2,name:"Force Core (Highest)",quantity:1},{level:3,name:"Force Core (Highest)",quantity:2},{level:4,name:"Force Core (Highest)",quantity:4},{level:5,name:"Force Core (Highest)",quantity:5},{level:6,name:"Force Core (Highest)",quantity:6},{level:7,name:"Force Core (Highest)",quantity:7},{level:8,name:"Force Core (Highest)",quantity:8},{level:9,name:"Force Core (Highest)",quantity:9},{level:10,name:"Force Core (Highest)",quantity:10}]}]},ignorePenetration:{baseStatType:"ignorePenetration",iconId:"ignorePenetration",variants:[{id:"ignorePenetration2",name:"Ignore Penetration II",tier:2,maxLevel:10,valuePerLevel:[13,21,29,37,45,53,61,69,77,85],apCost:[450,475,500,525,550,575,600,625,650,675],location:"Celestia",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:5},{level:3,name:"Divine Stone",quantity:5},{level:4,name:"Divine Stone",quantity:5},{level:5,name:"Divine Stone",quantity:10},{level:6,name:"Divine Stone",quantity:10},{level:7,name:"Divine Stone",quantity:10},{level:8,name:"Divine Stone",quantity:15},{level:9,name:"Divine Stone",quantity:20},{level:10,name:"Divine Stone",quantity:20}]}]},penetration:{baseStatType:"penetration",iconId:"penetration",variants:[{id:"penetration2",name:"Penetration II",tier:2,maxLevel:9,valuePerLevel:[5,10,15,20,25,30,35,40,45],apCost:[450,475,500,525,550,575,600,625,650],location:"Secret Base SCA-76",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:5},{level:3,name:"Divine Stone",quantity:5},{level:4,name:"Divine Stone",quantity:5},{level:5,name:"Divine Stone",quantity:10},{level:6,name:"Divine Stone",quantity:10},{level:7,name:"Divine Stone",quantity:10},{level:8,name:"Divine Stone",quantity:15},{level:9,name:"Divine Stone",quantity:20}]}]},int:{baseStatType:"int",iconId:"int",variants:[{id:"int2",name:"INT II",tier:2,maxLevel:20,valuePerLevel:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"NPC Chloe's Request with material from Labyrinth",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:2},{level:3,name:"Divine Stone",quantity:2},{level:4,name:"Divine Stone",quantity:2},{level:5,name:"Divine Stone",quantity:2},{level:6,name:"Divine Stone",quantity:2},{level:7,name:"Divine Stone",quantity:2},{level:8,name:"Divine Stone",quantity:2},{level:9,name:"Divine Stone",quantity:2},{level:10,name:"Divine Stone",quantity:2},{level:11,name:"Divine Stone",quantity:4},{level:12,name:"Divine Stone",quantity:6},{level:13,name:"Divine Stone",quantity:8},{level:14,name:"Divine Stone",quantity:10},{level:15,name:"Divine Stone",quantity:14},{level:16,name:"Divine Stone",quantity:14},{level:17,name:"Divine Stone",quantity:14},{level:18,name:"Divine Stone",quantity:14},{level:19,name:"Divine Stone",quantity:14},{level:20,name:"Divine Stone",quantity:20}]},{id:"int",name:"INT I",tier:1,maxLevel:20,valuePerLevel:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"NPC Chloe's Request with material from Labyrinth",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:2},{level:3,name:"Divine Stone",quantity:2},{level:4,name:"Divine Stone",quantity:2},{level:5,name:"Divine Stone",quantity:2},{level:6,name:"Divine Stone",quantity:2},{level:7,name:"Divine Stone",quantity:2},{level:8,name:"Divine Stone",quantity:2},{level:9,name:"Divine Stone",quantity:2},{level:10,name:"Divine Stone",quantity:2},{level:11,name:"Divine Stone",quantity:4},{level:12,name:"Divine Stone",quantity:6},{level:13,name:"Divine Stone",quantity:8},{level:14,name:"Divine Stone",quantity:10},{level:15,name:"Divine Stone",quantity:14},{level:16,name:"Divine Stone",quantity:14},{level:17,name:"Divine Stone",quantity:14},{level:18,name:"Divine Stone",quantity:14},{level:19,name:"Divine Stone",quantity:14},{level:20,name:"Divine Stone",quantity:20}]}]},damageReduction:{baseStatType:"damageReduce",iconId:"damageReduce",variants:[{id:"dmgReduction2",name:"DMG Reduction II",tier:2,maxLevel:5,valuePerLevel:[12,24,36,48,60],apCost:[310,312,315,320,325],location:"Garden of Dust",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:5},{level:3,name:"Divine Stone",quantity:10},{level:4,name:"Divine Stone",quantity:15},{level:5,name:"Divine Stone",quantity:20}]},{id:"dmgReduction",name:"DMG Reduction I",tier:1,maxLevel:5,valuePerLevel:[12,24,36,48,60],apCost:[310,312,315,320,325],location:"Garden of Dust",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:5},{level:3,name:"Divine Stone",quantity:10},{level:4,name:"Divine Stone",quantity:15},{level:5,name:"Divine Stone",quantity:20}]}]},dex:{baseStatType:"dex",iconId:"dex",variants:[{id:"dex2",name:"DEX II",tier:2,maxLevel:20,valuePerLevel:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"NPC Chloe's Request with material from Labyrinth",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:2},{level:3,name:"Divine Stone",quantity:2},{level:4,name:"Divine Stone",quantity:2},{level:5,name:"Divine Stone",quantity:2},{level:6,name:"Divine Stone",quantity:2},{level:7,name:"Divine Stone",quantity:2},{level:8,name:"Divine Stone",quantity:2},{level:9,name:"Divine Stone",quantity:2},{level:10,name:"Divine Stone",quantity:2},{level:11,name:"Divine Stone",quantity:4},{level:12,name:"Divine Stone",quantity:6},{level:13,name:"Divine Stone",quantity:8},{level:14,name:"Divine Stone",quantity:10},{level:15,name:"Divine Stone",quantity:14},{level:16,name:"Divine Stone",quantity:14},{level:17,name:"Divine Stone",quantity:14},{level:18,name:"Divine Stone",quantity:14},{level:19,name:"Divine Stone",quantity:14},{level:20,name:"Divine Stone",quantity:20}]},{id:"dex",name:"DEX I",tier:1,maxLevel:20,valuePerLevel:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"NPC Chloe's Request with material from Labyrinth",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:2},{level:3,name:"Divine Stone",quantity:2},{level:4,name:"Divine Stone",quantity:2},{level:5,name:"Divine Stone",quantity:2},{level:6,name:"Divine Stone",quantity:2},{level:7,name:"Divine Stone",quantity:2},{level:8,name:"Divine Stone",quantity:2},{level:9,name:"Divine Stone",quantity:2},{level:10,name:"Divine Stone",quantity:2},{level:11,name:"Divine Stone",quantity:4},{level:12,name:"Divine Stone",quantity:6},{level:13,name:"Divine Stone",quantity:8},{level:14,name:"Divine Stone",quantity:10},{level:15,name:"Divine Stone",quantity:14},{level:16,name:"Divine Stone",quantity:14},{level:17,name:"Divine Stone",quantity:14},{level:18,name:"Divine Stone",quantity:14},{level:19,name:"Divine Stone",quantity:14},{level:20,name:"Divine Stone",quantity:20}]}]},str:{baseStatType:"str",iconId:"str",variants:[{id:"str2",name:"STR II",tier:2,maxLevel:20,valuePerLevel:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"NPC Chloe's Request with material from Labyrinth",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:2},{level:3,name:"Divine Stone",quantity:2},{level:4,name:"Divine Stone",quantity:2},{level:5,name:"Divine Stone",quantity:2},{level:6,name:"Divine Stone",quantity:2},{level:7,name:"Divine Stone",quantity:2},{level:8,name:"Divine Stone",quantity:2},{level:9,name:"Divine Stone",quantity:2},{level:10,name:"Divine Stone",quantity:2},{level:11,name:"Divine Stone",quantity:4},{level:12,name:"Divine Stone",quantity:6},{level:13,name:"Divine Stone",quantity:8},{level:14,name:"Divine Stone",quantity:10},{level:15,name:"Divine Stone",quantity:14},{level:16,name:"Divine Stone",quantity:14},{level:17,name:"Divine Stone",quantity:14},{level:18,name:"Divine Stone",quantity:14},{level:19,name:"Divine Stone",quantity:14},{level:20,name:"Divine Stone",quantity:20}]},{id:"str",name:"STR I",tier:1,maxLevel:20,valuePerLevel:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"NPC Chloe's Request with material from Labyrinth",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:2},{level:3,name:"Divine Stone",quantity:2},{level:4,name:"Divine Stone",quantity:2},{level:5,name:"Divine Stone",quantity:2},{level:6,name:"Divine Stone",quantity:2},{level:7,name:"Divine Stone",quantity:2},{level:8,name:"Divine Stone",quantity:2},{level:9,name:"Divine Stone",quantity:2},{level:10,name:"Divine Stone",quantity:2},{level:11,name:"Divine Stone",quantity:4},{level:12,name:"Divine Stone",quantity:6},{level:13,name:"Divine Stone",quantity:8},{level:14,name:"Divine Stone",quantity:10},{level:15,name:"Divine Stone",quantity:14},{level:16,name:"Divine Stone",quantity:14},{level:17,name:"Divine Stone",quantity:14},{level:18,name:"Divine Stone",quantity:14},{level:19,name:"Divine Stone",quantity:14},{level:20,name:"Divine Stone",quantity:20}]}]},magicSkillAmp:{baseStatType:"magicSkillAmp",iconId:"magicSkillAmp",variants:[{id:"magicSkillAmp2",name:"Magic Skill Amp II",tier:2,maxLevel:5,valuePerLevel:[1,2,3,4,5],apCost:[300,350,400,450,500],location:"Garden of Dust (Secret Chest)",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:5},{level:3,name:"Divine Stone",quantity:10},{level:4,name:"Divine Stone",quantity:15},{level:5,name:"Divine Stone",quantity:20}]}]},swordSkillAmp:{baseStatType:"swordSkillAmp",iconId:"swordSkillAmp",variants:[{id:"swordSkillAmp2",name:"Sword Skill Amp II",tier:2,maxLevel:5,valuePerLevel:[1,2,3,4,5],apCost:[300,350,400,450,500],location:"Garden of Dust (Secret Chest)",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:5},{level:3,name:"Divine Stone",quantity:10},{level:4,name:"Divine Stone",quantity:15},{level:5,name:"Divine Stone",quantity:20}]}]},magicAttack:{baseStatType:"magicAttack",iconId:"magicAttack",variants:[{id:"magicAttack2",name:"Magic Attack II",tier:2,maxLevel:20,valuePerLevel:[2,4,6,8,10,12,14,16,18,20,22,24,26,28,30,32,34,36,38,40],apCost:[20,50,80,110,140,170,200,230,260,290,320,350,380,410,440,470,500,530,560,590],location:"Mirage Island",materials:[{level:1,name:null,quantity:0},{level:2,name:"Divine Stone",quantity:1},{level:3,name:"Divine Stone",quantity:1},{level:4,name:"Divine Stone",quantity:1},{level:5,name:"Divine Stone",quantity:1},{level:6,name:"Divine Stone",quantity:1},{level:7,name:"Divine Stone",quantity:1},{level:8,name:"Divine Stone",quantity:1},{level:9,name:"Divine Stone",quantity:1},{level:10,name:"Divine Stone",quantity:1},{level:11,name:"Divine Stone",quantity:2},{level:12,name:"Divine Stone",quantity:3},{level:13,name:"Divine Stone",quantity:4},{level:14,name:"Divine Stone",quantity:5},{level:15,name:"Divine Stone",quantity:7},{level:16,name:"Divine Stone",quantity:7},{level:17,name:"Divine Stone",quantity:7},{level:18,name:"Divine Stone",quantity:7},{level:19,name:"Divine Stone",quantity:7},{level:20,name:"Divine Stone",quantity:10}]}]}}},window.KarmaRuneData={runeDefinitions:{hp:{baseStatType:"hp",iconId:"hp",variants:[{id:"karmaHp",name:"HP Karma Rune",tier:1,maxLevel:100,valuePerLevel:Array.from({length:100},((e,a)=>10*(a+1))),apCost:Array.from({length:100},((e,a)=>Math.floor(3+1.5*a))),location:"Karma Rune Dungeon, Karma Shop",materials:[]}]},pveAllAttackUp:{baseStatType:"allAttackUp",iconId:"allAttack",variants:[{id:"pveAllAttackUp",name:"PvE All Attack Up",tier:1,maxLevel:15,valuePerLevel:[3,6,9,12,15,18,21,24,27,30,33,36,39,42,45],apCost:[500,500,500,500,500,600,600,600,600,600,700,700,700,700,700],runeCount:[1,2,3,4,5,9,10,11,12,13,14,15,16,17,18],location:"Karma Rune Dungeon, Karma Shop",materials:[]}]},pvpAllAttackUp:{baseStatType:"allAttackUp",iconId:"allAttack",variants:[{id:"pvpAllAttackUp",name:"PvP All Attack Up",tier:1,maxLevel:15,valuePerLevel:[3,6,9,12,15,18,21,24,27,30,33,36,39,42,45],apCost:[500,500,500,500,500,600,600,600,600,600,700,700,700,700,700],runeCount:[1,2,3,4,5,9,10,11,12,13,14,15,16,17,18],location:"Karma Rune Dungeon, Karma Shop",materials:[]}]},pvpResistSkillAmp:{baseStatType:"resistSkillAmp",iconId:"resistSkillAmp",variants:[{id:"pvpResistSkillAmp",name:"PvP Resist Skill Amp.",tier:1,maxLevel:5,valuePerLevel:[1,2,3,4,5],apCost:[700,700,700,1200,2200],runeCount:[0,5,7,9,11],location:"Karma Rune Dungeon, Karma Shop",materials:[]}]},pveAttackRate:{baseStatType:"attackRate",iconId:"attackRate",variants:[{id:"pveAttackRate",name:"PvE Attack Rate",tier:1,maxLevel:20,valuePerLevel:[20,40,60,80,100,120,140,160,180,200,220,240,260,280,300,320,340,360,380,400],apCost:[400,400,500,500,500,500,500,500,500,500,600,600,600,600,600,700,700,700,700,700],runeCount:[0,0,5,5,5,10,10,10,10,10,15,15,15,15,15,20,20,20,20,20],location:"Karma Rune Dungeon, Karma Shop",materials:[]}]},pveDefenseRate:{baseStatType:"defenseRate",iconId:"defenseRate",variants:[{id:"pveDefenseRate",name:"PvE Defense Rate",tier:1,maxLevel:20,valuePerLevel:[20,40,60,80,100,120,140,160,180,200,220,240,260,280,300,320,340,360,380,400],apCost:[400,400,500,500,500,500,500,500,500,500,600,600,600,600,600,700,700,700,700,700],runeCount:[0,0,5,5,5,10,10,10,10,10,15,15,15,15,15,20,20,20,20,20],location:"Karma Rune Dungeon, Karma Shop",materials:[]}]},pvpAttackRate:{baseStatType:"attackRate",iconId:"attackRate",variants:[{id:"pvpAttackRate",name:"PvP Attack Rate",tier:1,maxLevel:20,valuePerLevel:[20,40,60,80,100,120,140,160,180,200,220,240,260,280,300,320,340,360,380,400],apCost:[400,400,500,500,500,500,500,500,500,500,600,600,600,600,600,700,700,700,700,700],runeCount:[0,0,5,5,5,10,10,10,10,10,15,15,15,15,15,20,20,20,20,20],location:"Karma Rune Dungeon, Karma Shop",materials:[]}]},pvpDefenseRate:{baseStatType:"defenseRate",iconId:"defenseRate",variants:[{id:"pvpDefenseRate",name:"PvP Defense Rate",tier:1,maxLevel:20,valuePerLevel:[20,40,60,80,100,120,140,160,180,200,220,240,260,280,300,320,340,360,380,400],apCost:[400,400,500,500,500,500,500,500,500,500,600,600,600,600,600,700,700,700,700,700],runeCount:[0,0,5,5,5,10,10,10,10,10,15,15,15,15,15,20,20,20,20,20],location:"Karma Rune Dungeon, Karma Shop",materials:[]}]}}},window.EquipmentData={itemTypes:{weapon:{upgradePaths:["base","extreme","divine","slots","epic"],maxBaseLevel:20,canHaveEpicOption:!0,hasSlots:!0},armor:{upgradePaths:["base","extreme","divine","slots","epic"],maxBaseLevel:20,canHaveEpicOption:!0,hasSlots:!0},helmet:{upgradePaths:["base","extreme","divine","slots","epic"],maxBaseLevel:20,canHaveEpicOption:!0,hasSlots:!0},gloves:{upgradePaths:["base","extreme","divine","slots","epic"],maxBaseLevel:20,canHaveEpicOption:!0,hasSlots:!0},boots:{upgradePaths:["base","extreme","divine","slots","epic"],maxBaseLevel:20,canHaveEpicOption:!0,hasSlots:!0},ring:{upgradePaths:[],canHaveEpicOption:!1,hasSlots:!1},earring:{upgradePaths:["chaos"],maxChaosLevel:15,canHaveEpicOption:!1,hasSlots:!1},bracelet:{upgradePaths:["chaos"],maxChaosLevel:15,canHaveEpicOption:!1,hasSlots:!1},amulet:{upgradePaths:["chaos"],maxChaosLevel:15,canHaveEpicOption:!1,hasSlots:!1},arcana:{upgradePaths:["base"],maxBaseLevel:20,canHaveEpicOption:!1,hasSlots:!1},belt:{upgradePaths:["base"],maxBaseLevel:20,canHaveEpicOption:!1,hasSlots:!1},carnelian:{upgradePaths:["base"],maxBaseLevel:20,canHaveEpicOption:!1,hasSlots:!1},talisman:{upgradePaths:["base"],maxBaseLevel:20,canHaveEpicOption:!1,hasSlots:!1},epaulet:{upgradePaths:[],canHaveEpicOption:!1,hasSlots:!1},bike:{upgradePaths:["base","extreme","divine","slots","epic"],maxBaseLevel:20,canHaveEpicOption:!0,hasSlots:!0},effector:{upgradePaths:[],canHaveEpicOption:!1,hasSlots:!1}},items:{weapons:[],armor:[],helmets:[],gloves:[],boots:[],rings:[],earrings:[],bracelets:[],amulets:[],arcanas:[],belts:[],carnelians:[],talismans:[],epaulets:[],bikes:[],effectors:[]},init:function(){window.WeaponsData&&(this.items.weapons=WeaponsData.weapons,this.gradeUpgrades=WeaponsData.gradeUpgrades,this.extremeUpgrades=WeaponsData.extremeUpgrades,this.divineUpgrades=WeaponsData.divineUpgrades,this.epicOptions=WeaponsData.epicOptions,this.slotOptions=WeaponsData.slotOptions),window.BeltsData&&(this.items.belts=BeltsData.belts)},baseUpgrades:{plate:[{level:0,defenseMod:1,magicDefenseMod:1,hpMod:1},{level:1,defenseMod:1.03,magicDefenseMod:1.02,hpMod:1.01},{level:20,defenseMod:1.6,magicDefenseMod:1.4,hpMod:1.3}],generic:[{level:0,statMod:1},{level:1,statMod:1.02},{level:2,statMod:1.04},{level:20,statMod:1.4}]},chaosUpgrades:{gold:[[],[{stat:"allAttackUp",value:10}],[{stat:"allAttackUp",value:150},{stat:"critRate",value:10},{stat:"critDamage",value:20}]],platinum:[[],[{stat:"allAttackUp",value:15}],[{stat:"allAttackUp",value:200},{stat:"critRate",value:15},{stat:"critDamage",value:25}]]}},window.WeaponsData={weapons:[],materialGrades:{mithril:"high",archridium:"highest",palladium:"ultimate",demonite:"ultimate",dragonium:"ultimate"},weaponTemplates:{orb:{type:"weapon",subtype:"orb",material:"orb",class:"mage",maxSlots:3,grades:{dragonium:{baseStats:{attack:370,magicAttack:240,attackRate:105},maxExtremeLevel:7,imagePath:"orb dragonium final.png",description:"An orb crafted from the legendary dragonium crystal. Immense magical power flows through it, enhancing spell potency."},demonite:{baseStats:{attack:360,magicAttack:230,attackRate:110},maxExtremeLevel:7,imagePath:"orb demonite final.png",description:"A sinister orb made of rare demonite. Channels dark energies for devastating magical attacks."},palladium:{baseStats:{attack:350,magicAttack:220,attackRate:100},maxExtremeLevel:6,imagePath:"orb palladium final.png",description:"A powerful orb made of rare palladium crystal. Enhances magical abilities and critical strikes."},archridium:{baseStats:{attack:320,magicAttack:200,attackRate:95},maxExtremeLevel:6,imagePath:"orb archridium final.png",description:"An orb crafted from the mystic metal archridium. Provides excellent magical power."},mithril:{baseStats:{attack:290,magicAttack:180,attackRate:90},maxExtremeLevel:5,imagePath:"orb mithril final.png",description:"A finely crafted orb of mithril. Enhances magical abilities."}}},crystal:{type:"weapon",subtype:"crystal",material:"orb",class:"mage",maxSlots:3,grades:{dragonium:{baseStats:{attack:340,magicAttack:260,attackRate:100},maxExtremeLevel:7,imagePath:"crystal dragonium final.png",description:"A crystal forged from the core of dragon remains. Perfectly channels magical energies for devastating spells."},demonite:{baseStats:{attack:330,magicAttack:250,attackRate:105},maxExtremeLevel:7,imagePath:"crystal demonite final.png",description:"A dark crystal formed from demonic essence. Amplifies magical power with chaotic energies."},palladium:{baseStats:{attack:320,magicAttack:240,attackRate:95},maxExtremeLevel:7,imagePath:"crystal palladium final.png",description:"A powerful crystal made of rare palladium. Maximizes magical potential and enhances casting speed."},archridium:{baseStats:{attack:290,magicAttack:220,attackRate:90},maxExtremeLevel:6,imagePath:"crystal archridium final.png",description:"A crystal crafted from the mystic metal archridium. Provides excellent magical power and cast rates."},mithril:{baseStats:{attack:260,magicAttack:200,attackRate:85},maxExtremeLevel:5,imagePath:"crystal mithril final.png",description:"A finely crafted crystal of mithril. Enhances magical abilities."}}},blade:{type:"weapon",subtype:"blade",material:"sword",class:"warrior",maxSlots:3,grades:{dragonium:{baseStats:{attack:400,magicAttack:170,attackRate:115},maxExtremeLevel:7,imagePath:"blade dragonium final.png",description:"A blade forged from dragonium scales. Cuts through enemies with incredible sharpness and precision."},demonite:{baseStats:{attack:390,magicAttack:160,attackRate:120},maxExtremeLevel:7,imagePath:"blade demonite final.png",description:"A blade infused with demonite. Strikes with supernatural speed and leaves wounds that burn like hellfire."},palladium:{baseStats:{attack:380,magicAttack:150,attackRate:110},maxExtremeLevel:7,imagePath:"blade palladium final.png",description:"A powerful blade forged from rare palladium. Delivers precise strikes at lightning speed."},archridium:{baseStats:{attack:350,magicAttack:130,attackRate:105},maxExtremeLevel:6,imagePath:"blade archridium final.png",description:"A blade crafted from mystic archridium metal. Provides excellent cutting power."},mithril:{baseStats:{attack:320,magicAttack:110,attackRate:100},maxExtremeLevel:5,imagePath:"blade mithril final.png",description:"A finely crafted blade of mithril. Perfect balance for swift attacks."}}},katana:{type:"weapon",subtype:"katana",material:"sword",class:"warrior",maxSlots:3,grades:{dragonium:{baseStats:{attack:420,magicAttack:150,attackRate:105},maxExtremeLevel:7,imagePath:"katana dragonium final.png",description:"A legendary katana forged with dragonium. Each swing creates a wind that can slice through armor like paper."},demonite:{baseStats:{attack:410,magicAttack:140,attackRate:110},maxExtremeLevel:7,imagePath:"katana demonite final.png",description:"A sinister katana made from demonite. Feeds on the lifeforce of enemies it cuts, strengthening the wielder."},palladium:{baseStats:{attack:400,magicAttack:130,attackRate:100},maxExtremeLevel:7,imagePath:"katana palladium final.png",description:"A masterfully crafted katana of rare palladium. Delivers devastating damage with precision."},archridium:{baseStats:{attack:370,magicAttack:110,attackRate:95},maxExtremeLevel:6,imagePath:"katana archridium final.png",description:"A katana forged from mystic archridium. Provides excellent cutting power."},mithril:{baseStats:{attack:340,magicAttack:90,attackRate:90},maxExtremeLevel:5,imagePath:"katana mithril final.png",description:"A finely crafted katana of mithril. Delivers powerful strikes."}}},greatsword:{type:"weapon",subtype:"greatsword",material:"sword",class:"warrior",maxSlots:3,grades:{dragonium:{baseStats:{attack:470,magicAttack:130,attackRate:85},maxExtremeLevel:7,imagePath:"greatsword dragonium final.png",description:"A colossal greatsword of dragonium, said to be forged from a dragon's spine. One swing can cleave mountains."},demonite:{baseStats:{attack:460,magicAttack:120,attackRate:90},maxExtremeLevel:7,imagePath:"greatsword demonite final.png",description:"A demonic greatsword that pulses with dark energy. Strikes fear into enemies before crushing them."},palladium:{baseStats:{attack:450,magicAttack:110,attackRate:80},maxExtremeLevel:7,imagePath:"greatsword palladium final.png",description:"A massive greatsword forged from rare palladium. Devastating power."},archridium:{baseStats:{attack:420,magicAttack:90,attackRate:75},maxExtremeLevel:6,imagePath:"greatsword archridium final.png",description:"A greatsword crafted from mystic archridium. Provides overwhelming strength."},mithril:{baseStats:{attack:390,magicAttack:70,attackRate:70},maxExtremeLevel:5,imagePath:"greatsword mithril final.png",description:"A finely crafted greatsword of mithril. Brings great power to battle."}}},daikatana:{type:"weapon",subtype:"daikatana",material:"sword",class:"warrior",maxSlots:3,grades:{dragonium:{baseStats:{attack:450,magicAttack:150,attackRate:90},maxExtremeLevel:7,imagePath:"daikatana dragonium final.png",description:"A legendary daikatana of dragonium. Its blade gleams with the colors of dragon scales and cuts with unrivaled precision."},demonite:{baseStats:{attack:440,magicAttack:140,attackRate:95},maxExtremeLevel:7,imagePath:"daikatana demonite final.png",description:"A terrifying daikatana of demonite. Each swing leaves trails of unholy energy that harm lingering foes."},palladium:{baseStats:{attack:430,magicAttack:130,attackRate:85},maxExtremeLevel:7,imagePath:"daikatana palladium final.png",description:"A massive daikatana forged from rare palladium. Combines power with precision."},archridium:{baseStats:{attack:400,magicAttack:110,attackRate:80},maxExtremeLevel:6,imagePath:"daikatana archridium final.png",description:"A daikatana crafted from mystic archridium. An imposing weapon of great reach."},mithril:{baseStats:{attack:370,magicAttack:90,attackRate:75},maxExtremeLevel:5,imagePath:"daikatana mithril final.png",description:"A finely crafted daikatana of mithril. Perfect for devastating sweeping attacks."}}},chakram:{type:"weapon",subtype:"chakram",material:"orb",class:"ranger",maxSlots:3,grades:{dragonium:{baseStats:{attack:380,magicAttack:200,attackRate:125},maxExtremeLevel:7,imagePath:"chakram dragonium final.png",description:"A chakram formed from dragonium scales. Returns to the wielder with supernatural accuracy and cuts through any defense."},demonite:{baseStats:{attack:370,magicAttack:190,attackRate:130},maxExtremeLevel:7,imagePath:"chakram demonite final.png",description:"A demonite chakram that leaves trails of dark energy. Can strike multiple foes in a single throw."},palladium:{baseStats:{attack:360,magicAttack:180,attackRate:120},maxExtremeLevel:7,imagePath:"chakram palladium final.png",description:"A perfectly balanced chakram made of rare palladium. Enables rapid attacks at range."},archridium:{baseStats:{attack:330,magicAttack:160,attackRate:115},maxExtremeLevel:6,imagePath:"chakram archridium final.png",description:"A chakram crafted from mystic archridium. Provides excellent range and speed."},mithril:{baseStats:{attack:300,magicAttack:140,attackRate:110},maxExtremeLevel:5,imagePath:"chakram mithril final.png",description:"A finely crafted chakram of mithril. Perfect for rapid ranged attacks."}}}},upgradeStats:{twoHanded:{attack:[0,12,24,36,54,72,90,114,138,162,192,222,252,288,324,360,407,445,484,524,565],attackRate:[0,40,80,120,172,224,276,340,404,468,544,620,696,784,872,960,1050,1142,1236,1332,1430],magicAttack:[0,10,20,30,46,62,78,100,122,144,172,200,228,262,296,330,375,411,448,486,525]},oneHanded:{attack:[0,6,12,18,27,36,45,57,69,81,96,111,126,144,162,180,209,229,250,272,295],attackRate:[0,20,40,60,86,112,138,170,202,234,272,310,348,392,436,480,526,574,624,676,730],magicAttack:[0,5,10,15,23,31,39,50,61,72,86,100,114,131,148,165,193,212,232,253,275]},magic:{attack:[0,5,10,15,23,31,39,50,61,72,86,100,114,131,148,165,193,212,232,253,275],attackRate:[0,20,40,60,86,112,138,170,202,234,272,310,348,392,436,480,526,574,624,676,730],magicAttack:[0,6,12,18,27,36,45,57,69,81,96,111,126,144,162,180,209,229,250,272,295]}},getUpgradeStatType:function(e){return"greatsword"===e||"daikatana"===e?"twoHanded":"blade"===e||"katana"===e||"chakram"===e?"oneHanded":"magic"},getUpgradeStat:function(e,a,t){const i=this.getUpgradeStatType(e),n=Math.min(t,this.upgradeStats[i][a].length-1);return this.upgradeStats[i][a][n]},extremeUpgrades:{level7:[[],[{stat:"allAttackUp",value:20},{stat:"attackRate",value:50},{stat:"accuracy",value:80}],[{stat:"allAttackUp",value:60},{stat:"attackRate",value:120},{stat:"accuracy",value:140}],[{stat:"allAttackUp",value:100},{stat:"attackRate",value:190},{stat:"accuracy",value:200},{stat:"critDamage",value:7}],[{stat:"allAttackUp",value:130},{stat:"attackRate",value:270},{stat:"accuracy",value:260},{stat:"critDamage",value:15},{stat:"penetration",value:30}],[{stat:"allAttackUp",value:160},{stat:"attackRate",value:360},{stat:"accuracy",value:330},{stat:"critDamage",value:23},{stat:"penetration",value:55}],[{stat:"allAttackUp",value:200},{stat:"attackRate",value:460},{stat:"accuracy",value:420},{stat:"critDamage",value:31},{stat:"penetration",value:80}],[{stat:"allAttackUp",value:250},{stat:"attackRate",value:600},{stat:"accuracy",value:420},{stat:"critDamage",value:40},{stat:"penetration",value:110}]]},divineUpgrades:{high:{allAttackUp:[0,18,22,26,30,34,38,42,46,50,54,58,62,66,70,74],attackRate:[0,5,10,15,20,30,40,50,60,70,90,110,130,150,180,210],critDamage:[0,0,0,0,0,0,0,1,2,3,4,5,6,7,9,11],accuracy:[0,0,0,0,0,0,0,0,0,60,75,90,105,120,135,150],penetration:[0,0,0,0,0,0,0,0,0,0,0,0,0,5,25,45]},highest:{allAttackUp:[0,27,31,35,39,43,47,51,55,59,63,67,71,75,79,83],attackRate:[0,10,20,30,40,60,80,100,120,140,160,180,200,220,240,270],critDamage:[0,0,0,0,0,0,0,1,2,3,4,5,6,8,10,13],accuracy:[0,0,0,0,0,0,0,0,0,80,95,120,135,150,165,180],penetration:[0,0,0,0,0,0,0,0,0,0,0,0,0,10,30,50]},ultimate:{allAttackUp:[0,36,40,44,48,52,56,60,64,68,72,76,80,84,88,92],attackRate:[0,15,30,45,60,90,120,150,180,210,230,250,270,290,300,330],critDamage:[0,0,0,0,0,0,0,1,2,3,4,5,6,9,11,15],accuracy:[0,0,0,0,0,0,0,0,0,100,115,150,165,180,195,210],penetration:[0,0,0,0,0,0,0,0,0,0,0,0,0,15,35,55]}},epicOptions:[{id:"critDamage",name:"Critical DMG.",levels:[{level:0,value:20},{level:1,value:22},{level:2,value:25}]},{id:"critRate",name:"Critical Rate",levels:[{level:0,value:15},{level:1,value:17},{level:2,value:20}]},{id:"allSkillAmp",name:"Skill Amp.",levels:[{level:0,value:10},{level:1,value:12},{level:2,value:15}]}],slotOptions:[{id:"critDamage",name:"Critical DMG.",value:20},{id:"critRate",name:"Critical Rate",value:15},{id:"swordSkillAmp",name:"Sword Amp.",value:10},{id:"magicSkillAmp",name:"Magic Amp.",value:10},{id:"accuracy",name:"Accuracy",value:100}],getDivineUpgradeStats:function(e,a){const t=this.materialGrades[e]||"high",i={},n=this.divineUpgrades[t];return n.allAttackUp[a]>0&&(i.allAttackUp=n.allAttackUp[a]),n.attackRate[a]>0&&(i.attackRate=n.attackRate[a]),n.critDamage[a]>0&&(i.critDamage=n.critDamage[a]),n.accuracy[a]>0&&(i.accuracy=n.accuracy[a]),n.penetration[a]>0&&(i.penetration=n.penetration[a]),i},initialize:function(){const e=forceguidesPlannerData.pluginUrl+"assets/images/equipment/";Object.keys(this.weaponTemplates).forEach((a=>{const t=this.weaponTemplates[a];Object.keys(t.grades).forEach((i=>{const n=t.grades[i],l={id:i+"_"+a,name:i.charAt(0).toUpperCase()+i.slice(1)+" "+a.charAt(0).toUpperCase()+a.slice(1),type:t.type,subtype:t.subtype,material:t.material,class:t.class,grade:this.materialGrades[i],imagePath:e+n.imagePath,description:n.description,baseStats:n.baseStats,maxSlots:t.maxSlots,maxExtremeLevel:n.maxExtremeLevel,getUpgradedAttack:function(e){return WeaponsData.getUpgradeStat(a,"attack",e)},getUpgradedAttackRate:function(e){return WeaponsData.getUpgradeStat(a,"attackRate",e)},getUpgradedMagicAttack:function(e){return WeaponsData.getUpgradeStat(a,"magicAttack",e)}};this.weapons.push(l)}))}))}},document.addEventListener("DOMContentLoaded",(function(){window.WeaponsData&&window.WeaponsData.initialize()})),window.BeltsData={belts:[{id:"chaos_fighter_belt",name:"Minesta's Chaos Fighter Belt",type:"belt",imagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/fighter-belt.png",description:"A belt imbued with chaos energy, optimized for physical fighters.",baseStats:{hp:10},maxBaseLevel:20,upgradeData:"fighterBelt"},{id:"chaos_sage_belt",name:"Minesta's Chaos Sage Belt",type:"belt",imagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/sage belt.png",description:"A belt imbued with chaos energy, optimized for magic users.",baseStats:{hp:10},maxBaseLevel:20,upgradeData:"sageBelt"},{id:"chaos_guardian_belt",name:"Minesta's Chaos Guardian Belt",type:"belt",imagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/belt_guardian.png",description:"A belt imbued with chaos energy, optimized for defensive fighters.",baseStats:{hp:10},maxBaseLevel:20,upgradeData:"guardianBelt"}],fighterBelt:{upgrades:[{hp:0,attack:0,critDamage:0,swordSkillAmp:0,accuracy:0,penetration:0},{hp:20,attack:5,critDamage:0,swordSkillAmp:0,accuracy:0,penetration:0},{hp:30,attack:10,critDamage:0,swordSkillAmp:0,accuracy:0,penetration:0},{hp:40,attack:15,critDamage:0,swordSkillAmp:0,accuracy:0,penetration:0},{hp:50,attack:20,critDamage:1,swordSkillAmp:0,accuracy:0,penetration:0},{hp:60,attack:25,critDamage:2,swordSkillAmp:0,accuracy:0,penetration:0},{hp:70,attack:30,critDamage:3,swordSkillAmp:1,accuracy:0,penetration:0},{hp:80,attack:30,critDamage:4,swordSkillAmp:2,accuracy:0,penetration:0},{hp:90,attack:40,critDamage:5,swordSkillAmp:3,accuracy:0,penetration:0},{hp:100,attack:45,critDamage:6,swordSkillAmp:4,accuracy:10,penetration:0},{hp:110,attack:50,critDamage:7,swordSkillAmp:5,accuracy:20,penetration:0},{hp:120,attack:55,critDamage:8,swordSkillAmp:6,accuracy:30,penetration:0},{hp:130,attack:60,critDamage:9,swordSkillAmp:7,accuracy:40,penetration:10},{hp:140,attack:65,critDamage:10,swordSkillAmp:8,accuracy:50,penetration:20},{hp:150,attack:70,critDamage:11,swordSkillAmp:9,accuracy:60,penetration:30},{hp:160,attack:75,critDamage:12,swordSkillAmp:10,accuracy:70,penetration:40},{hp:170,attack:85,critDamage:14,swordSkillAmp:11,accuracy:80,penetration:50},{hp:180,attack:95,critDamage:16,swordSkillAmp:12,accuracy:90,penetration:60},{hp:190,attack:105,critDamage:18,swordSkillAmp:13,accuracy:100,penetration:70},{hp:200,attack:115,critDamage:20,swordSkillAmp:14,accuracy:110,penetration:80},{hp:210,attack:125,critDamage:22,swordSkillAmp:14,accuracy:110,penetration:80}]},sageBelt:{upgrades:[{hp:0,magicAttack:0,critDamage:0,magicSkillAmp:0,accuracy:0,penetration:0},{hp:20,magicAttack:5,critDamage:0,magicSkillAmp:0,accuracy:0,penetration:0},{hp:30,magicAttack:10,critDamage:0,magicSkillAmp:0,accuracy:0,penetration:0},{hp:40,magicAttack:15,critDamage:0,magicSkillAmp:0,accuracy:0,penetration:0},{hp:50,magicAttack:20,critDamage:1,magicSkillAmp:0,accuracy:0,penetration:0},{hp:60,magicAttack:25,critDamage:2,magicSkillAmp:0,accuracy:0,penetration:0},{hp:70,magicAttack:30,critDamage:3,magicSkillAmp:1,accuracy:0,penetration:0},{hp:80,magicAttack:30,critDamage:4,magicSkillAmp:2,accuracy:0,penetration:0},{hp:90,magicAttack:40,critDamage:5,magicSkillAmp:3,accuracy:0,penetration:0},{hp:100,magicAttack:45,critDamage:6,magicSkillAmp:4,accuracy:10,penetration:0},{hp:110,magicAttack:50,critDamage:7,magicSkillAmp:5,accuracy:20,penetration:0},{hp:120,magicAttack:55,critDamage:8,magicSkillAmp:6,accuracy:30,penetration:0},{hp:130,magicAttack:60,critDamage:9,magicSkillAmp:7,accuracy:40,penetration:10},{hp:140,magicAttack:65,critDamage:10,magicSkillAmp:8,accuracy:50,penetration:20},{hp:150,magicAttack:70,critDamage:11,magicSkillAmp:9,accuracy:60,penetration:30},{hp:160,magicAttack:75,critDamage:12,magicSkillAmp:10,accuracy:70,penetration:40},{hp:170,magicAttack:85,critDamage:14,magicSkillAmp:11,accuracy:80,penetration:50},{hp:180,magicAttack:95,critDamage:16,magicSkillAmp:12,accuracy:90,penetration:60},{hp:190,magicAttack:105,critDamage:18,magicSkillAmp:13,accuracy:100,penetration:70},{hp:200,magicAttack:115,critDamage:20,magicSkillAmp:14,accuracy:110,penetration:80},{hp:210,magicAttack:125,critDamage:22,magicSkillAmp:14,accuracy:110,penetration:80}]},guardianBelt:{upgrades:[{hp:0,defense:0,resistCritDmg:0,resistSkillAmp:0,ignorePenetration:0,resistCritRate:0},{hp:20,defense:7,resistCritDmg:2,resistSkillAmp:0,ignorePenetration:0,resistCritRate:0},{hp:30,defense:14,resistCritDmg:4,resistSkillAmp:0,ignorePenetration:0,resistCritRate:0},{hp:40,defense:21,resistCritDmg:6,resistSkillAmp:0,ignorePenetration:0,resistCritRate:0},{hp:50,defense:28,resistCritDmg:8,resistSkillAmp:0,ignorePenetration:0,resistCritRate:0},{hp:60,defense:35,resistCritDmg:10,resistSkillAmp:2,ignorePenetration:0,resistCritRate:0},{hp:70,defense:42,resistCritDmg:12,resistSkillAmp:3,ignorePenetration:0,resistCritRate:0},{hp:80,defense:42,resistCritDmg:14,resistSkillAmp:4,ignorePenetration:0,resistCritRate:0},{hp:90,defense:56,resistCritDmg:16,resistSkillAmp:5,ignorePenetration:5,resistCritRate:0},{hp:100,defense:63,resistCritDmg:18,resistSkillAmp:6,ignorePenetration:10,resistCritRate:0},{hp:110,defense:70,resistCritDmg:20,resistSkillAmp:7,ignorePenetration:15,resistCritRate:1},{hp:120,defense:77,resistCritDmg:22,resistSkillAmp:8,ignorePenetration:20,resistCritRate:2},{hp:130,defense:84,resistCritDmg:24,resistSkillAmp:9,ignorePenetration:25,resistCritRate:3},{hp:140,defense:91,resistCritDmg:27,resistSkillAmp:10,ignorePenetration:30,resistCritRate:4},{hp:150,defense:98,resistCritDmg:30,resistSkillAmp:12,ignorePenetration:40,resistCritRate:5},{hp:160,defense:105,resistCritDmg:33,resistSkillAmp:14,ignorePenetration:50,resistCritRate:6},{hp:170,defense:112,resistCritDmg:36,resistSkillAmp:16,ignorePenetration:60,resistCritRate:7},{hp:180,defense:119,resistCritDmg:39,resistSkillAmp:18,ignorePenetration:70,resistCritRate:8},{hp:190,defense:126,resistCritDmg:39,resistSkillAmp:18,ignorePenetration:70,resistCritRate:8},{hp:200,defense:133,resistCritDmg:39,resistSkillAmp:20,ignorePenetration:80,resistCritRate:8},{hp:210,defense:140,resistCritDmg:39,resistSkillAmp:20,ignorePenetration:80,resistCritRate:8}]}},window.RingsData={ringDefinitions:[{id:"ring_of_luck_1",name:"Ring of Luck +1",type:"ring",imagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/rings/ring_of_luck_1.png",description:"A ring imbued with luck that increases critical hit rate.",baseStats:{critRate:10}},{id:"ring_of_luck_2",name:"Ring of Luck +2",type:"ring",imagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/rings/ring_of_luck_2.png",description:"An enhanced ring of luck that further increases critical hit rate.",baseStats:{critRate:15}},{id:"ring_of_luck_3",name:"Ring of Luck +3",type:"ring",imagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/rings/ring_of_luck_3.png",description:"A masterfully enhanced ring of luck that greatly increases critical hit rate.",baseStats:{critRate:17}},{id:"tempus_ring",name:"Tempus Ring",type:"ring",imagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/rings/tempus_ring.png",description:"A legendary ring that enhances critical strikes and overall combat effectiveness.",baseStats:{critDamage:5,critRate:16,penetration:50,allAttackUp:15,accuracy:100,hp:50}},{id:"ring_of_dr_mazel",name:"Ring of Dr. Mazel",type:"ring",imagePath:forceguidesPlannerData.pluginUrl+"assets/images/equipment/rings/ring_of_dr_mazel.png",description:"A powerful ring created by the legendary Dr. Mazel, providing exceptional combat bonuses and defensive capabilities.",baseStats:{critDamage:18,allSkillAmp:6,defense:40,damageReduce:30,penetration:50,accuracy:100,hp:50,allAttackUp:18}}],init:function(){window.EquipmentData&&EquipmentData.items&&(EquipmentData.items.rings=this.ringDefinitions)}},window.RingsData&&"function"==typeof RingsData.init&&RingsData.init(),window.EquipmentDetailView={system:null,elements:{},init:function(e){return this.system=e,this.elements={detailsPanel:e.elements.detailsPanel,detailsTitle:e.elements.detailsTitle,detailsContent:e.elements.detailsContent},this},updateDetailsPanel:function(e){this.elements.detailsTitle.textContent=`${e.name} Details`;const a=this.system.data.equippedItems[e.id];a&&(a.type||e.type,this.system.data.weaponUpgrades&&!this.system.data.weaponUpgrades.items&&(this.system.data.weaponUpgrades.items={}),this.system.data.weaponUpgrades.items[e.id]||(this.system.data.weaponUpgrades.items[e.id]={weaponId:a.id,grade:0,epicOption:{id:null,level:0},activeSlots:1,slotOptions:new Array(3).fill(null),extremeLevel:0,divineLevel:0,chaosLevel:0}),this.system.data.weaponUpgrades.settings=this.system.data.weaponUpgrades.items[e.id],this.system.data.weaponUpgrades.activeWeapon=a.id);let t="";t=a?this.renderItemDetailsUI(e,a):`\n                <div class="fg-empty-slot">\n                    <p>No ${e.name} equipped</p>\n                    <p class="fg-slot-type">${this.formatSlotType(e.type)}</p>\n                    <button class="fg-button fg-equip-item-btn" data-slot-id="${e.id}">Select ${e.name}</button>\n                </div>\n            `,this.elements.detailsContent.innerHTML=t,this.setupDetailUIEventListeners(e,a)},renderItemDetailsUI:function(e,a){const t=a.type||e.type;if(window.EquipmentData&&EquipmentData.itemTypes&&EquipmentData.itemTypes[t]){const i=EquipmentData.itemTypes[t],n=i.upgradePaths||[];if(0===n.length)return this.renderBasicItemDetails(a);let l=`\n                <div class="fg-item-full-details">\n                    <div class="fg-item-header">\n                        <img src="${a.imagePath}" alt="${a.name}" class="fg-item-thumbnail">\n                        <div class="fg-item-title">\n                            <h4>${a.name}</h4>\n                            <div class="fg-item-type">${this.formatItemType(t)}</div>\n                        </div>\n                    </div>\n                    <div class="fg-item-description">${a.description||""}</div>\n            `;return n.includes("base")&&(l+=this.renderBaseUpgradeSection(a)),n.includes("epic")&&i.canHaveEpicOption&&(l+=this.renderEpicOptionSection(a)),n.includes("slots")&&i.hasSlots&&(l+=this.renderSlotsSection(a)),n.includes("extreme")&&(l+=this.renderExtremeUpgradeSection(a)),n.includes("divine")&&(l+=this.renderDivineUpgradeSection(a)),n.includes("chaos")&&(l+=this.renderChaosUpgradeSection(a)),l+=`\n                <div class="fg-item-controls">\n                    <button class="fg-button fg-remove-item-btn" data-slot-id="${e.id}">Remove ${this.formatItemType(t)}</button>\n                </div>\n            </div>`,l}return this.renderBasicItemDetails(a)},setupDetailUIEventListeners:function(e,a){const t=this.elements.detailsContent.querySelector(".fg-equip-item-btn");t&&t.addEventListener("click",(()=>{this.system.openEquipmentModal(e.id,e.type)}));const i=this.elements.detailsContent.querySelector(".fg-remove-item-btn");if(i&&i.addEventListener("click",(()=>{this.system.removeEquippedItem(e.id)})),!a)return;const n=a.type||e.type;if(window.EquipmentData&&EquipmentData.itemTypes&&EquipmentData.itemTypes[n]){const e=EquipmentData.itemTypes[n],t=e.upgradePaths||[];t.includes("base")&&this.setupGradeUpgradeEvents(a),t.includes("epic")&&e.canHaveEpicOption&&this.setupEpicOptionEvents(a),t.includes("slots")&&e.hasSlots&&this.setupSlotOptionEvents(a),t.includes("extreme")&&this.setupExtremeUpgradeEvents(a),t.includes("divine")&&this.setupDivineUpgradeEvents(a),t.includes("chaos")&&this.setupChaosUpgradeEvents(a)}},renderBasicItemDetails:function(e){let a="";const t=e.baseStats||e.stats||{};for(const e in t){const i=t[e];a+=`<div class="fg-item-stat"><span class="fg-stat-name">${this.formatStatName(e)}</span>: <span class="fg-stat-value">${i}</span></div>`}return`\n            <div class="fg-equipped-item">\n                <div class="fg-item-header">\n                    <img src="${e.imagePath}" alt="${e.name}" class="fg-item-thumbnail">\n                    <h4>${e.name}</h4>\n                </div>\n                <div class="fg-item-description">${e.description||""}</div>\n                <div class="fg-item-stats">\n                    ${a}\n                </div>\n                <button class="fg-button fg-remove-item-btn" data-slot-id="${e.slotId}">Remove Item</button>\n            </div>\n        `},renderItemBaseStats:function(e){const a=e.baseStats||e.stats||{};let t="";for(const e in a){const i=a[e];t+=`\n                <div class="fg-stat-item">\n                    <span class="fg-stat-name">${this.formatStatName(e)}:</span>\n                    <span class="fg-stat-value">${i}${this.isPercentStat(e)?"%":""}</span>\n                </div>\n            `}return t||'<div class="fg-no-stats">No base stats available</div>'},formatItemType:function(e){return e?e.charAt(0).toUpperCase()+e.slice(1):""},formatBonusesList:function(e){if(!e||0===e.length)return'<div class="fg-no-bonuses">No bonuses at this level</div>';let a='<div class="fg-bonuses-list">';return e.forEach((e=>{const t="undefined"!=typeof StatsConfig?StatsConfig.getStatInfo(e.stat).name:this.formatStatName(e.stat),i="undefined"!=typeof StatsConfig?StatsConfig.formatStatValue(e.stat,e.value):this.isPercentStat(e.stat)?e.value+"%":e.value;let n="";"undefined"!=typeof StatsConfig&&StatsConfig.getStatIconUrl&&(n=`<img src="${StatsConfig.getStatIconUrl(e.stat)}" alt="${t}" class="fg-stat-icon">`),a+=`\n                <div class="fg-bonus-item">\n                    ${n}\n                    <span class="fg-stat-name">${t}:</span>\n                    <span class="fg-stat-value">+${i}</span>\n                </div>\n            `})),a+="</div>",a},calculateTotalStatValue:function(e,a){return(e||0)+(a||0)},formatWeaponType:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},isPercentStat:function(e){return"undefined"!=typeof StatsConfig&&StatsConfig.getStatInfo(e).isPercentage||!1},formatStatName:function(e){return"undefined"!=typeof StatsConfig&&StatsConfig.getStatInfo(e).name||e},formatSlotType:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},renderBaseUpgradeSection:function(e){const a=(this.system.data.weaponUpgrades.settings||{}).grade||0;if("belt"===e.type)return this.renderBeltUpgradeSection(e,a);let t="";const i=e.baseStats||{};i.attack&&(t+=`\n                <div class="fg-stat-item">\n                    <span class="fg-stat-name">Attack:</span>\n                    <span class="fg-stat-value">${i.attack}</span>\n                </div>`),i.magicAttack&&(t+=`\n                <div class="fg-stat-item">\n                    <span class="fg-stat-name">Magic Attack:</span>\n                    <span class="fg-stat-value">${i.magicAttack}</span>\n                </div>`),i.attackRate&&(t+=`\n                <div class="fg-stat-item">\n                    <span class="fg-stat-name">Attack Rate:</span>\n                    <span class="fg-stat-value">${i.attackRate}</span>\n                </div>`);for(const e in i)["attack","magicAttack","attackRate"].includes(e)||(t+=`\n                    <div class="fg-stat-item">\n                        <span class="fg-stat-name">${this.formatStatName(e)}:</span>\n                        <span class="fg-stat-value">${i[e]}${this.isPercentStat(e)?"%":""}</span>\n                    </div>`);return`\n            <div class="fg-upgrade-section fg-grade-upgrade">\n                <h5>${e.name} +${a}</h5>\n                <div class="fg-slider-container">\n                    <input type="range" min="0" max="20" value="${a}" class="fg-grade-slider" id="fg-grade-slider">\n                    <div class="fg-grade-value">${a}</div>\n                </div>\n                <div class="fg-combined-stats">\n                    ${t}\n                </div>\n            </div>\n        `},renderBeltUpgradeSection:function(e,a){let t={};if(window.BeltsData&&e.upgradeData){const i=window.BeltsData[e.upgradeData];i&&Array.isArray(i.upgrades)&&a>=0&&a<i.upgrades.length&&(t=i.upgrades[a])}let i="";const n=e.baseStats||{};for(const e in n){const a=n[e],l=t[e]||0,s=this.calculateTotalStatValue(a,l);0!==s&&(i+=`\n                <div class="fg-stat-item">\n                    <span class="fg-stat-name">${this.formatStatName(e)}:</span>\n                    <span class="fg-stat-value">${s}${this.isPercentStat(e)?"%":""}</span>\n                </div>`)}for(const e in t)!n[e]&&t[e]>0&&(i+=`\n                    <div class="fg-stat-item">\n                        <span class="fg-stat-name">${this.formatStatName(e)}:</span>\n                        <span class="fg-stat-value">${t[e]}${this.isPercentStat(e)?"%":""}</span>\n                    </div>`);return`\n            <div class="fg-upgrade-section fg-grade-upgrade">\n                <h5>${e.name} +${a}</h5>\n                <div class="fg-slider-container">\n                    <input type="range" min="0" max="20" value="${a}" class="fg-grade-slider" id="fg-grade-slider">\n                    <div class="fg-grade-value">${a}</div>\n                </div>\n                <div class="fg-combined-stats">\n                    ${i}\n                </div>\n            </div>\n        `},renderEpicOptionSection:function(e){const a=(this.system.data.weaponUpgrades.settings||{}).epicOption||{id:null,level:0},t=window.EquipmentData?EquipmentData.epicOptions:[],i=t.find((e=>e.id===a.id))||t[0]||{},n=i.levels&&i.levels[a.level]?.value||0;let l="";t.forEach((e=>{l+=`<option value="${e.id}" ${a.id===e.id?"selected":""}>${e.name}</option>`}));let s=[];if(i.id&&n>0){const e=i.id;s.push({stat:e,value:n})}return`\n            <div class="fg-upgrade-section fg-epic-option">\n                <h5>Epic Option (Level ${a.level})</h5>\n                <div class="fg-epic-option-row">\n                    <select class="fg-epic-option-select" id="fg-epic-option-select">\n                        ${l}\n                    </select>\n                </div>\n                <div class="fg-epic-level-buttons">\n                    <button class="fg-button fg-epic-level-btn ${0===a.level?"fg-active":""}" data-level="0">Level 0</button>\n                    <button class="fg-button fg-epic-level-btn ${1===a.level?"fg-active":""}" data-level="1">Level 1</button>\n                    <button class="fg-button fg-epic-level-btn ${2===a.level?"fg-active":""}" data-level="2">Level 2</button>\n                </div>\n                <div class="fg-epic-bonuses">\n                    ${this.formatBonusesList(s)}\n                </div>\n            </div>\n        `},renderSlotsSection:function(e){const a=this.system.data.weaponUpgrades.settings||{},t=a.activeSlots||1,i=e.maxSlots||3,n=window.EquipmentData?EquipmentData.slotOptions:[];let l="";n.forEach((e=>{l+=`<option value="${e.id}">${e.name} (+${e.value}${this.isPercentStat(e.id)?"%":""})</option>`}));let s=[];if(Array.isArray(a.slotOptions))for(let e=0;e<t;e++){const t=a.slotOptions[e];if(t){const e=n.find((e=>e.id===t));if(e){const a=e.id,t=s.find((e=>e.stat===a));t?t.value+=e.value:s.push({stat:a,value:e.value})}}}return`\n            <div class="fg-upgrade-section fg-option-slots">\n                <h5>Option Slots (${t}/${i})</h5>\n                <div class="fg-slot-container">\n                    <div class="fg-slot-row ${t>=1?"fg-slot-active":"fg-slot-inactive"}">\n                        <div class="fg-slot-number">1</div>\n                        <select class="fg-slot-option-select" id="fg-slot-option-1" ${t>=1?"":"disabled"}>\n                            ${l}\n                        </select>\n                    </div>\n                    <div class="fg-slot-row ${t>=2?"fg-slot-active":"fg-slot-inactive"}">\n                        <div class="fg-slot-number">2</div>\n                        <select class="fg-slot-option-select" id="fg-slot-option-2" ${t>=2?"":"disabled"}>\n                            ${l}\n                        </select>\n                    </div>\n                    <div class="fg-slot-row ${t>=3?"fg-slot-active":"fg-slot-inactive"}">\n                        <div class="fg-slot-number">3</div>\n                        <select class="fg-slot-option-select" id="fg-slot-option-3" ${t>=3?"":"disabled"}>\n                            ${l}\n                        </select>\n                    </div>\n                </div>\n                <div class="fg-slot-buttons">\n                    <button class="fg-button fg-slot-count-btn ${1===t?"fg-active":""}" data-count="1">1 Slot</button>\n                    <button class="fg-button fg-slot-count-btn ${2===t?"fg-active":""}" data-count="2" ${i<2?"disabled":""}>2 Slots</button>\n                    <button class="fg-button fg-slot-count-btn ${3===t?"fg-active":""}" data-count="3" ${i<3?"disabled":""}>3 Slots</button>\n                </div>\n                <div class="fg-slots-bonuses">\n                    ${this.formatBonusesList(s)}\n                </div>\n            </div>\n        `},renderExtremeUpgradeSection:function(e){const a=(this.system.data.weaponUpgrades.settings||{}).extremeLevel||0,t=e.maxExtremeLevel||7,i=window.EquipmentData&&EquipmentData.extremeUpgrades?EquipmentData.extremeUpgrades.level7:[];let n=[];if(a>0&&a<i.length){const t="two-handed"===e.handType||!0===e.twoHanded;n=JSON.parse(JSON.stringify(i[a]||[])),t&&n.forEach((e=>{e.value=2*e.value}))}return`\n            <div class="fg-upgrade-section fg-extreme-upgrade">\n                <h5>Extreme Upgrade (${a}/${t})</h5>\n                <div class="fg-slider-container">\n                    <input type="range" min="0" max="${t}" value="${a}" class="fg-extreme-slider" id="fg-extreme-slider">\n                    <div class="fg-extreme-value">${a}</div>\n                </div>\n                <div class="fg-extreme-bonuses">\n                    ${this.formatBonusesList(n)}\n                </div>\n            </div>\n        `},renderDivineUpgradeSection:function(e){const a=(this.system.data.weaponUpgrades.settings||{}).divineLevel||0;let t=[];const i=window.WeaponsData&&WeaponsData.divineUpgrades?WeaponsData.divineUpgrades.high:null;if(a>0&&i)for(const e in i){const n=i[e];if(n&&a<n.length){const i=n[a];i>0&&t.push({stat:e,value:i})}}return`\n            <div class="fg-upgrade-section fg-divine-upgrade">\n                <h5>Divine Upgrade (${a}/15)</h5>\n                <div class="fg-slider-container">\n                    <input type="range" min="0" max="15" value="${a}" class="fg-divine-slider" id="fg-divine-slider">\n                    <div class="fg-divine-value">${a}</div>\n                </div>\n                <div class="fg-divine-bonuses">\n                    ${this.formatBonusesList(t)}\n                </div>\n            </div>\n        `},renderChaosUpgradeSection:function(e){const a=(this.system.data.weaponUpgrades.settings||{}).chaosLevel||0,t=e.chaosTier||"gold";let i=[];if(window.EquipmentData&&EquipmentData.chaosUpgrades&&EquipmentData.chaosUpgrades[t]){const e=EquipmentData.chaosUpgrades[t]||[];i=a>0&&a<e.length?e[a]:[]}return`\n            <div class="fg-upgrade-section fg-chaos-upgrade">\n                <h5>Chaos Upgrade (${a}/15)</h5>\n                <div class="fg-tier-info">Tier: ${t.charAt(0).toUpperCase()+t.slice(1)}</div>\n                <div class="fg-slider-container">\n                    <input type="range" min="0" max="15" value="${a}" class="fg-chaos-slider" id="fg-chaos-slider">\n                    <div class="fg-chaos-value">${a}</div>\n                </div>\n                <div class="fg-chaos-bonuses">\n                    ${this.formatBonusesList(i)}\n                </div>\n            </div>\n        `},calculateTotalItemStats:function(e){if("weapon"===e.type){const e=this.system.calculateStats();let a='<div class="fg-stat-list-container"><ul class="fg-stat-list">';const t={offensive:[],defensive:[],utility:[]},i=(e,a)=>{if(!a)return;let i="utility";if("undefined"!=typeof StatsConfig&&StatsConfig.getStatInfo){const a=StatsConfig.getStatInfo(e);a&&a.category&&(i=a.category)}t[i]&&t[i].push({id:e,value:a})};for(const a in e)e[a]&&i(a,e[a]);const n={offensive:"Attack Stats",defensive:"Defense Stats",utility:"Utility Stats"};for(const e in t)t[e].length>0&&(a+=`<li class="fg-stat-category-header">${n[e]}</li>`,t[e].forEach((e=>{const t=e.id,i=e.value,n="undefined"!=typeof StatsConfig?StatsConfig.getStatInfo(t).name:t,l="undefined"!=typeof StatsConfig?StatsConfig.formatStatValue(t,i):i;let s="";"undefined"!=typeof StatsConfig&&StatsConfig.getStatIconUrl&&(s=`<img src="${StatsConfig.getStatIconUrl(t)}" alt="${n}" class="fg-stat-icon">`),a+=`<li>${s}<span class="fg-stat-name">${n}:</span> <span class="fg-stat-value">${l}</span></li>`})));return a+="</ul></div>",a}return'<div class="fg-total-stats-placeholder">\n            <p>Base stats will apply.</p>\n        </div>'},setupGradeUpgradeEvents:function(e){const a=document.getElementById("fg-grade-slider");if(a){const t=this.system.data.weaponUpgrades.settings,i=t.grade||0;a.value=i;const n=document.querySelector(".fg-grade-value");n&&(n.textContent=i);const l=document.querySelector(".fg-grade-upgrade h5");l&&(l.textContent=`${e.name} +${i}`),"belt"===e.type?this.updateBeltUpgradeBonuses(e):this.updateWeaponStats(),a.addEventListener("input",(a=>{const i=parseInt(a.target.value);t.grade=i,document.querySelector(".fg-grade-value").textContent=i;const n=document.querySelector(".fg-grade-upgrade h5");n&&(n.textContent=`${e.name} +${i}`),"belt"===e.type?this.updateBeltUpgradeBonuses(e):this.updateWeaponStats()}))}},updateBeltUpgradeBonuses:function(e){const a=this.system.data.weaponUpgrades.settings.grade||0,t=document.querySelector(".fg-grade-upgrade h5");t&&(t.textContent=`${e.name} +${a}`);const i=document.querySelector(".fg-combined-stats");if(!i)return;let n={};if(window.BeltsData&&e.upgradeData){const t=window.BeltsData[e.upgradeData];t&&Array.isArray(t.upgrades)&&a>=0&&a<t.upgrades.length&&(n=t.upgrades[a])}let l="";const s=e.baseStats||{};for(const e in s){const a=s[e],t=n[e]||0,i=this.calculateTotalStatValue(a,t);0!==i&&(l+=`\n                <div class="fg-stat-item">\n                    <span class="fg-stat-name">${this.formatStatName(e)}:</span>\n                    <span class="fg-stat-value">${i}${this.isPercentStat(e)?"%":""}</span>\n                </div>`)}for(const e in n)!s[e]&&n[e]>0&&(l+=`\n                    <div class="fg-stat-item">\n                        <span class="fg-stat-name">${this.formatStatName(e)}:</span>\n                        <span class="fg-stat-value">${n[e]}${this.isPercentStat(e)?"%":""}</span>\n                    </div>`);i.innerHTML=l,this.system.updateStats()},setupEpicOptionEvents:function(e){const a=document.getElementById("fg-epic-option-select");if(a){a.addEventListener("change",(e=>{this.system.data.weaponUpgrades.settings.epicOption.id=e.target.value,this.updateWeaponStats()}));const e=this.system.data.weaponUpgrades.settings;e.epicOption&&e.epicOption.id?a.value=e.epicOption.id:a.options.length>0&&(e.epicOption={id:a.options[0].value,level:0},a.value=e.epicOption.id)}const t=document.querySelectorAll(".fg-epic-level-btn");t.forEach((e=>{e.addEventListener("click",(e=>{const a=parseInt(e.target.getAttribute("data-level"));this.system.data.weaponUpgrades.settings.epicOption.level=a,t.forEach((e=>e.classList.remove("fg-active"))),e.target.classList.add("fg-active"),this.updateWeaponStats()}))}))},setupSlotOptionEvents:function(e){const a=document.querySelectorAll(".fg-slot-count-btn");a.forEach((e=>{e.addEventListener("click",(e=>{if(e.target.hasAttribute("disabled"))return;const t=parseInt(e.target.getAttribute("data-count"));this.system.data.weaponUpgrades.settings.activeSlots=t,a.forEach((e=>e.classList.remove("fg-active"))),e.target.classList.add("fg-active"),document.querySelectorAll(".fg-slot-row").forEach(((e,a)=>{a<t?(e.classList.remove("fg-slot-inactive"),e.classList.add("fg-slot-active"),e.querySelector("select").removeAttribute("disabled")):(e.classList.remove("fg-slot-active"),e.classList.add("fg-slot-inactive"),e.querySelector("select").setAttribute("disabled","disabled"))})),this.updateWeaponStats()}))}));for(let e=1;e<=3;e++){const a=document.getElementById(`fg-slot-option-${e}`);if(a){a.addEventListener("change",(a=>{const t=this.system.data.weaponUpgrades.settings;(!Array.isArray(t.slotOptions)||t.slotOptions.length<e)&&(t.slotOptions=new Array(3).fill(null)),t.slotOptions[e-1]=a.target.value,this.updateWeaponStats()}));const t=this.system.data.weaponUpgrades.settings;if((!Array.isArray(t.slotOptions)||t.slotOptions.length<3)&&(t.slotOptions=new Array(3).fill(null)),t.slotOptions&&t.slotOptions[e-1])a.value=t.slotOptions[e-1];else if(a.options.length>0){const i=a.options[0].value;t.slotOptions[e-1]=i,a.value=i}}}},setupExtremeUpgradeEvents:function(e){const a=document.getElementById("fg-extreme-slider");a&&a.addEventListener("input",(a=>{const t=parseInt(a.target.value);this.system.data.weaponUpgrades.settings.extremeLevel=t,document.querySelector(".fg-extreme-value").textContent=t;const i=document.querySelector(".fg-extreme-upgrade h5");if(i){const a=e.maxExtremeLevel||7;i.textContent=`Extreme Upgrade (${t}/${a})`}this.updateWeaponStats()}))},setupDivineUpgradeEvents:function(e){const a=document.getElementById("fg-divine-slider");if(a){const e=this.system.data.weaponUpgrades.settings,t=e.divineLevel||0;a.value=t,a.addEventListener("input",(a=>{const t=parseInt(a.target.value);e.divineLevel=t,document.querySelector(".fg-divine-value").textContent=t;const i=document.querySelector(".fg-divine-upgrade h5");i&&(i.textContent=`Divine Upgrade (${t}/15)`),this.updateWeaponStats()}))}},setupChaosUpgradeEvents:function(e){const a=document.getElementById("fg-chaos-slider");a&&a.addEventListener("input",(e=>{const a=parseInt(e.target.value);this.system.data.weaponUpgrades.settings.chaosLevel=a,document.querySelector(".fg-chaos-value").textContent=a;const t=document.querySelector(".fg-chaos-upgrade h5");t&&(t.textContent=`Chaos Upgrade (${a}/15)`),this.updateWeaponStats()}))},updateWeaponStats:function(){const e=this.system.data.selectedSlot;if(!e)return;const a=this.system.data.equippedItems[e.id];if(!a)return;const t=this.system.data.weaponUpgrades.settings,i=t.grade||0,n=document.querySelector(".fg-grade-upgrade h5");n&&(n.textContent=`${a.name} +${i}`);const l=document.querySelector(".fg-combined-stats");if(l){const e=a.subtype||a.material||"orb";let t="";const n=a.baseStats||{};n.attack&&(t+=`\n                    <div class="fg-stat-item">\n                        <span class="fg-stat-name">Attack:</span>\n                        <span class="fg-stat-value">${n.attack+(window.WeaponsData?WeaponsData.getUpgradeStat(e,"attack",i):0)}</span>\n                    </div>`),n.magicAttack&&(t+=`\n                    <div class="fg-stat-item">\n                        <span class="fg-stat-name">Magic Attack:</span>\n                        <span class="fg-stat-value">${n.magicAttack+(window.WeaponsData?WeaponsData.getUpgradeStat(e,"magicAttack",i):0)}</span>\n                    </div>`),n.attackRate&&(t+=`\n                    <div class="fg-stat-item">\n                        <span class="fg-stat-name">Attack Rate:</span>\n                        <span class="fg-stat-value">${n.attackRate+(window.WeaponsData?WeaponsData.getUpgradeStat(e,"attackRate",i):0)}</span>\n                    </div>`);for(const e in n)["attack","magicAttack","attackRate"].includes(e)||0===n[e]||(t+=`\n                        <div class="fg-stat-item">\n                            <span class="fg-stat-name">${this.formatStatName(e)}:</span>\n                            <span class="fg-stat-value">${n[e]}${this.isPercentStat(e)?"%":""}</span>\n                        </div>`);l.innerHTML=t}const s=document.querySelector(".fg-epic-bonuses");if(s){const e=window.EquipmentData?EquipmentData.epicOptions:[],a=t.epicOption||{id:null,level:0},i=e.find((e=>e.id===a.id))||e[0]||{},n=i.levels&&i.levels[a.level]?.value||0;let l=[];if(i.id&&n>0){const e=i.id;l.push({stat:e,value:n})}s.innerHTML=this.formatBonusesList(l)}const r=document.querySelector(".fg-slots-bonuses");if(r){const e=window.EquipmentData?EquipmentData.slotOptions:[],a=t.activeSlots||1;let i=[];if(Array.isArray(t.slotOptions))for(let n=0;n<a;n++){const a=t.slotOptions[n];if(a){const t=e.find((e=>e.id===a));if(t){const e=t.id,a=i.find((a=>a.stat===e));a?a.value+=t.value:i.push({stat:e,value:t.value})}}}r.innerHTML=this.formatBonusesList(i)}if("weapon"===a.type){const e=document.querySelector(".fg-extreme-bonuses");if(e){const i=window.EquipmentData&&EquipmentData.extremeUpgrades?EquipmentData.extremeUpgrades.level7:[];let n=[];if(t.extremeLevel>0&&t.extremeLevel<i.length){const e=["greatsword","daikatana"].includes(a.subtype)||"two-handed"===a.handType||!0===a.twoHanded;n=JSON.parse(JSON.stringify(i[t.extremeLevel]||[])),e&&n.forEach((e=>{e.value=2*e.value}))}e.innerHTML=this.formatBonusesList(n)}const i=document.querySelector(".fg-divine-bonuses");if(i){const e=window.WeaponsData&&WeaponsData.divineUpgrades?WeaponsData.divineUpgrades.high:null;let a=[];if(t.divineLevel>0&&e)for(const i in e){const n=e[i];if(n&&t.divineLevel<n.length){const e=n[t.divineLevel];e>0&&a.push({stat:i,value:e})}}i.innerHTML=this.formatBonusesList(a)}}if(["earring","bracelet","amulet"].includes(a.type)){const e=document.querySelector(".fg-chaos-bonuses");if(e){const i=a.chaosTier||"gold";let n=[];if(window.EquipmentData&&EquipmentData.chaosUpgrades&&EquipmentData.chaosUpgrades[i]){const e=EquipmentData.chaosUpgrades[i];n=t.chaosLevel>0&&t.chaosLevel<e.length?e[t.chaosLevel]:[]}e.innerHTML=this.formatBonusesList(n)}}this.system.updateStats()}},window.CostumeData={typeStats:{generic:[{id:"hp",value:100},{id:"attack",value:20},{id:"magicAttack",value:20},{id:"defense",value:15},{id:"evasion",value:100},{id:"critDamage",value:4},{id:"critRate",value:1},{id:"maxCritRate",value:1},{id:"swordSkillAmp",value:2},{id:"magicSkillAmp",value:2},{id:"resistCritRate",value:1},{id:"accuracy",value:50},{id:"penetration",value:10}],forceWing:[{id:"hp",value:200},{id:"attack",value:30},{id:"damageReduce",value:10},{id:"critDamage",value:3},{id:"swordSkillAmp",value:2},{id:"magicSkillAmp",value:2},{id:"accuracy",value:100},{id:"ignoreAccuracy",value:20},{id:"penetration",value:15},{id:"ignoreDamageReduce",value:20}],vehicle:[{id:"hp",value:200},{id:"defense",value:20},{id:"evasion",value:50},{id:"ignoreEvasion",value:100},{id:"critDamage",value:3},{id:"accuracy",value:70},{id:"ignoreAccuracy",value:20},{id:"penetration",value:15},{id:"ignoreDamageReduce",value:20},{id:"ignoreResistCritRate",value:1},{id:"ignoreResistCritDmg",value:4}]},metaOptions:{generic:{penetration:{name:"Penetration",statId:"penetration",values:[10,15,20,30],grades:[1,2,3,4],chances:[25,50,15,10]},critDamage:{name:"Crit. DMG",statId:"critDamage",values:[3,5,7,10],grades:[1,2,3,4],chances:[25,50,15,10]},allSkillAmp:{name:"All Skill Amp.",statId:"allSkillAmp",values:[1,2,3,5],grades:[1,2,3,4],chances:[25,50,15,10]},resistCritRate:{name:"Resist Crit. Rate",statId:"resistCritRate",values:[1,2,3,4],grades:[1,2,3,4],chances:[25,50,15,10]},allAttackUp:{name:"All Attack Up",statId:"allAttackUp",values:[20,30,40,50],grades:[1,2,3,4],chances:[25,50,15,10]},defense:{name:"Defense",statId:"defense",values:[15,20,30,40],grades:[1,2,3,4],chances:[25,50,15,10]},evasion:{name:"Evasion",statId:"evasion",values:[50,70,100,150],grades:[1,2,3,4],chances:[25,50,15,10]},hp:{name:"HP",statId:"hp",values:[50,70,100,150],grades:[1,2,3,4],chances:[25,50,15,10]}},forceWing:{hp:{name:"HP",statId:"hp",values:[100,150,200,300],grades:[1,2,3,4],chances:[25,50,15,10]},defense:{name:"Defense",statId:"defense",values:[30,40,50,60],grades:[1,2,3,4],chances:[25,50,15,10]},allAttackUp:{name:"All Attack Up",statId:"allAttackUp",values:[30,40,50,60],grades:[1,2,3,4],chances:[25,50,15,10]},allSkillAmp:{name:"All Skill Amp.",statId:"allSkillAmp",values:[2,3,4,5],grades:[1,2,3,4],chances:[25,50,15,10]},critDamage:{name:"Crit. DMG",statId:"critDamage",values:[4,7,9,11],grades:[1,2,3,4],chances:[25,50,15,10]},normalDmgUp:{name:"Normal DMG Up",statId:"normalDmgUp",values:[5,8,11,14],grades:[1,2,3,4],chances:[25,50,15,10]},addDmg:{name:"Add DMG",statId:"addDmg",values:[15,30,45,60],grades:[1,2,3,4],chances:[25,50,15,10]},ignoreDmgReduce:{name:"Ignore DMG Reduce",statId:"ignoreDmgReduce",values:[20,40,60,80],grades:[1,2,3,4],chances:[25,50,15,10]}},vehicle:{hp:{name:"HP",statId:"hp",values:[100,150,200,300],grades:[1,2,3,4],chances:[25,50,15,10]},defense:{name:"Defense",statId:"defense",values:[20,30,40,60],grades:[1,2,3,4],chances:[25,50,15,10]},ignorePenetration:{name:"Ignore Penetration",statId:"ignorePenetration",values:[20,25,35,45],grades:[1,2,3,4],chances:[25,50,15,10]},allAttackUp:{name:"All Attack Up",statId:"allAttackUp",values:[30,40,50,70],grades:[1,2,3,4],chances:[25,50,15,10]},allSkillAmp:{name:"All Skill Amp.",statId:"allSkillAmp",values:[3,5,7,9],grades:[1,2,3,4],chances:[25,50,15,10]},ignoreResistSkillAmp:{name:"Ignore Resist Skill Amp.",statId:"ignoreResistSkillAmp",values:[3,5,7,9],grades:[1,2,3,4],chances:[25,50,15,10]},critDamage:{name:"Crit. DMG",statId:"critDamage",values:[3,5,7,9],grades:[1,2,3,4],chances:[25,50,15,10]},resistCritDmg:{name:"Resist Crit. DMG",statId:"resistCritDmg",values:[8,12,16,20],grades:[1,2,3,4],chances:[25,50,15,10]}}},maxSlots:{generic:3,forceWing:3,vehicle:3},minSlotsForEpic:2},window.OverlordMasteryData={unlockLevel:200,categories:{attack:{name:"Attack",id:"attack"},defense:{name:"Defense",id:"defense"}},skills:{attack:[{id:"allAttackI",name:"All Attack I",statId:"allAttackUp",maxLevel:5,tier:1,gridPosition:{row:1,col:1},opRequired:1,values:[2,4,6,8,10]},{id:"allAttackII",name:"All Attack II",statId:"allAttackUp",maxLevel:5,tier:2,gridPosition:{row:2,col:1},opRequired:2,values:[3,6,9,12,15]},{id:"allAttackIII",name:"All Attack III",statId:"allAttackUp",maxLevel:5,tier:4,gridPosition:{row:4,col:1},opRequired:3,values:[4,8,12,16,20]},{id:"critDmgI",name:"Critical DMG I",statId:"critDamage",maxLevel:5,tier:1,gridPosition:{row:1,col:2},opRequired:2,isPercentage:!0,values:[1,2,3,4,5]},{id:"critDmgII",name:"Critical DMG II",statId:"critDamage",maxLevel:3,tier:2,gridPosition:{row:2,col:2},opRequired:3,isPercentage:!0,values:[2,4,6]},{id:"ignoreResistCritRate",name:"Ignore Resist Critical Rate",statId:"ignoreResistCritRate",maxLevel:5,tier:3,gridPosition:{row:3,col:2},opRequired:4,values:[10,20,30,40,50]},{id:"attackRateI",name:"Attack Rate I",statId:"attackRate",maxLevel:5,tier:1,gridPosition:{row:1,col:3},opRequired:1,values:[20,40,60,80,100]},{id:"accuracyI",name:"Accuracy I",statId:"accuracy",maxLevel:5,tier:2,gridPosition:{row:2,col:3},opRequired:1,values:[40,60,80,100,120]},{id:"attackRateII",name:"Attack Rate II",statId:"attackRate",maxLevel:5,tier:3,gridPosition:{row:3,col:3},opRequired:2,values:[30,60,90,120,150]},{id:"accuracyII",name:"Accuracy II",statId:"accuracy",maxLevel:5,tier:4,gridPosition:{row:4,col:3},opRequired:5,values:[50,70,90,110,130]},{id:"addDmgI",name:"Add. DMG I",statId:"addDamage",maxLevel:5,tier:1,gridPosition:{row:1,col:4},opRequired:1,values:[2,4,6,8,10]},{id:"penetrationI",name:"Penetration I",statId:"penetration",maxLevel:5,tier:2,gridPosition:{row:2,col:4},opRequired:2,values:[2,4,6,8,10]},{id:"addDmgII",name:"Add. DMG II",statId:"addDamage",maxLevel:5,tier:3,gridPosition:{row:3,col:4},opRequired:5,values:[3,6,9,12,15]},{id:"penetrationII",name:"Penetration II",statId:"penetration",maxLevel:3,tier:4,gridPosition:{row:4,col:4},opRequired:10,values:[3,6,9]}],defense:[{id:"defenseI",name:"Defense I",statId:"defense",maxLevel:5,tier:1,gridPosition:{row:1,col:1},opRequired:1,values:[3,6,9,12,15]},{id:"dmgReduction",name:"DMG Reduction",statId:"damageReduce",maxLevel:5,tier:2,gridPosition:{row:2,col:1},opRequired:2,values:[5,10,15,20,25]},{id:"defenseII",name:"Defense II",statId:"defense",maxLevel:5,tier:4,gridPosition:{row:4,col:1},opRequired:3,values:[15,20,25,30,35]},{id:"hpUpI",name:"HP UP I",statId:"hp",maxLevel:5,tier:1,gridPosition:{row:1,col:2},opRequired:1,values:[20,40,60,80,100]},{id:"resistCritDmg",name:"Resist Critical DMG",statId:"resistCritDmg",maxLevel:5,tier:2,gridPosition:{row:2,col:2},opRequired:2,isPercentage:!0,values:[4,8,12,16,20]},{id:"hpUpII",name:"HP UP II",statId:"hp",maxLevel:5,tier:3,gridPosition:{row:3,col:2},opRequired:3,values:[20,40,60,80,100]},{id:"resistKnockback",name:"Resist Knockback",statId:"resistKnockback",maxLevel:5,tier:1,gridPosition:{row:1,col:3},opRequired:1,isPercentage:!0,values:[2,4,6,8,10]},{id:"resistDown",name:"Resist Down",statId:"resistDown",maxLevel:5,tier:2,gridPosition:{row:2,col:3},opRequired:2,isPercentage:!0,values:[2,4,6,8,10]},{id:"resistStun",name:"Resist Stun",statId:"resistStun",maxLevel:5,tier:3,gridPosition:{row:3,col:3},opRequired:3,isPercentage:!0,values:[2,4,6,8,10]},{id:"resistSkillAmp",name:"Resist Skill Amp.",statId:"resistSkillAmp",maxLevel:5,tier:4,gridPosition:{row:4,col:3},opRequired:5,isPercentage:!0,values:[3,6,9,12,15]},{id:"ignoreAccuracyI",name:"Ignore Accuracy I",statId:"ignoreAccuracy",maxLevel:5,tier:1,gridPosition:{row:1,col:4},opRequired:1,values:[40,60,80,100,120]},{id:"ignorePenetrationI",name:"Ignore Penetration I",statId:"ignorePenetration",maxLevel:5,tier:2,gridPosition:{row:2,col:4},opRequired:3,values:[5,10,15,20,25]},{id:"ignoreAccuracyII",name:"Ignore Accuracy II",statId:"ignoreAccuracy",maxLevel:5,tier:3,gridPosition:{row:3,col:4},opRequired:5,values:[50,70,90,110,130]},{id:"ignorePenetrationII",name:"Ignore Penetration II",statId:"ignorePenetration",maxLevel:3,tier:4,gridPosition:{row:4,col:4},opRequired:10,values:[5,10,15]}]},getSkillValueAtLevel:function(e,a){return!e||a<=0?0:(a>e.maxLevel&&(a=e.maxLevel),e.values[a-1]||0)},getAllSkills:function(){const e=[];for(const a in this.skills)e.push(...this.skills[a]);return e},getSkillById:function(e){return this.getAllSkills().find((a=>a.id===e))},getSkillsByCategory:function(e){return this.skills[e]||[]}},(()=>{const e={wingLevels:{normal:{min:1,max:100,name:"Normal"},rare:{min:101,max:200,name:"Rare Wing"},unique:{min:201,max:300,name:"Unique"},epic:{min:301,max:400,name:"Epic"},master:{min:401,max:500,name:"Master"},legend:{min:501,max:600,name:"Legend"}},slotConfigs:{0:{availableStats:[{statId:"pvpIgnorePenetration",maxLevel:5,values:[2,4,6,7,15]},{statId:"pvpDamageReduce",maxLevel:5,values:[2,16,18,22,30]},{statId:"pvpIgnorePenetration",maxLevel:5,values:[1,2,3,4,5]},{statId:"pveDamageReduce",maxLevel:5,values:[3,30,26,34,50]}]},1:{availableStats:[{statId:"pvpIgnorePenetration",maxLevel:5,values:[1,2,3,4,5]},{statId:"pveIgnorePenetration",maxLevel:5,values:[3,16,18,22,30]},{statId:"pvpDamageReduce",maxLevel:5,values:[2,16,22,34,52]},{statId:"pveDamageReduce",maxLevel:5,values:[20,21,31,45,80]}]},2:{availableStats:[{statId:"pveDefense",maxLevel:5,values:[10,30,42,54,75]},{statId:"hp",maxLevel:5,values:[25,120,150,180,250]}]},3:{availableStats:[{statId:"pveDefense",maxLevel:5,values:[10,30,42,54,75]},{statId:"hp",maxLevel:5,values:[16,180,210,250,350]}]},4:{availableStats:[{statId:"ignoreResistStun",maxLevel:4,values:[1,2,4,8]},{statId:"resistDown",maxLevel:4,values:[2,3,6,16]}]},5:{availableStats:[{statId:"resistStun",maxLevel:4,values:[1,2,4,8]},{statId:"resistCritRate",maxLevel:4,values:[1,2,75,14]}]},6:{availableStats:[{statId:"pvpNormalDamageUp",maxLevel:4,values:[1,2,3,6]},{statId:"pvpAddDamage",maxLevel:5,values:[3,8,12,20,35]},{statId:"pveNormalDamageUp",maxLevel:4,values:[2,4,6,12]},{statId:"pveAddDamage",maxLevel:5,values:[8,20,32,48,75]}]},7:{availableStats:[{statId:"pveNormalDamageUp",maxLevel:4,values:[1,2,3,6]},{statId:"pveAddDamage",maxLevel:5,values:[3,8,12,20,35]},{statId:"pvpNormalDamageUp",maxLevel:4,values:[2,4,6,12]},{statId:"pvpAddDamage",maxLevel:5,values:[8,20,32,48,75]}]},8:{availableStats:[{statId:"pvpAllAttackUp",maxLevel:5,values:[4,8,12,18,30]},{statId:"pvpAllSkillAmp",maxLevel:3,values:[1,2,4]},{statId:"pveAllAttackUp",maxLevel:5,values:[6,14,22,32,50]},{statId:"pveAllSkillAmp",maxLevel:3,values:[2,4,8]}]},9:{availableStats:[{statId:"pveAllAttackUp",maxLevel:5,values:[4,8,12,18,30]},{statId:"pveAllSkillAmp",maxLevel:3,values:[1,2,4]},{statId:"pvpAllAttackUp",maxLevel:5,values:[6,14,22,32,50]},{statId:"pvpAllSkillAmp",maxLevel:3,values:[2,4,8]}]},10:{availableStats:[{statId:"pvpPenetration",maxLevel:4,values:[2,5,9,15]},{statId:"pvpCritDamage",maxLevel:3,values:[1,4,8]},{statId:"pvePenetration",maxLevel:5,values:[3,7,12,16,25]},{statId:"pveCritDamage",maxLevel:4,values:[2,4,8,16]}]},11:{availableStats:[{statId:"pvePenetration",maxLevel:4,values:[2,5,9,15]},{statId:"pveCritDamage",maxLevel:3,values:[1,4,8]},{statId:"pvpPenetration",maxLevel:5,values:[3,7,12,16,25]},{statId:"pvpCritDamage",maxLevel:4,values:[2,4,8,16]}]}},getWingTierName:function(e){for(const[a,t]of Object.entries(this.wingLevels))if(e>=t.min&&e<=t.max)return t.name;return"Unknown"},isWingLevelImplemented:function(e){return e<=500},getSlotStats:function(e){return this.slotConfigs[e]?.availableStats||[]},getStatValue:function(e,a,t){const i=this.slotConfigs[e];if(!i)return 0;const n=i.availableStats.find((e=>e.statId===a));if(!n)return 0;const l=t-1;return l<0||l>=n.values.length?0:n.values[l]||0}};window.ForceWingData=e})(),window.AchievementData={categories:{quests:{name:"Quests",achievements:{}},dungeons:{name:"Dungeon",achievements:{"illusion-castle-underworld":{name:"Illusion Castle - Underworld",type:"milestone",milestones:[{threshold:100,stats:{defenseRate:2}},{threshold:500,stats:{defenseRate:4}},{threshold:1e3,stats:{defenseRate:6}},{threshold:5e3,stats:{defenseRate:8}}]},"illusion-castle-radiant":{name:"Illusion Castle - Radiant Hall",type:"milestone",milestones:[{threshold:100,stats:{attackRate:2}},{threshold:500,stats:{attackRate:4}},{threshold:1e3,stats:{attackRate:6}},{threshold:5e3,stats:{attackRate:8}}]},"illusion-castle-underworld-apocrypha":{name:"Illusion Castle - Underworld (Apocrypha)",type:"milestone",milestones:[{threshold:100,stats:{resistSuppression:1}},{threshold:500,stats:{resistSuppression:1}},{threshold:1e3,stats:{resistSuppression:1}},{threshold:5e3,stats:{resistSuppression:2}}]},"illusion-castle-radiant-apocrypha":{name:"Illusion Castle - Radiant Hall (Apocrypha)",type:"milestone",milestones:[{threshold:100,stats:{resistSilence:1}},{threshold:500,stats:{resistSilence:1}},{threshold:1e3,stats:{resistSilence:1}},{threshold:5e3,stats:{resistSilence:2}}]},"forgotten-temple-b1f":{name:"Forgotten Temple B1F",type:"milestone",milestones:[{threshold:100,stats:{hp:4}},{threshold:500,stats:{hp:6}},{threshold:1e3,stats:{hp:8}},{threshold:5e3,stats:{hp:12}}]},"forgotten-temple-b2f":{name:"Forgotten Temple B2F",type:"milestone",milestones:[{threshold:100,stats:{accuracy:5}},{threshold:500,stats:{accuracy:10}},{threshold:1e3,stats:{accuracy:15}},{threshold:5e3,stats:{accuracy:20}}]},"forgotten-temple-b2f-awakening":{name:"Forgotten Temple B2F (Awakening)",type:"milestone",milestones:[{threshold:100,stats:{resistCritDmg:2}},{threshold:500,stats:{resistCritDmg:2}},{threshold:1e3,stats:{resistCritDmg:2}},{threshold:5e3,stats:{resistCritDmg:2}}]},"forgotten-temple-b3f":{name:"Forgotten Temple B3F",type:"milestone",milestones:[{threshold:100,stats:{allAttackUp:4}},{threshold:500,stats:{allAttackUp:6,penetration:3}},{threshold:1e3,stats:{allAttackUp:8,penetration:5}},{threshold:5e3,stats:{allAttackUp:12,penetration:7}}]}}},pvp:{name:"PvP",achievements:{}},"mission-war":{name:"Mission War",achievements:{}},hunting:{name:"Hunting",achievements:{}},shared:{name:"Shared Achievements",achievements:{}},normal:{name:"Normal",achievements:{"tutorial-master":{name:"Tutorial Master",type:"single",stats:{exp:10,movementSpeed:5}},"world-explorer":{name:"World Explorer",type:"single",stats:{movementSpeed:10,exp:5}}}}},getCategoryAchievements:function(e){return this.categories[e]?this.categories[e].achievements:{}},getAchievementById:function(e){for(const a in this.categories){const t=this.categories[a].achievements;if(t[e])return{...t[e],id:e,category:a}}return null},getCategoryCounts:function(){const e={};for(const a in this.categories){const t=this.categories[a].achievements;e[a]={total:Object.keys(t).length,completed:0}}return e}},"undefined"!=typeof window&&(window.AchievementData=window.AchievementData||AchievementData);