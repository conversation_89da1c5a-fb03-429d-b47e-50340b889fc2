# Webpack Build Setup

This WordPress plugin now uses Webpack for bundling and minifying JavaScript and CSS files.

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Build for production:**
   ```bash
   npm run build
   ```

3. **Build for development:**
   ```bash
   npm run dev
   ```

4. **Watch mode (development):**
   ```bash
   npm run watch
   ```

## How It Works

### Always Bundled

The plugin **always uses bundled files** from the `dist/` folder for simplicity and consistency across all environments. This means:

- ✅ **Test environment**: Uses bundled files
- ✅ **Production environment**: Uses bundled files
- ✅ **No environment switching**: Same behavior everywhere

### Bundle Structure

The build process creates 5 main bundles:

1. **core.bundle.js/css** - Essential files loaded on every page
   - stats-config.js, stats-summary.js, build-planner-core.js
   - Core CSS files

2. **systems.bundle.js/css** - All system-specific files
   - All system JavaScript and CSS files

3. **data.bundle.js** - All data files
   - Equipment data, pet data, achievement data, etc.

4. **calculators.bundle.js** - Calculator modules
   - damage-calculator.js, cp-calculator.js

5. **build-management.bundle.js** - Build save/share functionality
   - LZ-string, compression, saver, sharer

### File Structure

```
├── js/                    # Source JavaScript files
├── css/                   # Source CSS files
├── dist/                  # Built/bundled files (committed to repo)
│   ├── js/
│   │   ├── core.bundle.js
│   │   ├── systems.bundle.js
│   │   ├── data.bundle.js
│   │   ├── calculators.bundle.js
│   │   └── build-management.bundle.js
│   ├── css/
│   │   ├── core.bundle.css
│   │   └── systems.bundle.css
│   └── assets/            # Copied asset files
├── package.json           # NPM dependencies and scripts
├── webpack.config.js      # Webpack configuration
└── .gitignore            # Excludes node_modules, includes dist/
```

## Deployment

The `dist/` folder is committed to your repository, so your hosting provider will receive the pre-built files. No build process is needed on the server.

## Development Workflow

1. Make changes to source files in `js/` or `css/`
2. Run `npm run build` to create production bundles
3. Test your changes (bundled files are always used)
4. Commit both source changes and updated `dist/` files
5. Push to repository for auto-deployment

## Performance Benefits

- **Reduced HTTP requests**: 5 bundles instead of 30+ individual files
- **Minification**: Smaller file sizes in production
- **Cache optimization**: Better browser caching with versioned bundles
- **Load order**: Proper dependency management between bundles

## Troubleshooting

- Always run `npm run build` after making any changes to source files
- Check browser console for JavaScript errors
- Verify all source files are included in webpack.config.js entry points
- Clear browser cache if changes don't appear
- Check that `dist/` folder contains the latest built files
