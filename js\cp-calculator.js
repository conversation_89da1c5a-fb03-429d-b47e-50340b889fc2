/**
 * CP Calculator
 * Calculates and displays Combat Power (CP) based on character stats
 * CP is a weighted sum of all stats that represents overall character strength
 */

const CPCalculator = {
    // Store for previous CP value
    previousCP: 0,

    // CP weights for each stat (how much CP 1 point of each stat gives)
    cpWeights: {
        // Offensive stats
        critDamage: 177,                // 1% Crit. Damage = 177
        resistCritDmg: 150,             // 1% Resist Crit. Damage = 150
        ignoreResistCritDmg: 142.5,     // 1% Ignore Resist Crit. Damage = 142.5
        allSkillAmp: 349,               // 1% All Skill AMP = 349
        resistSkillAmp: 296.5,          // 1% Resist Skill AMP = 296.5
        ignoreResistSkillAmp: 267,      // 1% Ignore Resist Skill AMP = 267
        penetration: 71,                // 1 Penetration = 71
        ignorePenetration: 53.1,        // 1 Ignore Penetration = 53.1
        cancelIgnorePenetration: 47.8,  // 1 Cancel Ignore Penetration = 47.8
        allAttackUp: 34.5,              // 1 All Attack = 34.5
        normalDamageUp: 86,             // 1% Normal Damage UP = 86
        defense: 21,                    // 1 Defense = 21
        hp: 5,                          // 1 HP = 5
        critRate: 750,                  // 1% Crit. Rate = 750
        resistCritRate: 636,            // 1% Resist Crit. Rate = 636
        ignoreResistCritRate: 574,      // 1% Ignore Resist Crit. Rate = 574
        attackRate: 3,                  // 1 Attack Rate = 3
        defenseRate: 2.4,               // 1 Defense Rate = 2.4
        damageReduce: 19.5,             // 1 Damage Reduction = 19.5
        ignoreDamageReduce: 16.8,       // 1 Ignore Damage Reduction = 16.8
        accuracy: 6.5,                  // 1 Accuracy = 6.5
        ignoreAccuracy: 5.3,            // 1 Ignore Accuracy = 5.3
        evasion: 5.3,                   // 1 Evasion = 5.3
        addDamage: 35,                  // 1 Add Damage = 35
        cancelIgnoreDamageReduce: 19.9, // 1 Cancel Ignore Damage Reduction = 19.9
        ignoreEvasion: 4.5,             // 1 Ignore Evasion = 4.5
        finalDamageIncreased: 1604,     // 1% Final Damage Increase = 1604
        finalDamageDecrease: 1451       // 1% Final Damage Decrease = 1451
    },

    // Initialize the calculator
    init: function() {
        // Create CP display element if it doesn't exist
        this.createCPDisplayIfNeeded();

        // Register with StatsSummary for stat updates
        if (typeof StatsSummary !== 'undefined') {
            // Store original method
            const originalUpdateTotalStats = StatsSummary.updateTotalStats;

            // Override with our version that triggers CP calculation
            StatsSummary.updateTotalStats = (totalStats) => {
                // Call original method first
                originalUpdateTotalStats.call(StatsSummary, totalStats);

                // Calculate and update CP using derived stats
                this.calculateAndUpdateCP(StatsSummary.derivedStats || totalStats);
            };

            // Check if we already have stats to calculate CP with
            if (StatsSummary.derivedStats && Object.keys(StatsSummary.derivedStats).length > 0) {
                setTimeout(() => {
                    this.calculateAndUpdateCP(StatsSummary.derivedStats);
                }, 500);
            } else if (StatsSummary.totalStats && Object.keys(StatsSummary.totalStats).length > 0) {
                setTimeout(() => {
                    this.calculateAndUpdateCP(StatsSummary.totalStats);
                }, 500);
            }
        }
    },

    // Create the CP display element
    createCPDisplayIfNeeded: function() {
        if (!document.getElementById('fg-cp-display')) {
            // Get the damage display container to add CP next to it
            const damageContainer = document.getElementById('fg-damage-display-container');
            
            if (damageContainer) {
                // Create CP display element
                const cpDisplay = document.createElement('div');
                cpDisplay.id = 'fg-cp-display';
                cpDisplay.className = 'fg-cp-display';
                
                cpDisplay.innerHTML = `
                    <span class="fg-cp-label">Combat Power:</span>
                    <span id="fg-cp-value" class="fg-cp-value">0</span>
                    <span id="fg-cp-change" class="fg-cp-change"></span>
                `;
                
                // Create a container for title and CP
                const titleContainer = document.createElement('div');
                titleContainer.className = 'fg-title-cp-container';
                
                // Get the damage title
                const damageTitle = damageContainer.querySelector('.fg-damage-title');
                
                if (damageTitle) {
                    // Move the title to our new container
                    damageTitle.parentNode.insertBefore(titleContainer, damageTitle);
                    titleContainer.appendChild(damageTitle);
                    
                    // Add CP display to the container
                    titleContainer.appendChild(cpDisplay);
                }
                
                // Add CSS for CP display
                this.addCPStyles();
            }
        }
    },

    // Add CSS styles for CP display
    addCPStyles: function() {
        const style = document.createElement('style');
        style.textContent = `
            .fg-title-cp-container {
                display: flex;
                align-items: center;
                flex: 1;
                text-align: left;
            }
            
            .fg-cp-display {
                display: flex;
                align-items: center;
                margin-left: 15px;
                padding: 4px 8px;
                background-color: rgba(0, 0, 0, 0.2);
                border-radius: 4px;
                white-space: nowrap;
            }
            
            .fg-cp-label {
                font-size: 12px;
                color: #aaa;
                margin-right: 6px;
            }
            
            .fg-cp-value {
                font-size: 16px;
                font-weight: bold;
                color: #FFD700; /* Golden color for CP */
                text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
            }
            
            .fg-cp-change {
                font-size: 10px;
                min-height: 12px;
                min-width: 40px;
                margin-left: 4px;
                text-align: left;
            }
            
            .fg-cp-increase {
                color: #4caf50;
            }
            
            .fg-cp-decrease {
                color: #f44336;
            }
            
            @keyframes cp-pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            .fg-cp-pulse {
                animation: cp-pulse 0.5s ease;
            }
        `;
        document.head.appendChild(style);
    },

    // Calculate CP based on stats
    calculateCP: function(stats) {
        // Make sure stats exists and is an object
        if (!stats || typeof stats !== 'object') {
            return 0;
        }

        let totalCP = 0;

        // Calculate CP for each stat based on weights
        for (const statName in stats) {
            const statValue = stats[statName];
            const cpWeight = this.cpWeights[statName];
            
            if (typeof statValue === 'number' && cpWeight) {
                totalCP += statValue * cpWeight;
            }
        }

        // Round to nearest integer
        return Math.round(totalCP);
    },

    // Calculate and update the CP display
    calculateAndUpdateCP: function(stats) {
        // Safety check for stats
        if (!stats || typeof stats !== 'object') {
            return;
        }

        // Calculate CP
        const cp = this.calculateCP(stats);

        // Get the CP display element
        const cpValueDisplay = document.getElementById('fg-cp-value');
        const cpChangeEl = document.getElementById('fg-cp-change');

        if (cpValueDisplay && cpChangeEl) {
            // Calculate change from previous CP
            const cpChange = cp - this.previousCP;

            // Format CP with thousands separators
            const formattedCP = this.formatNumber(cp);

            // Update the display
            cpValueDisplay.textContent = formattedCP;

            // Add/remove pulse animation
            cpValueDisplay.classList.remove('fg-cp-pulse');
            void cpValueDisplay.offsetWidth; // Force reflow to restart animation
            cpValueDisplay.classList.add('fg-cp-pulse');

            // Update change indicator if not the first calculation
            this.updateChangeIndicator(cpChangeEl, this.previousCP, cpChange);

            // Store current CP for next comparison
            this.previousCP = cp;

            // Store CP in BuildPlanner and StatsSummary for other systems to access
            if (typeof BuildPlanner !== 'undefined') {
                BuildPlanner.cp = cp;
            }

            if (typeof StatsSummary !== 'undefined') {
                StatsSummary.cp = cp;
            }
        }
    },

    // Helper to update change indicators
    updateChangeIndicator: function(changeElement, previousValue, changeValue) {
        if (previousValue > 0) {
            const absChangeValue = Math.abs(changeValue).toLocaleString(undefined, {maximumFractionDigits: 0});
            if (changeValue > 0) {
                changeElement.textContent = `+${absChangeValue}`;
                changeElement.className = 'fg-cp-change fg-cp-increase';
            } else if (changeValue < 0) {
                changeElement.textContent = `-${absChangeValue}`;
                changeElement.className = 'fg-cp-change fg-cp-decrease';
            } else {
                changeElement.textContent = '';
                changeElement.className = 'fg-cp-change';
            }
        } else {
            changeElement.textContent = '';
        }
    },

    // Helper to format numbers with commas for thousands
    formatNumber: function(number) {
        return Math.round(number).toLocaleString(undefined, {maximumFractionDigits: 0});
    }
};

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // Defer initialization slightly to ensure StatsSummary is loaded
    setTimeout(() => {
        CPCalculator.init();
    }, 600); // Initialize after DamageCalculator (which uses 500ms)
});