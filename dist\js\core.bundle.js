(()=>{(()=>{const e={iconBasePath:"assets/images/stat icons/",baseStats:{critRate:5,maxCritRate:50,critDamage:20},stats:{attack:{name:"Attack",icon:"attack_icon.png",category:"offensive",isPercentage:!1,description:"Increases physical damage",variants:["pvp","pve"]},magicAttack:{name:"Magic Attack",icon:"mattack_icon.png",category:"offensive",isPercentage:!1,description:"Increases magic damage",variants:["pvp","pve"]},attackRate:{name:"Attack Rate",icon:"attack_rate_icon.png",category:"offensive",isPercentage:!1,description:"Increases attack speed",variants:["pvp","pve"]},critRate:{name:"Critical Rate",icon:"critical_rate_icon.png",category:"offensive",isPercentage:!0,description:"Increases chance of landing critical hits",variants:["pvp","pve"]},maxCritRate:{name:"Max Crit. Rate",icon:"max_crit_rate_icon.png",category:"offensive",isPercentage:!0,description:"Maximum critical rate cap",variants:["pvp","pve"]},critDamage:{name:"Critical DMG",icon:"critical_damage_icon.png",category:"offensive",isPercentage:!0,description:"Increases critical hit damage",variants:["pvp","pve"]},swordSkillAmp:{name:"Sword Skill Amp.",icon:"sword_amp_icon.png",category:"offensive",isPercentage:!0,description:"Increases sword skill damage",variants:["pvp","pve"]},magicSkillAmp:{name:"Magic Skill Amp.",icon:"magic_amp_icon.png",category:"offensive",isPercentage:!0,description:"Increases magic skill damage",variants:["pvp","pve"]},accuracy:{name:"Accuracy",icon:"accuracy_icon.png",category:"offensive",isPercentage:!1,description:"Reduces chance of attacks being evaded",variants:["pvp","pve"]},penetration:{name:"Penetration",icon:"penetration_icon.png",category:"offensive",isPercentage:!1,description:"Bypasses a portion of enemy defense",variants:["pvp","pve"]},addDamage:{name:"Add. Damage",icon:"add_dmg_icon.png",category:"offensive",isPercentage:!1,description:"Flat damage added to attacks",variants:["pvp","pve"]},minDamage:{name:"Min Damage",icon:"min_dmg_icon.png",category:"offensive",isPercentage:!0,description:"Increases minimum damage",variants:["pvp","pve"]},ignoreEvasion:{name:"Ignore Evasion",icon:"ignore_evasion_icon.png",category:"offensive",isPercentage:!1,description:"Reduces chance of being evaded",variants:["pvp","pve"]},finalDamageIncreased:{name:"Final DMG Increased",icon:"final_dmg_increased_icon.png",category:"offensive",isPercentage:!0,description:"Increases final damage",variants:["pvp","pve"]},ignoreDamageReduce:{name:"Ignore DMG Reduction",icon:"ignore_damage_reduction_icon.png",category:"offensive",isPercentage:!1,description:"Bypasses enemy damage reduction",variants:["pvp","pve"]},ignoreResistCritRate:{name:"Ignore Resist Critical Rate",icon:"ignore_resist_crit_rate.png",category:"offensive",isPercentage:!0,description:"Bypasses enemy critical rate resistance",variants:["pvp","pve"]},ignoreResistCritDmg:{name:"Ignore Resist Critical DMG",icon:"ignore_resist_crit_dmg_icon.png",category:"offensive",isPercentage:!0,description:"Bypasses enemy critical damage resistance",variants:["pvp","pve"]},cancelIgnoreDamageReduce:{name:"Cancel Ignore Damage Reduction",icon:"cancel_ignore_damage_reduction_icon.png",category:"offensive",isPercentage:!1,description:"Reduces enemy damage reduction",variants:["pvp","pve"]},ignoreResistSkillAmp:{name:"Ignore Resist Skill Amp",icon:"ignore_resist_skill_amp_icon.png",category:"offensive",isPercentage:!0,description:"Bypasses enemy skill amplification resistance",variants:["pvp","pve"]},normalDamageUp:{name:"Normal DMG Up",icon:"normal_dmg_icon.png",category:"offensive",isPercentage:!0,description:"Increases normal attack damage",variants:["pvp","pve"]},cancelIgnorePenetration:{name:"Cancel Ignore Penetration",icon:"cancel_ignore_penetration_icon.png",category:"offensive",isPercentage:!1,description:"Reduces enemy penetration",variants:["pvp","pve"]},hp:{name:"HP",icon:"hp_icon.png",category:"defensive",isPercentage:!1,description:"Total health points"},defense:{name:"Defense",icon:"defense_icon.png",category:"defensive",isPercentage:!1,description:"Reduces damage taken",variants:["pvp","pve"]},defenseRate:{name:"Defense Rate",icon:"defense_rate_icon.png",category:"defensive",isPercentage:!1,description:"Increases damage reduction",variants:["pvp","pve"]},evasion:{name:"Evasion",icon:"evasion_icon.png",category:"defensive",isPercentage:!1,description:"Chance to evade attacks",variants:["pvp","pve"]},resistCritRate:{name:"Resist Critical Rate",icon:"resist_critical_rate_icon.png",category:"defensive",isPercentage:!0,description:"Reduces chance of receiving critical hits"},resistCritDmg:{name:"Resist Critical DMG",icon:"resist_crit_dmg_icon.png",category:"defensive",isPercentage:!0,description:"Reduces critical damage taken"},resistSkillAmp:{name:"Resist Skill Amp",icon:"resist_skill_amp_icon.png",category:"defensive",isPercentage:!0,description:"Reduces skill damage taken",variants:["pvp","pve"]},ignorePenetration:{name:"Ignore Penetration",icon:"ignore_penetration_icon.png",category:"defensive",isPercentage:!1,description:"Reduces enemy penetration",variants:["pvp","pve"]},ignoreAccuracy:{name:"Ignore Accuracy",icon:"ignore_accuracy_icon.png",category:"defensive",isPercentage:!1,description:"Increases chance to evade despite enemy accuracy"},damageReduce:{name:"DMG Reduction",icon:"dmg_reduction_icon.png",category:"defensive",isPercentage:!1,description:"Reduces all damage taken",variants:["pvp","pve"]},resistSuppression:{name:"Resist Suppression",icon:"resist_suppression_icon.png",category:"defensive",isPercentage:!0,description:"Reduces chance of being suppressed"},resistSilence:{name:"Resist Silence",icon:"resist_silence_icon.png",category:"defensive",isPercentage:!0,description:"Reduces chance of being silenced"},mp:{name:"MP",icon:"mana_icon.png",category:"utility",isPercentage:!1,description:"Total mana points"},hpAbsorb:{name:"HP Absorb",icon:"hp_absorb_icon.png",category:"utility",isPercentage:!0,description:"Amount of HP absorbed per hit"},maxHpSteal:{name:"HP Absorb Up",icon:"max_hp_absorb_icon.png",category:"utility",isPercentage:!0,description:"Increases HP absorption"},mpAbsorb:{name:"MP Absorb",icon:"mp_absorb_icon.png",category:"utility",isPercentage:!0,description:"Amount of MP absorbed per hit"},maxMpSteal:{name:"MP Absorb Up",icon:"max_mp_absorb_icon.png",category:"utility",isPercentage:!0,description:"Increases MP absorption"},hpAutoHeal:{name:"HP Auto Heal",icon:"hp_auto_heal_icon.png",category:"utility",isPercentage:!1,description:"HP regenerated over time"},mpAutoHeal:{name:"MP Auto Heal",icon:"mp_auto_heal_icon.png",category:"utility",isPercentage:!1,description:"MP regenerated over time"},exp:{name:"EXP",icon:"exp_icon.png",category:"utility",isPercentage:!0,description:"Increases experience gain"},skillExp:{name:"Skill EXP",icon:"skill_exp_icon.png",category:"utility",isPercentage:!0,description:"Increases skill experience gain"},partyExp:{name:"Party EXP",icon:"party_exp_icon.png",category:"utility",isPercentage:!0,description:"Increases party experience gain"},petExp:{name:"Pet EXP",icon:"skill_exp_icon.png",category:"utility",isPercentage:!0,description:"Increases pet experience gain"},alzDropAmount:{name:"Alz Drop Amount",icon:"alz_drop_amount_icon.png",category:"utility",isPercentage:!0,description:"Increases Alz drops"},alzDropRate:{name:"Alz Drop Rate",icon:"alz_drop_rate_icon.png",category:"utility",isPercentage:!0,description:"Increases chance of Alz drops"},alzBombChance:{name:"Alz Bomb Chance",icon:"alz_bomb_chance_icon.png",category:"utility",isPercentage:!0,description:"Increases chance of getting big Alz drops"},"2SlotDropRate":{name:"2-slot Drop Rate",icon:"2_slot_item_drop_icon.png",category:"utility",isPercentage:!0,description:"Increases 2-slot item drop rate"},resistUnableToMove:{name:"Resist Unable to Move",icon:"resist_unable_move_icon.png",category:"utility",isPercentage:!0,description:"Reduces chance of movement-impairing effects"},resistDown:{name:"Resist Down",icon:"resist_down_icon.png",category:"utility",isPercentage:!0,description:"Reduces chance of knockdown"},resistKnockback:{name:"Resist Knockback",icon:"resist_knock_back_icon.png",category:"utility",isPercentage:!0,description:"Reduces chance of being knocked back"},resistStun:{name:"Resist Stun",icon:"stun_resist_icon.png",category:"utility",isPercentage:!0,description:"Reduces chance of being stunned"},ignoreResistKnockback:{name:"Ignore Resist Knockback",icon:"ignore_resist_knochback_icon.png",category:"utility",isPercentage:!0,description:"Bypasses enemy knockback resistance"},ignoreResistDown:{name:"Ignore Resist Down",icon:"ignore_resist_down_icon.png",category:"utility",isPercentage:!0,description:"Bypasses enemy knockdown resistance"},ignoreResistStun:{name:"Ignore Resist Stun",icon:"ignore_resist_stun_icon.png",category:"utility",isPercentage:!0,description:"Bypasses enemy stun resistance"},auraDurationIncrease:{name:"Aura Duration Increase",icon:"aura_mode_increase_icon.png",category:"utility",isPercentage:!1,description:"Extends aura skill duration"},default:{name:"Unknown Stat",icon:"default_stat_icon.png",category:"utility",isPercentage:!1,description:"Unknown stat type"},allAttackUp:{name:"All Attack Up",icon:"all_atk_icon.png",category:"offensive",isPercentage:!1,description:"Increases all attack types",variants:["pvp","pve"]},allSkillAmp:{name:"All Skill Amp.",icon:"all_amp_icon.png",category:"offensive",isPercentage:!0,description:"Increases all skill damage",variants:["pvp","pve"]},str:{name:"STR",icon:"str_icon.png",category:"offensive",isPercentage:!1,description:"Strength stat"},int:{name:"INT",icon:"int_icon.png",category:"offensive",isPercentage:!1,description:"Intelligence stat"},dex:{name:"DEX",icon:"dex_icon.png",category:"offensive",isPercentage:!1,description:"Dexterity stat"}},getBaseStats:function(){return{...this.baseStats}},init:function(){"undefined"!=typeof forceguidesPlannerData&&forceguidesPlannerData.pluginUrl&&(this.pluginUrl=forceguidesPlannerData.pluginUrl)},getPluginUrl:function(){return this.pluginUrl||""},getStatIconUrl:function(e){const t=this.getStatInfo(e);return t?this.getPluginUrl()+this.iconBasePath+t.icon:this.getPluginUrl()+this.iconBasePath+this.stats.default.icon},formatStatValue:function(e,t){const n=this.getStatInfo(e);return n&&n.isPercentage?t+"%":t},getStatsByCategory:function(e){const t=[];for(const n in this.stats)this.stats[n].category===e&&(t.push(n),this.stats[n].variants&&this.stats[n].variants.forEach((e=>{t.push(e+n.charAt(0).toUpperCase()+n.slice(1))})));return t},getStatInfo:function(e){if(e.startsWith("pvp")||e.startsWith("pve")){const t=e.substring(0,3),n=e.substring(3,4).toLowerCase()+e.substring(4);if(this.stats[n]&&this.stats[n].variants&&this.stats[n].variants.includes(t))return{name:`${t.toUpperCase()} ${this.stats[n].name}`,icon:`${t}_${this.stats[n].icon}`,category:this.stats[n].category,isPercentage:this.stats[n].isPercentage,description:`${this.stats[n].description} against ${"pvp"===t?"players":"monsters"}`}}return this.stats[e]||this.stats.default}};document.addEventListener("DOMContentLoaded",(function(){e.init()}))})(),window.StatsSummary={totalStats:{},derivedStats:{},init:function(){const e=document.getElementById("atk-stats-list"),t=document.getElementById("def-stats-list"),n=document.getElementById("other-stats-list");if(!e||!t||!n)return;e.innerHTML="",t.innerHTML="",n.innerHTML="";const i='<div class="fg-stat-item">No stats yet</div>';e.innerHTML=i,t.innerHTML=i,n.innerHTML=i,this.setupTabEvents()},updateStats:function(e,t){BuildPlanner&&BuildPlanner.updateStats&&BuildPlanner.updateStats(e,t)},updateTotalStats:function(e){this.totalStats=e,this.calculateDerivedStats(),this.updateStatsSummaryUI(this.derivedStats)},calculateDerivedStats:function(){this.derivedStats={...this.totalStats},this.totalStats.allAttackUp&&("number"==typeof this.derivedStats.attack?this.derivedStats.attack+=this.totalStats.allAttackUp:this.derivedStats.attack=this.totalStats.allAttackUp,"number"==typeof this.derivedStats.magicAttack?this.derivedStats.magicAttack+=this.totalStats.allAttackUp:this.derivedStats.magicAttack=this.totalStats.allAttackUp);const e=(this.totalStats.allSkillAmp||0)+(this.totalStats.allAmp||0);e>0&&("number"==typeof this.derivedStats.swordSkillAmp?this.derivedStats.swordSkillAmp+=e:this.derivedStats.swordSkillAmp=e,"number"==typeof this.derivedStats.magicSkillAmp?this.derivedStats.magicSkillAmp+=e:this.derivedStats.magicSkillAmp=e)},setupTabEvents:function(){document.querySelectorAll(".fg-summary-tab").forEach((e=>{e.addEventListener("click",(()=>{const t=e.getAttribute("data-tab");this.setActiveStatsTab(t)}))}))},setActiveStatsTab:function(e){document.querySelectorAll(".fg-summary-tab").forEach((t=>{t.getAttribute("data-tab")===e?t.classList.add("active"):t.classList.remove("active")})),document.querySelectorAll(".fg-summary-tab-content").forEach((t=>{t.id===`${e}-tab`?t.classList.add("active"):t.classList.remove("active")}))},updateStatsSummaryUI:function(e){const t=document.getElementById("atk-stats-list"),n=document.getElementById("def-stats-list"),i=document.getElementById("other-stats-list");if(!t||!n||!i)return;t.innerHTML="",n.innerHTML="",i.innerHTML="";const s={offensive:{general:[],pvp:[],pve:[]},defensive:{general:[],pvp:[],pve:[]},utility:{general:[]}};"undefined"!=typeof StatsConfig&&Object.keys(StatsConfig.stats).forEach((e=>{const t=StatsConfig.stats[e];t&&"object"==typeof t&&"default"!==e&&t.category&&["offensive","defensive","utility"].includes(t.category)&&("offensive"===t.category?s.offensive.general.push({id:e,name:t.name}):"defensive"===t.category?s.defensive.general.push({id:e,name:t.name}):"utility"===t.category&&s.utility.general.push({id:e,name:t.name}),t.variants&&Array.isArray(t.variants)&&t.variants.forEach((n=>{const i=n+e.charAt(0).toUpperCase()+e.slice(1),a=`${n.toUpperCase()} ${t.name}`;"offensive"===t.category?"pvp"===n?s.offensive.pvp.push({id:i,name:a}):"pve"===n&&s.offensive.pve.push({id:i,name:a}):"defensive"===t.category&&("pvp"===n?s.defensive.pvp.push({id:i,name:a}):"pve"===n&&s.defensive.pve.push({id:i,name:a}))})))}));const a=(e,t)=>{const n=document.createElement("div");n.className="fg-stats-table";const i=document.createElement("div");i.className="fg-stats-column";const s=document.createElement("div");s.className="fg-stats-column";const a=document.createElement("div");a.className="fg-stats-column";const o=e=>{if(!e)return null;const n=t[e.id]||0;return this.createStatElement(e.name,this.formatStatValue(e.id,n),e.id)};return Array.isArray(e.general)&&e.general.forEach((e=>{const t=o(e);t&&i.appendChild(t)})),Array.isArray(e.pvp)&&e.pvp.forEach((e=>{const t=o(e);t&&s.appendChild(t)})),Array.isArray(e.pve)&&e.pve.forEach((e=>{const t=o(e);t&&a.appendChild(t)})),n.appendChild(i),n.appendChild(s),n.appendChild(a),n};t.appendChild(a(s.offensive,e)),n.appendChild(a(s.defensive,e)),i.appendChild(a(s.utility,e))},formatStatValue:function(e,t){return"critRate"===e?`${t}%/${this.derivedStats.maxCritRate||50}%`:StatsConfig.formatStatValue(e,t)},createStatElement:function(e,t,n){const i=document.createElement("div");return i.className="fg-stat-item",i.setAttribute("data-stat",n),i.innerHTML=`\n            <span class="fg-stat-name">${e}</span>\n            <span class="fg-stat-value">${t}</span>\n        `,i}},window.StatIntegrationService={createStatSummaryHTML:function(e){if(!e||0===Object.keys(e).length)return'<p class="no-stats">No stats selected yet.</p>';const t={offensive:[],defensive:[],utility:[]};for(const n in e){const i=e[n];if(0===i)continue;let s=n,a=!1,o="utility";if("undefined"!=typeof StatsConfig){const e=StatsConfig.getStatInfo(n);e&&(s=e.name,a=e.isPercentage,o=e.category||"utility")}t[o].push({id:n,name:s,value:i,isPercentage:a})}let n='<div class="fg-stat-summary">';const i={offensive:"Offensive",defensive:"Defensive",utility:"Utility"};for(const e in t){const s=t[e];0!==s.length&&(n+=`<div class="fg-stat-category">\n                <h4 class="fg-stat-category-title">${i[e]}</h4>\n                <div class="fg-stat-list">`,s.sort(((e,t)=>e.name.localeCompare(t.name))),s.forEach((e=>{let t="";if("undefined"!=typeof StatsConfig)try{t=`<img src="${StatsConfig.getStatIconUrl(e.id)}" alt="${e.name}" class="fg-stat-icon" onerror="this.style.display='none';">`}catch(e){}n+=`<div class="fg-stat-item" data-stat-id="${e.id}">\n                    <div class="fg-stat-info">\n                        ${t}\n                        <span class="fg-stat-name">${e.name}</span>\n                    </div>\n                    <span class="fg-stat-value">+${e.value}${e.isPercentage?"%":""}</span>\n                </div>`})),n+="</div></div>")}return n+="</div>",n},addSummaryStyles:function(){if(document.getElementById("fg-stat-summary-styles"))return;const e=document.createElement("style");e.id="fg-stat-summary-styles",e.textContent="\n            /* Stat Summary Styles */\n            .fg-stat-summary {\n                display: flex;\n                flex-direction: column;\n                gap: 15px;\n                width: 100%;\n            }\n            \n            .fg-stat-category {\n                background-color: rgba(20, 20, 30, 0.5);\n                border-radius: 6px;\n                padding: 10px;\n                border-left: 3px solid rgba(100, 100, 220, 0.6);\n            }\n            \n            .fg-stat-category-title {\n                margin: 0 0 10px 0;\n                font-size: 16px;\n                color: #eee;\n                border-bottom: 1px solid rgba(100, 100, 220, 0.3);\n                padding-bottom: 5px;\n            }\n            \n            .fg-stat-list {\n                display: flex;\n                flex-direction: column;\n                gap: 5px;\n            }\n            \n            .fg-stat-item {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                padding: 4px 8px;\n                border-radius: 4px;\n                background-color: rgba(40, 40, 50, 0.4);\n            }\n            \n            .fg-stat-info {\n                display: flex;\n                align-items: center;\n                gap: 8px;\n            }\n            \n            .fg-stat-icon {\n                width: 20px;\n                height: 20px;\n            }\n            \n            .fg-stat-name {\n                color: #d0d0d0;\n                font-size: 14px;\n            }\n            \n            .fg-stat-value {\n                color: #88ff88;\n                font-weight: bold;\n                font-size: 14px;\n            }\n            \n            /* Category-specific colors */\n            .fg-stat-category:nth-child(1) {\n                border-left-color: rgba(220, 100, 100, 0.6);\n            }\n            \n            .fg-stat-category:nth-child(1) .fg-stat-category-title {\n                border-bottom-color: rgba(220, 100, 100, 0.3);\n            }\n            \n            .fg-stat-category:nth-child(2) {\n                border-left-color: rgba(100, 180, 100, 0.6);\n            }\n            \n            .fg-stat-category:nth-child(2) .fg-stat-category-title {\n                border-bottom-color: rgba(100, 180, 100, 0.3);\n            }\n            \n            .fg-stat-category:nth-child(3) {\n                border-left-color: rgba(100, 120, 220, 0.6);\n            }\n            \n            .fg-stat-category:nth-child(3) .fg-stat-category-title {\n                border-bottom-color: rgba(100, 120, 220, 0.3);\n            }\n            \n            .no-stats {\n                color: #aaa;\n                font-style: italic;\n                text-align: center;\n                padding: 10px;\n            }\n        ",document.head.appendChild(e)}},document.addEventListener("DOMContentLoaded",(function(){StatIntegrationService.addSummaryStyles()})),window.SelectionWindowManager=class{constructor(e={}){this.options=Object.assign({id:"fg-selection-window-"+Date.now(),title:"Select an Option",className:"",fixedPosition:!0,onSelect:()=>{},onClose:()=>{}},e),this.isCreated=!1,this.isVisible=!1,this.elements={},this._createWindow()}_createWindow(){if(this.isCreated)return;const e=document.createElement("div");e.id=this.options.id,e.className=`fg-selection-window ${this.options.className}`,this.options.fixedPosition?e.classList.add("fg-selection-window-fixed"):e.classList.add("fg-selection-window-relative"),e.innerHTML=`\n            <div class="fg-selection-window-content">\n                <div class="fg-selection-window-header">\n                    <h3 class="fg-selection-window-title">${this.options.title}</h3>\n                    <button class="fg-selection-window-close">&times;</button>\n                </div>\n                <div class="fg-selection-window-body">\n                    <div class="fg-selection-window-options"></div>\n                </div>\n            </div>\n        `,document.body.appendChild(e),this.elements.window=e,this.elements.content=e.querySelector(".fg-selection-window-content"),this.elements.title=e.querySelector(".fg-selection-window-title"),this.elements.closeBtn=e.querySelector(".fg-selection-window-close"),this.elements.body=e.querySelector(".fg-selection-window-body"),this.elements.options=e.querySelector(".fg-selection-window-options"),this._setupEventListeners(),this.isCreated=!0}_setupEventListeners(){this.elements.closeBtn.addEventListener("click",(()=>{this.hide(),"function"==typeof this.options.onClose&&this.options.onClose()})),this.elements.window.addEventListener("click",(e=>{e.target===this.elements.window&&(this.hide(),"function"==typeof this.options.onClose&&this.options.onClose())})),this.elements.options.addEventListener("click",(e=>{const t=e.target.closest(".fg-selection-option");if(t){const e=this._getOptionData(t);if(t.classList.contains("fg-selection-category-header")||t.classList.contains("fg-selection-separator"))return;"function"==typeof this.options.onSelect&&this.options.onSelect(e),this.hide()}}))}_getOptionData(e){const t={};for(const n in e.dataset){try{t[n]=JSON.parse(e.dataset[n])}catch(i){t[n]=e.dataset[n]}if(n.toLowerCase()===n&&n.length>1){let e={statid:"statId",colorid:"colorId",typeid:"typeId",itemid:"itemId",nodeid:"nodeId",slotid:"slotId",rankid:"rankId",levelid:"levelId",actionid:"actionId"}[n]||n;e!==n&&(t[e]=t[n])}}return t}show(e={}){if(this.isCreated||this._createWindow(),e.title&&(this.elements.title.textContent=e.title),Array.isArray(e.options)){let t="";e.options.forEach((e=>{const n=Object.entries(e.data||{}).map((([e,t])=>`data-${e}="${String(t).replace(/"/g,"&quot;")}"`)).join(" ");(e.html||e.text)&&(t+=`\n                        <div class="fg-selection-option ${e.className||""}" ${n}>\n                            ${e.html||e.text||""}\n                        </div>\n                    `)})),this.elements.options.innerHTML=t}!this.options.fixedPosition&&e.targetElement?this._positionRelativeToElement(e.targetElement):e.position&&this._setPosition(e.position),this.elements.window.classList.add("active"),this.isVisible=!0}_positionRelativeToElement(e){const t=e.getBoundingClientRect(),n=window.innerWidth,i=window.innerHeight;let s=t.left+t.width/2;s=s/n*100;let a=t.top-10;a=a/i*100,this._setPosition({top:a,left:s})}_setPosition(e){void 0!==e.top&&(this.elements.content.style.top=`${e.top}%`),void 0!==e.left&&(this.elements.content.style.left=`${e.left}%`),this.elements.content.style.transform="translate(-50%, -50%)"}hide(){this.isCreated&&(this.elements.window.classList.remove("active"),this.isVisible=!1)}updateOptions(e){if(!this.isCreated)return;let t="";e.forEach((e=>{const n=Object.entries(e.data||{}).map((([e,t])=>`data-${e}="${t}"`)).join(" ");t+=`\n                <div class="fg-selection-option ${e.className||""}" ${n}>\n                    ${e.html||e.text||""}\n                </div>\n            `})),this.elements.options.innerHTML=t}updateTitle(e){this.isCreated&&(this.elements.title.textContent=e)}destroy(){this.isCreated&&(this.elements.window.remove(),this.isCreated=!1,this.isVisible=!1)}},(()=>{class e{constructor(e={}){if(this.options=Object.assign({container:null,buttons:[],position:"top",alignment:"left",className:""},e),this.isCreated=!1,this.elements={},this.buttonElements=[],!this.options.container)throw new Error("QuickFillButtonManager: container option is required");if(this.containerElement="string"==typeof this.options.container?document.querySelector(this.options.container):this.options.container,!this.containerElement)throw new Error("QuickFillButtonManager: container element not found");this._createButtonContainer()}_createButtonContainer(){if(this.isCreated)return;const e=document.createElement("div");e.className=`fg-quick-fill-container fg-quick-fill-${this.options.position} fg-quick-fill-align-${this.options.alignment}`,this.options.className&&e.classList.add(this.options.className),this.elements.container=e,this.options.buttons&&this.options.buttons.length>0&&this._createButtons(this.options.buttons),this._insertContainer(),this.isCreated=!0}_insertContainer(){switch(this.options.position){case"top":this.containerElement.insertBefore(this.elements.container,this.containerElement.firstChild);break;case"header":const e=this.containerElement.querySelector(".fg-system-header, .essence-runes-header, h2");e?e.appendChild(this.elements.container):this.containerElement.insertBefore(this.elements.container,this.containerElement.firstChild);break;default:this.containerElement.appendChild(this.elements.container)}}_createButtons(e){e.forEach(((e,t)=>{const n=this._createButton(e,t);this.elements.container.appendChild(n),this.buttonElements.push(n)}))}_createButton(e,t){const n=Object.assign({text:"Quick Fill",type:"primary",onClick:()=>{},tooltip:"",confirmMessage:null,disabled:!1},e),i=document.createElement("button");return i.className=`fg-quick-fill-button fg-quick-fill-button-${n.type}`,i.textContent=n.text,i.disabled=n.disabled,n.tooltip&&(i.title=n.tooltip),i.addEventListener("click",(()=>{this._handleButtonClick(n,i,t)})),i}_handleButtonClick(e,t,n){e.confirmMessage&&!confirm(e.confirmMessage)||"function"==typeof e.onClick&&e.onClick(t,n)}addButton(e){this.isCreated||this._createButtonContainer();const t=this._createButton(e,this.buttonElements.length);return this.elements.container.appendChild(t),this.buttonElements.push(t),t}removeButton(e){if(e>=0&&e<this.buttonElements.length){this.buttonElements[e].remove(),this.buttonElements.splice(e,1)}}updateButton(e,t){if(e>=0&&e<this.buttonElements.length){const n=this.buttonElements[e];void 0!==t.text&&(n.textContent=t.text),void 0!==t.disabled&&(n.disabled=t.disabled),void 0!==t.tooltip&&(n.title=t.tooltip),void 0!==t.type&&(n.className=n.className.replace(/fg-quick-fill-button-\w+/,`fg-quick-fill-button-${t.type}`))}}setEnabled(e){this.buttonElements.forEach((t=>{t.disabled=!e}))}setVisible(e){this.elements.container&&(this.elements.container.style.display=e?"":"none")}destroy(){this.isCreated&&this.elements.container&&(this.elements.container.remove(),this.isCreated=!1,this.buttonElements=[])}getButton(e){return this.buttonElements[e]||null}getAllButtons(){return[...this.buttonElements]}}window.QuickFillButtonManager=e,window.QuickFillButtonConfigs={pet:{position:"top",alignment:"left",buttons:[{text:"Quick Fill",tooltip:"Fill with endgame pet build (Normal: Max Crit Rate, Covenant/Trust: Penetration)",onClick:function(){window.PetSystem&&"function"==typeof window.PetSystem.quickFillEndgameSetup&&window.PetSystem.quickFillEndgameSetup()}},{text:"Reset All",type:"danger",tooltip:"Remove all equipped pet stats",confirmMessage:"Are you sure you want to remove all pet stats? This cannot be undone.",onClick:function(){window.PetSystem&&"function"==typeof window.PetSystem.resetAllStats&&window.PetSystem.resetAllStats()}}]},stellar:{position:"top",alignment:"left",buttons:[{text:"Quick Fill",tooltip:"Fill all nodes with popular PvE build (Penetration & Crit Damage focus)",onClick:function(){window.StellarSystem&&"function"==typeof window.StellarSystem.quickFillStellarSystem&&window.StellarSystem.quickFillStellarSystem()}},{text:"Reset All",type:"danger",tooltip:"Remove all stellar node stats",confirmMessage:"Are you sure you want to remove all stellar node stats? This cannot be undone.",onClick:function(){window.StellarSystem&&"function"==typeof window.StellarSystem.resetAllNodes&&window.StellarSystem.resetAllNodes()}}]},honor:{position:"top",alignment:"left",buttons:[{text:"Quick Fill",tooltip:"Fill all ranks with recommended stats (Dex, Crit Damage, All Attack Up, Penetration)",onClick:function(){window.HonorMedalSystem&&"function"==typeof window.HonorMedalSystem.quickFill&&window.HonorMedalSystem.quickFill()}},{text:"Reset All",type:"danger",tooltip:"Remove all equipped honor medal stats",confirmMessage:"Are you sure you want to remove all honor medal stats? This cannot be undone.",onClick:function(){window.HonorMedalSystem&&"function"==typeof window.HonorMedalSystem.resetAllStats&&window.HonorMedalSystem.resetAllStats()}}]},"essence-runes":{position:"top",alignment:"left",buttons:[{text:"Quick Fill",tooltip:"Add slots and equip/max all available essence runes",onClick:function(){window.EssenceRunesSystem&&"function"==typeof window.EssenceRunesSystem.maxAllRunes&&window.EssenceRunesSystem.maxAllRunes()}},{text:"Reset All",type:"danger",tooltip:"Remove all equipped essence runes",confirmMessage:"Are you sure you want to remove all essence runes? This cannot be undone.",onClick:function(){window.EssenceRunesSystem&&"function"==typeof window.EssenceRunesSystem.resetAllRunes&&window.EssenceRunesSystem.resetAllRunes()}}]},"karma-runes":{position:"top",alignment:"left",buttons:[{text:"Quick Fill",tooltip:"Add slots and equip/max all available karma runes",onClick:function(){window.KarmaRunesSystem&&"function"==typeof window.KarmaRunesSystem.maxAllRunes&&window.KarmaRunesSystem.maxAllRunes()}},{text:"Reset All",type:"danger",tooltip:"Remove all equipped karma runes",confirmMessage:"Are you sure you want to remove all karma runes? This cannot be undone.",onClick:function(){window.KarmaRunesSystem&&"function"==typeof window.KarmaRunesSystem.resetAllRunes&&window.KarmaRunesSystem.resetAllRunes()}}]},class:{position:"top",alignment:"left",buttons:[{text:"Reset All",type:"danger",tooltip:"Reset attributes to base values",confirmMessage:"Are you sure you want to reset all attributes to base values? This cannot be undone.",onClick:function(){window.ClassSystem&&"function"==typeof window.ClassSystem.resetAttributes&&window.ClassSystem.resetAttributes()}}]},"overlord-mastery":{position:"top",alignment:"left",buttons:[{text:"Quick Fill",tooltip:"Max all skills with balanced attack and defense build",onClick:function(){window.OverlordMasterySystem&&"function"==typeof window.OverlordMasterySystem.quickFillAllSkills&&window.OverlordMasterySystem.quickFillAllSkills()}},{text:"Reset All",type:"danger",tooltip:"Reset all overlord mastery skills to level 0",confirmMessage:"Are you sure you want to reset all overlord mastery skills? This cannot be undone.",onClick:function(){window.OverlordMasterySystem&&"function"==typeof window.OverlordMasterySystem.resetAllSkills&&window.OverlordMasterySystem.resetAllSkills()}}]},containerSelectors:{pet:".fg-pet-system-container",stellar:".fg-stellar-system-container",honor:".fg-honor-system-container","essence-runes":".essence-runes-container","karma-runes":".essence-runes-container",class:".fg-class-system-container","overlord-mastery":".fg-overlord-mastery-container"},getConfig:function(e){return this[e]||null},getContainerSelector:function(e){return this.containerSelectors[e]||null},createManager:function(t,n){const i=this.getConfig(t);if(!i)return null;try{return new e({container:n,position:i.position,alignment:i.alignment,buttons:i.buttons,className:`fg-quick-fill-${t}`})}catch(e){return null}},initializeSystem:function(e,t){const n=t||this.getContainerSelector(e);if(!n)return null;return this.createManager(e,n)}}})(),(()=>{const BuildPlanner={activeSystem:"class",ACTIVE_SYSTEM_STORAGE_KEY:"fg_active_system",systems:[{id:"class",name:"Stats"},{id:"pet",name:"Pet"},{id:"stellar",name:"Stellar Link"},{id:"honor",name:"Honor Medal"},{id:"equipment",name:"Equipment"},{id:"costumes",name:"Costumes"},{id:"gold-merit",name:"Gold Merit"},{id:"platinum-merit",name:"Platinum Merit"},{id:"force-wing",name:"Force Wing"},{id:"essence-runes",name:"Essence Runes"},{id:"karma-runes",name:"Karma Runes"},{id:"overlord-mastery",name:"Overlord Mastery"},{id:"achievement",name:"Achievement"}],stats:{class:{},pet:{},stellar:{},honor:{},"essence-runes":{},"karma-runes":{},equipment:{},costumes:{},"overlord-mastery":{},achievement:{}},loadedSystems:{},init:function(){this.loadActiveSystemFromStorage(),this.setupEventListeners(),"undefined"!=typeof StatsSummary&&StatsSummary.init(),this.updateActiveSystemButton(),this.showActiveSystem(),this.createLoaderStyles(),Promise.all(this.systems.map((e=>this.loadSystemScript(e.id).then((()=>this.initSystemIfNeeded(e.id))).catch((e=>{}))))).then((()=>{this.showActiveSystem(),this.refreshAllSystemStats()}))},createLoaderStyles:function(){const e=document.createElement("style");e.textContent="\n            #fg-system-loader {\n                position: fixed;\n                top: 0;\n                left: 0;\n                width: 100%;\n                height: 100%;\n                background: rgba(0,0,0,0.5);\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                z-index: 9999;\n            }\n            .fg-loader-spinner {\n                width: 50px;\n                height: 50px;\n                border: 5px solid rgba(255,255,255,0.3);\n                border-radius: 50%;\n                border-top-color: #fff;\n                animation: spin 1s ease infinite;\n            }\n            @keyframes spin {\n                to { transform: rotate(360deg); }\n            }\n        ",document.head.appendChild(e)},showLoadingIndicator:function(e){let t=document.getElementById("fg-system-loader");!t&&e&&(t=document.createElement("div"),t.id="fg-system-loader",t.innerHTML='<div class="fg-loader-spinner"></div>',document.querySelector(".fg-build-planner-container").appendChild(t)),t&&(t.style.display=e?"flex":"none")},loadSystemScript:function(e){return new Promise(((t,n)=>{if(this.loadedSystems[e])return t();const i=forceguidesPlannerData.systemJsUrls[e];if(!i)return t();const s=forceguidesPlannerData.systemCssUrls[e];if(s){const e=document.createElement("link");e.rel="stylesheet",e.href=s,document.head.appendChild(e)}else if("karma-runes"===e){const e=forceguidesPlannerData.systemCssUrls["essence-runes"];if(e){const t=document.createElement("link");t.rel="stylesheet",t.href=e,document.head.appendChild(t)}}const a=document.createElement("script");a.src=i,a.async=!0,a.onload=()=>{setTimeout((()=>{const n=this.getSystemObjectName(e),i=document.createElement("script");i.textContent=`\n                        // Ensure the ${n} exists as a global object\n                        if (typeof ${n} !== 'undefined' && typeof window.${n} === 'undefined') {\n                            window.${n} = ${n};\n                        }\n                    `,document.head.appendChild(i),this.loadedSystems[e]=!0,t()}),100)},a.onerror=e=>{n(e)},document.body.appendChild(a)}))},setupEventListeners:function(){document.querySelectorAll(".fg-system-button:not(.disabled)").forEach((e=>{e.addEventListener("click",(()=>{const t=e.getAttribute("data-system");this.setActiveSystem(t)}))}))},setActiveSystem:function(e){this.activeSystem!==e&&(this.showLoadingIndicator(!0),this.loadSystemScript(e).then((()=>{this.activeSystem=e,this.saveActiveSystemToStorage(),this.updateActiveSystemButton(),this.showActiveSystem(),this.initSystemIfNeeded(e),this.showLoadingIndicator(!1)})).catch((e=>{this.showLoadingIndicator(!1)})))},initSystemIfNeeded:function(e){const t=`init${this.capitalizeFirstLetter(e)}System`;"function"==typeof this[t]?this[t]():this.initGenericSystem(e)},initGenericSystem:function(systemId){const systemObjectName=this.getSystemObjectName(systemId);if(window[systemObjectName]&&!0===window[systemObjectName].isInitialized)return!0;if(window[systemObjectName]&&"function"==typeof window[systemObjectName].init)return window[systemObjectName].init(),!0;if(void 0!==eval(systemObjectName)&&void 0===window[systemObjectName])try{if(window[systemObjectName]=eval(systemObjectName),"function"==typeof window[systemObjectName].init)return window[systemObjectName].init(),!0}catch(e){return!1}return!1},capitalizeFirstLetter:function(e){return e.replace(/-([a-z])/g,(function(e){return e[1].toUpperCase()})).replace(/^([a-z])/,(function(e){return e.toUpperCase()}))},getSystemObjectName:function(e){return"honor"===e?"HonorMedalSystem":this.capitalizeFirstLetter(e)+"System"},showActiveSystem:function(){document.querySelectorAll(".fg-system-panel").forEach((e=>{e.id===`fg-${this.activeSystem}-system`?e.classList.add("active"):e.classList.remove("active")}))},updateStats:function(e,t){this.stats[e]=t,this.calculateTotalStats()},updateSystemStats:function(e,t){this.stats[e]=t,this.calculateTotalStats()},calculateTotalStats:function(){const e={};if(Object.values(this.stats).forEach((t=>{Object.entries(t).forEach((([t,n])=>{e[t]?e[t]+=n:e[t]=n}))})),"undefined"!=typeof StatsConfig){const t=StatsConfig.getBaseStats();Object.entries(t).forEach((([t,n])=>{e[t]?e[t]+=n:e[t]=n}))}"undefined"!=typeof StatsSummary&&StatsSummary.updateTotalStats(e)},exportBuild:function(){const e={stats:this.stats,timestamp:(new Date).toISOString(),version:"1.0"};return JSON.stringify(e)},importBuild:function(e){try{const t=JSON.parse(e);if(!t.stats)throw new Error("Invalid build data: missing stats");return this.systems.forEach((e=>{t.stats[e.id]&&(this.stats[e.id]=t.stats[e.id])})),this.calculateTotalStats(),this.showActiveSystem(),!0}catch(e){return!1}},initPetSystem:function(){if(window.PetSystem&&!0===window.PetSystem.isInitialized)return!0;const e=()=>!(!window.PetSystem||"function"!=typeof window.PetSystem.init)&&(window.PetSystem.init(),!0);if(e())return;if("undefined"!=typeof PetSystem&&void 0===window.PetSystem&&(window.PetSystem=PetSystem,e()))return;const t=forceguidesPlannerData.systemJsUrls.pet;if(t){const n=document.createElement("script");n.src=t+"&forcereload=true",n.async=!1,n.onload=()=>{setTimeout((()=>{e()}),200)},document.body.appendChild(n)}},loadActiveSystemFromStorage:function(){try{const e=localStorage.getItem(this.ACTIVE_SYSTEM_STORAGE_KEY);if(e){this.systems.some((t=>t.id===e))?this.activeSystem=e:localStorage.removeItem(this.ACTIVE_SYSTEM_STORAGE_KEY)}}catch(e){try{localStorage.removeItem(this.ACTIVE_SYSTEM_STORAGE_KEY)}catch(e){}}},saveActiveSystemToStorage:function(){try{localStorage.setItem(this.ACTIVE_SYSTEM_STORAGE_KEY,this.activeSystem)}catch(e){}},updateActiveSystemButton:function(){document.querySelectorAll(".fg-system-button").forEach((e=>{e.getAttribute("data-system")===this.activeSystem?e.classList.add("active"):e.classList.remove("active")}))},initStellarSystem:function(){window.StellarSystem||(window.StellarSystem={isInitialized:!1,init:function(){this.isInitialized=!0},updateStats:function(){}}),!window.StellarSystem.isInitialized&&window.StellarSystem.init&&window.StellarSystem.init()},initEquipmentSystem:function(){window.EquipmentSystem&&!window.EquipmentSystem.isInitialized&&window.EquipmentSystem.init&&window.EquipmentSystem.init()},refreshAllSystemStats:function(){const e=this;setTimeout((()=>{e.systems.forEach((t=>{const n=t.id,i=e.getSystemObjectName(n);e.loadedSystems[n]&&window[i]&&"function"==typeof window[i].updateStats&&window[i].updateStats()})),e.calculateTotalStats()}),500)}};document.addEventListener("DOMContentLoaded",(()=>{document.querySelector(".fg-build-planner-container")&&BuildPlanner.init()}))})()})();