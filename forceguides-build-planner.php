<?php
/*
Plugin Name: Nipperlug Build Planner
Description: Character build planner for Force Guides
Version: 1.0
Author: ForceGuides Team
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Helper function to get file version based on modification time
function fg_get_file_version($file_path) {
    if (file_exists($file_path)) {
        return filemtime($file_path);
    }
    return time(); // Fallback to current time
}



// Enqueue scripts and styles
function forceguides_build_planner_enqueue_scripts() {
    // Only load scripts on pages that have our shortcode
    global $post;

    // Skip if not on a page/post or if post content doesn't have our shortcode
    if (!is_singular() || !$post ||
        (!has_shortcode($post->post_content, 'nipperlug_build_planner') &&
         !has_shortcode($post->post_content, 'forceguides_build_planner'))) {
        return;
    }

    // Always use bundled files for simplicity and consistency
    forceguides_enqueue_bundled_assets();
}

// Enqueue bundled assets
function forceguides_enqueue_bundled_assets() {
    global $post;

    $dist_path = plugin_dir_path(__FILE__) . 'dist/';
    $dist_url = plugin_dir_url(__FILE__) . 'dist/';

    // Get file versions for cache busting
    $core_js_version = file_exists($dist_path . 'js/core.bundle.js') ? filemtime($dist_path . 'js/core.bundle.js') : '1.0.0';
    $core_css_version = file_exists($dist_path . 'css/core.bundle.css') ? filemtime($dist_path . 'css/core.bundle.css') : '1.0.0';
    $systems_js_version = file_exists($dist_path . 'js/systems.bundle.js') ? filemtime($dist_path . 'js/systems.bundle.js') : '1.0.0';
    $systems_css_version = file_exists($dist_path . 'css/systems.bundle.css') ? filemtime($dist_path . 'css/systems.bundle.css') : '1.0.0';
    $data_version = file_exists($dist_path . 'js/data.bundle.js') ? filemtime($dist_path . 'js/data.bundle.js') : '1.0.0';
    $calculators_version = file_exists($dist_path . 'js/calculators.bundle.js') ? filemtime($dist_path . 'js/calculators.bundle.js') : '1.0.0';
    $build_mgmt_version = file_exists($dist_path . 'js/build-management.bundle.js') ? filemtime($dist_path . 'js/build-management.bundle.js') : '1.0.0';

    // Enqueue core CSS bundle
    wp_enqueue_style('forceguides-core-bundle-css',
        $dist_url . 'css/core.bundle.css',
        array(), $core_css_version);

    // Enqueue systems CSS bundle
    wp_enqueue_style('forceguides-systems-bundle-css',
        $dist_url . 'css/systems.bundle.css',
        array('forceguides-core-bundle-css'), $systems_css_version);

    // Enqueue core JS bundle (includes stats-config, stats-summary, etc.)
    wp_enqueue_script('forceguides-core-bundle',
        $dist_url . 'js/core.bundle.js',
        array('jquery'), $core_js_version, true);

    // Enqueue data bundle (all data files)
    wp_enqueue_script('forceguides-data-bundle',
        $dist_url . 'js/data.bundle.js',
        array('jquery', 'forceguides-core-bundle'), $data_version, true);

    // Enqueue calculators bundle
    wp_enqueue_script('forceguides-calculators-bundle',
        $dist_url . 'js/calculators.bundle.js',
        array('jquery', 'forceguides-core-bundle'), $calculators_version, true);

    // Enqueue build management bundle (compression, saver, sharer)
    wp_enqueue_script('forceguides-build-management-bundle',
        $dist_url . 'js/build-management.bundle.js',
        array('jquery', 'forceguides-core-bundle'), $build_mgmt_version, true);

    // Enqueue systems bundle (all system files)
    wp_enqueue_script('forceguides-systems-bundle',
        $dist_url . 'js/systems.bundle.js',
        array('jquery', 'forceguides-core-bundle', 'forceguides-data-bundle'), $systems_js_version, true);

    // Pass data to JavaScript
    wp_localize_script('forceguides-core-bundle', 'forceguidesPlannerData', array(
        'pluginUrl' => plugin_dir_url(__FILE__),
        'uploadsUrl' => wp_upload_dir()['baseurl'],
        'iconBase' => site_url('/wp-content/uploads/2025/03/'),
        'debug' => defined('WP_DEBUG') && WP_DEBUG,
        'buildPlannerPageUrl' => get_permalink($post->ID)
    ));
}


add_action('wp_enqueue_scripts', 'forceguides_build_planner_enqueue_scripts');

// Add shortcode
function forceguides_build_planner_shortcode() {
    ob_start();
    include plugin_dir_path(__FILE__) . 'templates/build-planner.php';
    return ob_get_clean();
}

// Register shortcodes - put nipperlug first as it's likely causing the issue
add_shortcode('nipperlug_build_planner', 'forceguides_build_planner_shortcode');
add_shortcode('forceguides_build_planner', 'forceguides_build_planner_shortcode');