<?php
/*
Plugin Name: Nipperlug Build Planner
Description: Character build planner for Force Guides
Version: 1.0
Author: ForceGuides Team
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Helper function to get file version based on modification time
function fg_get_file_version($file_path) {
    if (file_exists($file_path)) {
        return filemtime($file_path);
    }
    return time(); // Fallback to current time
}

// Helper function to register a new system
function fg_register_system($system_id, &$js_urls_array, &$css_urls_array = null) {
    // Map of systems to their custom directories
    $system_paths = [
        'pet' => 'pet-system',
        'stellar' => 'stellar',
        'honor' => 'honor-medal-system',
        'essence-runes' => 'essence-rune-system',
        'karma-runes' => 'karma-rune-system',
        'equipment' => 'equipment-system',
        'costumes' => 'costume-system',
        'overlord-mastery' => 'overlord-mastery',
        'force-wing' => 'force-wing-system',
        'achievement' => 'achievement-system'
    ];

    // Determine the correct JS path based on the system mapping
    $js_dir = isset($system_paths[$system_id]) ? $system_paths[$system_id] : 'systems';
    $js_path = plugin_dir_path(__FILE__) . "js/{$js_dir}/{$system_id}-system.js";
    $css_path = plugin_dir_path(__FILE__) . "css/{$system_id}-system.css";

    // Register JS with proper versioning
    $js_urls_array[$system_id] = plugin_dir_url(__FILE__) . "js/{$js_dir}/{$system_id}-system.js?ver=" . fg_get_file_version($js_path);

    // Register CSS if it exists and array was provided
    if ($css_urls_array !== null && file_exists($css_path)) {
        $css_urls_array[$system_id] = plugin_dir_url(__FILE__) . "css/{$system_id}-system.css?ver=" . fg_get_file_version($css_path);
    }
}

// Enqueue scripts and styles
function forceguides_build_planner_enqueue_scripts() {
    // Only load scripts on pages that have our shortcode
    global $post;

    // Skip if not on a page/post or if post content doesn't have our shortcode
    if (!is_singular() || !$post ||
        (!has_shortcode($post->post_content, 'nipperlug_build_planner') &&
         !has_shortcode($post->post_content, 'forceguides_build_planner'))) {
        return;
    }

    // Use file modification time for cache busting instead of time()
    $core_js_path = plugin_dir_path(__FILE__) . 'js/build-planner-core.js';
    $core_version = file_exists($core_js_path) ? filemtime($core_js_path) : '1.0.0';

    // Enqueue core CSS
    wp_enqueue_style('forceguides-build-planner-css',
        plugin_dir_url(__FILE__) . 'css/build-planner.css',
        array(), $core_version);

    // Enqueue selection window CSS
    $selection_window_css_path = plugin_dir_path(__FILE__) . 'css/selection-window.css';
    $selection_window_css_version = file_exists($selection_window_css_path) ? filemtime($selection_window_css_path) : $core_version;
    wp_enqueue_style('forceguides-selection-window-css',
        plugin_dir_url(__FILE__) . 'css/selection-window.css',
        array('forceguides-build-planner-css'), $selection_window_css_version);

    // Enqueue quick fill button CSS
    $quick_fill_css_path = plugin_dir_path(__FILE__) . 'css/common/quick-fill-buttons.css';
    $quick_fill_css_version = file_exists($quick_fill_css_path) ? filemtime($quick_fill_css_path) : $core_version;
    wp_enqueue_style('forceguides-quick-fill-buttons-css',
        plugin_dir_url(__FILE__) . 'css/common/quick-fill-buttons.css',
        array('forceguides-build-planner-css'), $quick_fill_css_version);

    // Enqueue only core JS files
    wp_enqueue_script('forceguides-stats-config',
        plugin_dir_url(__FILE__) . 'js/stats-config.js',
        array('jquery'), $core_version, true);

    // Enqueue stats summary module
    $stats_summary_path = plugin_dir_path(__FILE__) . 'js/stats-summary.js';
    $stats_summary_version = file_exists($stats_summary_path) ? filemtime($stats_summary_path) : $core_version;
    wp_enqueue_script('forceguides-stats-summary',
        plugin_dir_url(__FILE__) . 'js/stats-summary.js',
        array('jquery', 'forceguides-stats-config'),
        $stats_summary_version, true);

    // Enqueue stat integration service
    $stat_integration_path = plugin_dir_path(__FILE__) . 'js/utils/stat-integration-service.js';
    $stat_integration_version = file_exists($stat_integration_path) ? filemtime($stat_integration_path) : $core_version;
    wp_enqueue_script('forceguides-stat-integration',
        plugin_dir_url(__FILE__) . 'js/utils/stat-integration-service.js',
        array('jquery', 'forceguides-stats-config'),
        $stat_integration_version, true);

    // Enqueue selection window manager
    $selection_window_path = plugin_dir_path(__FILE__) . 'js/common/selection-window-manager.js';
    $selection_window_version = file_exists($selection_window_path) ? filemtime($selection_window_path) : $core_version;
    wp_enqueue_script('forceguides-selection-window',
        plugin_dir_url(__FILE__) . 'js/common/selection-window-manager.js',
        array('jquery'),
        $selection_window_version, true);

    // Enqueue quick fill button manager (includes configurations)
    $quick_fill_path = plugin_dir_path(__FILE__) . 'js/common/quick-fill-button-manager.js';
    $quick_fill_version = file_exists($quick_fill_path) ? filemtime($quick_fill_path) : $core_version;
    wp_enqueue_script('forceguides-quick-fill-buttons',
        plugin_dir_url(__FILE__) . 'js/common/quick-fill-button-manager.js',
        array('jquery'),
        $quick_fill_version, true);

    wp_enqueue_script('forceguides-build-planner-core',
        plugin_dir_url(__FILE__) . 'js/build-planner-core.js',
        array('jquery', 'forceguides-stats-config', 'forceguides-stats-summary', 'forceguides-stat-integration', 'forceguides-selection-window', 'forceguides-quick-fill-buttons'),
        $core_version, true);

    // Enqueue Damage calculator script - load after core but before build saver
    $damage_calculator_path = plugin_dir_path(__FILE__) . 'js/damage-calculator.js';
    $damage_calculator_version = file_exists($damage_calculator_path) ? filemtime($damage_calculator_path) : time();
    wp_enqueue_script('forceguides-damage-calculator',
        plugin_dir_url(__FILE__) . 'js/damage-calculator.js',
        array('jquery', 'forceguides-build-planner-core', 'forceguides-stats-config'),
        $damage_calculator_version, true);

    // Enqueue CP calculator script - load after damage calculator
    $cp_calculator_path = plugin_dir_path(__FILE__) . 'js/cp-calculator.js';
    $cp_calculator_version = file_exists($cp_calculator_path) ? filemtime($cp_calculator_path) : time();
    wp_enqueue_script('forceguides-cp-calculator',
        plugin_dir_url(__FILE__) . 'js/cp-calculator.js',
        array('jquery', 'forceguides-build-planner-core', 'forceguides-stats-config', 'forceguides-damage-calculator'),
        $cp_calculator_version, true);
            
    // Enqueue build saver script - ensure it loads AFTER core scripts
    $build_saver_path = plugin_dir_path(__FILE__) . 'js/build-saver.js';
    $build_saver_version = file_exists($build_saver_path) ? filemtime($build_saver_path) : time(); // Force reload on each page load
    wp_enqueue_script('forceguides-build-saver',
        plugin_dir_url(__FILE__) . 'js/build-saver.js',
        array('jquery', 'forceguides-build-planner-core', 'forceguides-stats-config', 'forceguides-damage-calculator'),
        $build_saver_version, true);

    // Enqueue LZString compression library
    $lzstring_path = plugin_dir_path(__FILE__) . 'js/libs/lz-string.min.js';
    $lzstring_version = file_exists($lzstring_path) ? filemtime($lzstring_path) : time();
    wp_enqueue_script('forceguides-lzstring',
        plugin_dir_url(__FILE__) . 'js/libs/lz-string.min.js',
        array(),
        $lzstring_version, true);

    // Enqueue build compression script - load before build sharer
    $build_compression_path = plugin_dir_path(__FILE__) . 'js/build-compression.js';
    $build_compression_version = file_exists($build_compression_path) ? filemtime($build_compression_path) : time();
    wp_enqueue_script('forceguides-build-compression',
        plugin_dir_url(__FILE__) . 'js/build-compression.js',
        array('jquery', 'forceguides-build-planner-core', 'forceguides-build-saver', 'forceguides-lzstring'),
        $build_compression_version, true);

    // Enqueue build sharer script - load after build compression
    $build_sharer_path = plugin_dir_path(__FILE__) . 'js/build-sharer.js';
    $build_sharer_version = file_exists($build_sharer_path) ? filemtime($build_sharer_path) : time();
    wp_enqueue_script('forceguides-build-sharer',
        plugin_dir_url(__FILE__) . 'js/build-sharer.js',
        array('jquery', 'forceguides-build-planner-core', 'forceguides-build-saver', 'forceguides-lzstring', 'forceguides-build-compression'),
        $build_sharer_version, true);

    // Initialize arrays for system URLs
    $system_js_urls = array();
    $system_css_urls = array();

    // Register all systems
    fg_register_system('class', $system_js_urls, $system_css_urls);
    fg_register_system('pet', $system_js_urls, $system_css_urls);
    fg_register_system('stellar', $system_js_urls, $system_css_urls);
    fg_register_system('honor', $system_js_urls, $system_css_urls);
    fg_register_system('essence-runes', $system_js_urls, $system_css_urls);
    fg_register_system('karma-runes', $system_js_urls, $system_css_urls);
    fg_register_system('equipment', $system_js_urls, $system_css_urls);
    fg_register_system('costumes', $system_js_urls, $system_css_urls);
    fg_register_system('overlord-mastery', $system_js_urls, $system_css_urls);
    fg_register_system('force-wing', $system_js_urls, $system_css_urls);
    fg_register_system('achievement', $system_js_urls, $system_css_urls);
    // Add more systems here as they are developed

    // Load data files for specific systems that need them
    // Equipment data - comprehensive data structure
    $equipment_data_path = plugin_dir_path(__FILE__) . 'js/equipment-system/equipment-data/equipment-data.js';
    $equipment_data_version = file_exists($equipment_data_path) ? filemtime($equipment_data_path) : time();

    wp_enqueue_script('forceguides-equipment-data',
        plugin_dir_url(__FILE__) . 'js/equipment-system/equipment-data/equipment-data.js',
        array('jquery', 'forceguides-stats-config'),
        $equipment_data_version, true);

    // Weapons data - weapon specific data and upgrades
    $weapons_data_path = plugin_dir_path(__FILE__) . 'js/equipment-system/equipment-data/weapons-data.js';
    $weapons_data_version = file_exists($weapons_data_path) ? filemtime($weapons_data_path) : time();

    wp_enqueue_script('forceguides-weapons-data',
        plugin_dir_url(__FILE__) . 'js/equipment-system/equipment-data/weapons-data.js',
        array('jquery', 'forceguides-equipment-data'),
        $weapons_data_version, true);

    // Belts data - belt specific data and upgrades
    $belts_data_path = plugin_dir_path(__FILE__) . 'js/equipment-system/equipment-data/belts-data.js';
    $belts_data_version = file_exists($belts_data_path) ? filemtime($belts_data_path) : time();

    wp_enqueue_script('forceguides-belts-data',
        plugin_dir_url(__FILE__) . 'js/equipment-system/equipment-data/belts-data.js',
        array('jquery', 'forceguides-equipment-data', 'forceguides-weapons-data'),
        $belts_data_version, true);

    // Rings data - ring specific data
    $rings_data_path = plugin_dir_path(__FILE__) . 'js/equipment-system/equipment-data/rings-data.js';
    $rings_data_version = file_exists($rings_data_path) ? filemtime($rings_data_path) : time();

    wp_enqueue_script('forceguides-rings-data',
        plugin_dir_url(__FILE__) . 'js/equipment-system/equipment-data/rings-data.js',
        array('jquery', 'forceguides-equipment-data', 'forceguides-weapons-data'),
        $rings_data_version, true);

    // Equipment detail view
    $equipment_detail_path = plugin_dir_path(__FILE__) . 'js/equipment-system/equipment-detail-view.js';
    $equipment_detail_version = file_exists($equipment_detail_path) ? filemtime($equipment_detail_path) : time();

    wp_enqueue_script('forceguides-equipment-detail',
        plugin_dir_url(__FILE__) . 'js/equipment-system/equipment-detail-view.js',
        array('jquery', 'forceguides-equipment-data', 'forceguides-stats-config'),
        $equipment_detail_version, true);

    // Add dependency for equipment system on the detail view
    wp_script_add_data('forceguides-equipment-system', 'deps', array('forceguides-equipment-detail'));

    // Pet system data
    $pet_data_path = plugin_dir_path(__FILE__) . 'js/pet-system/pet-system-data.js';
    $pet_data_version = file_exists($pet_data_path) ? filemtime($pet_data_path) : time();

    wp_enqueue_script('forceguides-pet-data',
        plugin_dir_url(__FILE__) . 'js/pet-system/pet-system-data.js',
        array('jquery'),
        $pet_data_version, true);

    // Overlord mastery system data
    $overlord_data_path = plugin_dir_path(__FILE__) . 'js/overlord-mastery/overlord-mastery-data.js';
    $overlord_data_version = file_exists($overlord_data_path) ? filemtime($overlord_data_path) : time();

    wp_enqueue_script('forceguides-overlord-data',
        plugin_dir_url(__FILE__) . 'js/overlord-mastery/overlord-mastery-data.js',
        array('jquery'),
        $overlord_data_version, true);

    // Force Wing system data
    $force_wing_data_path = plugin_dir_path(__FILE__) . 'js/force-wing-system/force-wing-data.js';
    $force_wing_data_version = file_exists($force_wing_data_path) ? filemtime($force_wing_data_path) : time();

    wp_enqueue_script('forceguides-force-wing-data',
        plugin_dir_url(__FILE__) . 'js/force-wing-system/force-wing-data.js',
        array('jquery'),
        $force_wing_data_version, true);

    // Achievement system data
    $achievement_data_path = plugin_dir_path(__FILE__) . 'js/achievement-system/achievement-data.js';
    $achievement_data_version = file_exists($achievement_data_path) ? filemtime($achievement_data_path) : time();

    wp_enqueue_script('forceguides-achievement-data',
        plugin_dir_url(__FILE__) . 'js/achievement-system/achievement-data.js',
        array('jquery'),
        $achievement_data_version, true);

    // Pass data to JavaScript including paths for dynamic loading
    wp_localize_script('forceguides-build-planner-core', 'forceguidesPlannerData', array(
        'pluginUrl' => plugin_dir_url(__FILE__),
        'uploadsUrl' => wp_upload_dir()['baseurl'],
        'iconBase' => site_url('/wp-content/uploads/2025/03/'),
        'systemJsUrls' => $system_js_urls,
        'systemCssUrls' => $system_css_urls,
        'debug' => defined('WP_DEBUG') && WP_DEBUG,
        'buildPlannerPageUrl' => get_permalink($post->ID) // Current page URL where the build planner is embedded
    ));
}
add_action('wp_enqueue_scripts', 'forceguides_build_planner_enqueue_scripts');

// Add shortcode
function forceguides_build_planner_shortcode() {
    ob_start();
    include plugin_dir_path(__FILE__) . 'templates/build-planner.php';
    return ob_get_clean();
}

// Register shortcodes - put nipperlug first as it's likely causing the issue
add_shortcode('nipperlug_build_planner', 'forceguides_build_planner_shortcode');
add_shortcode('forceguides_build_planner', 'forceguides_build_planner_shortcode');